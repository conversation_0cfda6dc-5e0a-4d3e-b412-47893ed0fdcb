package com.ruoyi.web.controller.test;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.consultation.message.ConsultationNotificationMessage;
import com.ruoyi.consultation.service.SseConnectionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * SSE连接调试控制器
 * 用于调试和测试SSE连接相关功能
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@RestController
@RequestMapping("/test/sse")
@Slf4j
public class SseDebugController extends BaseController {

    @Autowired
    private SseConnectionService sseConnectionService;

    /**
     * 获取当前用户信息和连接状态
     */
    @GetMapping("/user-info")
    public AjaxResult getCurrentUserInfo() {
        try {
            Long userId = SecurityUtils.getUserId();
            String username = SecurityUtils.getUsername();
            boolean isConnected = sseConnectionService.isUserConnected(userId);
            int totalConnections = sseConnectionService.getConnectionCount();

            Map<String, Object> result = new HashMap<>();
            result.put("userId", userId);
            result.put("username", username);
            result.put("isConnected", isConnected);
            result.put("totalConnections", totalConnections);
            result.put("onlineUsers", sseConnectionService.getOnlineUsers());

            log.info("当前用户信息 - ID: {}, 用户名: {}, 是否连接: {}, 总连接数: {}", 
                    userId, username, isConnected, totalConnections);

            return success(result);

        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return error("获取用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 测试发送通知给当前用户
     */
    @PostMapping("/send-to-self")
    public AjaxResult sendNotificationToSelf(@RequestBody Map<String, Object> request) {
        try {
            Long userId = SecurityUtils.getUserId();
            String title = (String) request.getOrDefault("title", "测试通知");
            String content = (String) request.getOrDefault("content", "这是一条测试通知");

            log.info("准备向当前用户 {} 发送测试通知", userId);

            // 创建测试消息
            ConsultationNotificationMessage message = new ConsultationNotificationMessage();
            message.setType("TEST");
            message.setTitle(title);
            message.setContent(content);
            //message.setTimestamp(System.currentTimeMillis());

            // 发送通知
            boolean result = sseConnectionService.sendNotificationToUser(userId, message);

            Map<String, Object> response = new HashMap<>();
            response.put("success", result);
            response.put("userId", userId);
            response.put("message", result ? "通知发送成功" : "通知发送失败");

            return result ? success(response) : error("通知发送失败");

        } catch (Exception e) {
            log.error("发送测试通知失败", e);
            return error("发送测试通知失败: " + e.getMessage());
        }
    }

    /**
     * 测试发送通知给指定用户
     */
    @PostMapping("/send-to-user/{targetUserId}")
    public AjaxResult sendNotificationToUser(@PathVariable Long targetUserId, 
                                           @RequestBody Map<String, Object> request) {
        try {
            Long currentUserId = SecurityUtils.getUserId();
            String title = (String) request.getOrDefault("title", "测试通知");
            String content = (String) request.getOrDefault("content", "这是一条测试通知");

            log.info("用户 {} 准备向用户 {} 发送测试通知", currentUserId, targetUserId);

            // 检查目标用户是否在线
            boolean isTargetOnline = sseConnectionService.isUserConnected(targetUserId);
            log.info("目标用户 {} 在线状态: {}", targetUserId, isTargetOnline);

            // 创建测试消息
            ConsultationNotificationMessage message = new ConsultationNotificationMessage();
            message.setType("TEST");
            message.setTitle(title);
            message.setContent(content);
            //message.setTimestamp(System.currentTimeMillis());

            // 发送通知
            boolean result = sseConnectionService.sendNotificationToUser(targetUserId, message);

            Map<String, Object> response = new HashMap<>();
            response.put("success", result);
            response.put("currentUserId", currentUserId);
            response.put("targetUserId", targetUserId);
            response.put("isTargetOnline", isTargetOnline);
            response.put("message", result ? "通知发送成功" : "通知发送失败");

            return result ? success(response) : error("通知发送失败");

        } catch (Exception e) {
            log.error("发送测试通知失败", e);
            return error("发送测试通知失败: " + e.getMessage());
        }
    }

    /**
     * 获取连接统计信息
     */
    @GetMapping("/connection-stats")
    public AjaxResult getConnectionStats() {
        try {
            Map<String, Object> stats = sseConnectionService.getConnectionStatistics();
            log.info("连接统计信息: {}", stats);
            return success(stats);

        } catch (Exception e) {
            log.error("获取连接统计信息失败", e);
            return error("获取连接统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 强制清理指定用户的连接
     */
    @PostMapping("/remove-connection/{userId}")
    public AjaxResult removeUserConnection(@PathVariable Long userId) {
        try {
            Long currentUserId = SecurityUtils.getUserId();
            log.info("用户 {} 请求清理用户 {} 的连接", currentUserId, userId);

            boolean wasConnected = sseConnectionService.isUserConnected(userId);
            sseConnectionService.removeConnection(userId);

            Map<String, Object> result = new HashMap<>();
            result.put("userId", userId);
            result.put("wasConnected", wasConnected);
            result.put("message", "连接已清理");

            return success(result);

        } catch (Exception e) {
            log.error("清理用户连接失败", e);
            return error("清理用户连接失败: " + e.getMessage());
        }
    }

    /**
     * 广播测试通知给所有在线用户
     */
    @PostMapping("/broadcast")
    public AjaxResult broadcastNotification(@RequestBody Map<String, Object> request) {
        try {
            Long currentUserId = SecurityUtils.getUserId();
            String title = (String) request.getOrDefault("title", "广播测试通知");
            String content = (String) request.getOrDefault("content", "这是一条广播测试通知");

            log.info("用户 {} 准备广播测试通知", currentUserId);

            // 创建测试消息
            ConsultationNotificationMessage message = new ConsultationNotificationMessage();
            message.setType("BROADCAST_TEST");
            message.setTitle(title);
            message.setContent(content);
            //message.setTimestamp(System.currentTimeMillis());

            // 广播通知
            int successCount = sseConnectionService.broadcastNotification(message);

            Map<String, Object> result = new HashMap<>();
            result.put("successCount", successCount);
            result.put("totalConnections", sseConnectionService.getConnectionCount());
            result.put("message", "广播完成，成功发送给 " + successCount + " 个用户");

            return success(result);

        } catch (Exception e) {
            log.error("广播测试通知失败", e);
            return error("广播测试通知失败: " + e.getMessage());
        }
    }

    /**
     * 模拟用户上线（用于测试）
     */
    @PostMapping("/simulate-online/{userId}")
    public AjaxResult simulateUserOnline(@PathVariable Long userId) {
        try {
            log.info("模拟用户 {} 上线", userId);

            // 为指定用户创建SSE连接
            sseConnectionService.createConnection(userId);

            Map<String, Object> result = new HashMap<>();
            result.put("userId", userId);
            result.put("message", "用户连接已创建");
            result.put("isConnected", sseConnectionService.isUserConnected(userId));

            return success(result);

        } catch (Exception e) {
            log.error("模拟用户上线失败", e);
            return error("模拟用户上线失败: " + e.getMessage());
        }
    }
}
