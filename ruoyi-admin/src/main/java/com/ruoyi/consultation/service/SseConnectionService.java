package com.ruoyi.consultation.service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * SSE连接管理服务
 * 负责管理所有的SSE连接，提供发送通知的能力
 *
 * 简化的连接管理机制：
 * 1. 永不过期连接 - SseEmitter设置为永不过期，避免复杂的续期逻辑
 * 2. 服务端主导清理 - 通过定时任务检测客户端活跃状态并清理无效连接
 * 3. 心跳状态跟踪 - 记录客户端最后心跳响应时间
 * 4. 智能清理策略 - 基于心跳超时和连接状态进行清理
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Service
@Slf4j
public class SseConnectionService {

    // SSE连接管理
    private static final Map<Long, SseEmitter> sseConnections = new ConcurrentHashMap<>();

    // 连接状态管理 - 记录每个连接的最后活跃时间
    private static final Map<Long, Long> connectionLastActive = new ConcurrentHashMap<>();

    // 心跳响应管理 - 记录每个连接的最后心跳响应时间
    private static final Map<Long, Long> heartbeatLastResponse = new ConcurrentHashMap<>();

    // 连接清理标记 - 记录标记为待清理的连接及其原因
    private static final Map<Long, String> connectionCleanupMarks = new ConcurrentHashMap<>();

    // 配置常量
    private static final long SSE_TIMEOUT = 0L; // 永不过期
    private static final long HEARTBEAT_INTERVAL = 30 * 1000L; // 30秒心跳间隔
    private static final long HEARTBEAT_TIMEOUT = 90 * 1000L; // 90秒心跳超时（3次心跳）
    private static final long CONNECTION_CLEANUP_INTERVAL = 60 * 1000L; // 60秒清理间隔

    // 心跳调度器
    private final ScheduledExecutorService heartbeatScheduler = Executors.newScheduledThreadPool(2);

    // 心跳任务引用
    private ScheduledFuture<?> heartbeatTask;
    private ScheduledFuture<?> cleanupTask;

    /**
     * 初始化心跳机制
     */
    @PostConstruct
    public void initHeartbeat() {
        log.info("初始化SSE心跳机制");

        // 启动统一心跳任务
        heartbeatTask = heartbeatScheduler.scheduleWithFixedDelay(
            this::sendHeartbeatToAllConnections,
            HEARTBEAT_INTERVAL,
            HEARTBEAT_INTERVAL,
            TimeUnit.MILLISECONDS
        );

        // 启动连接清理任务
        cleanupTask = heartbeatScheduler.scheduleWithFixedDelay(
            this::cleanupInactiveConnections,
            CONNECTION_CLEANUP_INTERVAL,
            CONNECTION_CLEANUP_INTERVAL,
            TimeUnit.MILLISECONDS
        );

        log.info("SSE心跳机制初始化完成，心跳间隔: {}ms, 清理间隔: {}ms",
                HEARTBEAT_INTERVAL, CONNECTION_CLEANUP_INTERVAL);
    }

    /**
     * 销毁心跳机制
     */
    @PreDestroy
    public void destroyHeartbeat() {
        log.info("销毁SSE心跳机制");

        if (heartbeatTask != null && !heartbeatTask.isCancelled()) {
            heartbeatTask.cancel(true);
        }

        if (cleanupTask != null && !cleanupTask.isCancelled()) {
            cleanupTask.cancel(true);
        }

        closeAllConnections();

        log.info("SSE心跳机制销毁完成");
    }

    /**
     * 创建SSE连接
     * 设置为永不过期，通过服务端心跳任务管理连接生命周期
     *
     * @param userId 用户ID
     * @return SSE连接对象
     */
    public SseEmitter createConnection(Long userId) {
        log.info("为用户 {} 创建SSE连接（永不过期）", userId);

        // 创建永不过期的SSE连接
        SseEmitter emitter = new SseEmitter(SSE_TIMEOUT);

        // 处理连接完成事件
        emitter.onCompletion(() -> {
            log.info("用户 {} 的SSE连接已完成", userId);
            // 不立即移除连接，而是标记为待清理状态
            // 让心跳机制和清理任务来处理连接的最终清理
            markConnectionForCleanup(userId, "COMPLETED");
        });

        // 处理连接错误事件
        emitter.onError((throwable) -> {
            log.error("用户 {} 的SSE连接发生错误", userId, throwable);
            // 错误时立即移除连接
            removeConnection(userId);
        });

        // 关闭旧连接
        SseEmitter oldEmitter = sseConnections.put(userId, emitter);
        if (oldEmitter != null) {
            try {
                oldEmitter.complete();
                log.info("已关闭用户 {} 的旧SSE连接", userId);
            } catch (Exception e) {
                log.warn("关闭旧连接失败", e);
            }
        }

        // 初始化连接状态
        long currentTime = System.currentTimeMillis();
        connectionLastActive.put(userId, currentTime);
        heartbeatLastResponse.put(userId, currentTime);

        try {
            // 发送连接成功消息
            emitter.send(SseEmitter.event()
                    .name("connected")
                    .data("连接建立成功"));

            log.info("用户 {} 的SSE连接创建成功", userId);

        } catch (IOException e) {
            log.error("发送连接成功消息失败", e);
            removeConnection(userId);
            emitter.completeWithError(e);
        }

        return emitter;
    }

    /**
     * 移除连接
     *
     * @param userId 用户ID
     */
    public void removeConnection(Long userId) {
        SseEmitter emitter = sseConnections.remove(userId);
        if (emitter != null) {
            try {
                emitter.complete();
            } catch (Exception e) {
                log.warn("完成SSE连接时发生异常", e);
            }
        }

        // 清理连接状态
        connectionLastActive.remove(userId);
        heartbeatLastResponse.remove(userId);
        connectionCleanupMarks.remove(userId);

        log.debug("已移除用户 {} 的SSE连接及相关状态", userId);
    }

    /**
     * 处理心跳响应
     * 更新客户端活跃状态，用于服务端清理任务判断
     *
     * @param userId 用户ID
     */
    public void handleHeartbeatResponse(Long userId) {
        if (!sseConnections.containsKey(userId)) {
            log.debug("用户 {} 的SSE连接不存在，无法处理心跳响应", userId);
            return;
        }

        long currentTime = System.currentTimeMillis();

        // 更新心跳响应时间和活跃时间
        heartbeatLastResponse.put(userId, currentTime);
        connectionLastActive.put(userId, currentTime);

        // 如果连接被标记为待清理，现在收到心跳响应，说明连接恢复正常，移除清理标记
        if (connectionCleanupMarks.containsKey(userId)) {
            log.info("用户 {} 连接恢复正常，移除清理标记", userId);
            connectionCleanupMarks.remove(userId);
        }

        log.debug("收到用户 {} 的心跳响应，连接保持活跃", userId);
    }

    /**
     * 标记连接为待清理状态
     *
     * @param userId 用户ID
     * @param reason 清理原因
     */
    private void markConnectionForCleanup(Long userId, String reason) {
        connectionCleanupMarks.put(userId, reason);
        log.info("标记用户 {} 的连接为待清理状态，原因: {}", userId, reason);
    }

    /**
     * 向指定用户发送通知
     *
     * @param userId 用户ID
     * @param message 消息内容
     * @return 是否发送成功
     */
    public boolean sendNotificationToUser(Long userId, Object message) {
        // 详细的调试日志
        log.info("尝试向用户 {} 发送通知，当前连接数: {}", userId, sseConnections.size());
        log.info("当前在线用户列表: {}", sseConnections.keySet());

        SseEmitter emitter = sseConnections.get(userId);
        if (emitter == null) {
            log.warn("用户 {} 未建立SSE连接，无法发送通知。当前在线用户: {}", userId, sseConnections.keySet());
            return false;
        }

        try {
            emitter.send(SseEmitter.event()
                    .name("consultation-notification")
                    .data(message));
            log.info("向用户 {} 发送通知成功", userId);
            return true;
        } catch (IOException e) {
            log.error("向用户 {} 发送通知失败", userId, e);
            // 移除失效的连接
            removeConnection(userId);
            return false;
        }
    }

    /**
     * 广播通知给所有在线用户
     *
     * @param message 消息内容
     * @return 成功发送的用户数量
     */
    public int broadcastNotification(Object message) {
        log.info("广播通知给 {} 个在线用户", sseConnections.size());
        
        int successCount = 0;
        
        // 使用迭代器安全地遍历和修改Map
        var iterator = sseConnections.entrySet().iterator();
        while (iterator.hasNext()) {
            var entry = iterator.next();
            Long userId = entry.getKey();
            SseEmitter emitter = entry.getValue();
            
            try {
                emitter.send(SseEmitter.event()
                        .name("consultation-broadcast")
                        .data(message));
                successCount++;
            } catch (IOException e) {
                log.error("向用户 {} 广播通知失败", userId, e);
                try {
                    emitter.complete();
                } catch (Exception ex) {
                    log.warn("关闭失效连接失败", ex);
                }
                iterator.remove(); // 安全地移除失效连接
            }
        }
        
        log.info("广播通知完成，成功发送给 {} 个用户", successCount);
        return successCount;
    }

    /**
     * 向指定用户发送离线通知列表
     *
     * @param userId 用户ID
     * @param notifications 离线通知列表
     * @return 是否发送成功
     */
    public boolean sendOfflineNotifications(Long userId, Object notifications) {
        SseEmitter emitter = sseConnections.get(userId);
        if (emitter == null) {
            log.debug("用户 {} 未建立SSE连接，无法发送离线通知", userId);
            return false;
        }

        try {
            emitter.send(SseEmitter.event()
                    .name("offline-notifications")
                    .data(notifications));
            log.info("向用户 {} 发送离线通知成功", userId);
            return true;
        } catch (IOException e) {
            log.error("向用户 {} 发送离线通知失败", userId, e);
            removeConnection(userId);
            return false;
        }
    }

    /**
     * 检查用户是否在线（有活跃的SSE连接）
     *
     * @param userId 用户ID
     * @return 是否在线
     */
    public boolean isUserOnline(Long userId) {
        return sseConnections.containsKey(userId);
    }

    /**
     * 获取在线用户数量
     *
     * @return 在线用户数量
     */
    public int getOnlineUserCount() {
        return sseConnections.size();
    }

    /**
     * 获取所有在线用户ID
     *
     * @return 在线用户ID集合
     */
    public java.util.Set<Long> getOnlineUserIds() {
        return sseConnections.keySet();
    }

    /**
     * 向所有连接发送心跳
     */
    private void sendHeartbeatToAllConnections() {
        if (sseConnections.isEmpty()) {
            return;
        }

        log.debug("开始向 {} 个连接发送心跳", sseConnections.size());
        long currentTime = System.currentTimeMillis();

        // 使用迭代器安全地遍历和修改Map
        var iterator = sseConnections.entrySet().iterator();
        int successCount = 0;
        int failureCount = 0;

        while (iterator.hasNext()) {
            var entry = iterator.next();
            Long userId = entry.getKey();
            SseEmitter emitter = entry.getValue();

            try {
                // 发送心跳消息
                emitter.send(SseEmitter.event()
                        .name("heartbeat")
                        .data(createHeartbeatData(currentTime)));

                // 更新连接活跃时间
                connectionLastActive.put(userId, currentTime);
                successCount++;

            } catch (IOException e) {
                log.warn("向用户 {} 发送心跳失败，标记为待清理", userId);
                failureCount++;
                // 不在这里直接移除连接，交给清理任务处理
            }
        }

        if (successCount > 0 || failureCount > 0) {
            log.debug("心跳发送完成，成功: {}, 失败: {}", successCount, failureCount);
        }
    }

    /**
     * 创建心跳数据
     */
    private Map<String, Object> createHeartbeatData(long timestamp) {
        Map<String, Object> heartbeatData = new java.util.HashMap<>();
        heartbeatData.put("timestamp", timestamp);
        heartbeatData.put("type", "heartbeat");
        heartbeatData.put("serverTime", new java.util.Date(timestamp));
        return heartbeatData;
    }

    /**
     * 清理无效连接
     */
    private void cleanupInactiveConnections() {
        if (sseConnections.isEmpty()) {
            return;
        }

        long currentTime = System.currentTimeMillis();
        var iterator = sseConnections.entrySet().iterator();
        int cleanedCount = 0;

        while (iterator.hasNext()) {
            var entry = iterator.next();
            Long userId = entry.getKey();
            SseEmitter emitter = entry.getValue();

            boolean shouldCleanup = false;
            String cleanupReason = "";

            // 检查是否被标记为待清理
            String markReason = connectionCleanupMarks.get(userId);
            if (markReason != null) {
                // 给被标记的连接一个宽限期（30秒），允许重连
                Long lastActive = connectionLastActive.get(userId);
                if (lastActive != null && (currentTime - lastActive) > 30000) {
                    shouldCleanup = true;
                    cleanupReason = "标记清理: " + markReason;
                }
            }

            // 检查心跳响应超时
            if (!shouldCleanup) {
                Long lastResponse = heartbeatLastResponse.get(userId);
                if (lastResponse == null || (currentTime - lastResponse) > HEARTBEAT_TIMEOUT) {
                    shouldCleanup = true;
                    cleanupReason = "心跳超时";
                }
            }

            if (shouldCleanup) {
                log.warn("清理用户 {} 的连接，原因: {}。最后响应时间: {}",
                        userId, cleanupReason,
                        heartbeatLastResponse.get(userId) != null ?
                            new java.util.Date(heartbeatLastResponse.get(userId)) : "无");

                try {
                    emitter.send(SseEmitter.event()
                            .name("connection-cleanup")
                            .data("连接被清理: " + cleanupReason));
                    emitter.complete();
                } catch (Exception e) {
                    log.debug("清理连接时发送通知失败", e);
                }

                iterator.remove();
                connectionLastActive.remove(userId);
                heartbeatLastResponse.remove(userId);
                connectionCleanupMarks.remove(userId);
                cleanedCount++;
            }
        }

        if (cleanedCount > 0) {
            log.info("清理了 {} 个无效连接，当前活跃连接数: {}", cleanedCount, sseConnections.size());
        }
    }

    /**
     * 关闭所有连接（应用关闭时调用）
     */
    public void closeAllConnections() {
        log.info("关闭所有SSE连接，当前连接数: {}", sseConnections.size());
        
        sseConnections.forEach((userId, emitter) -> {
            try {
                emitter.send(SseEmitter.event()
                        .name("server-shutdown")
                        .data("服务器即将关闭"));
                emitter.complete();
            } catch (Exception e) {
                log.warn("关闭用户 {} 的连接时发生异常", userId, e);
            }
        });
        
        sseConnections.clear();
        heartbeatScheduler.shutdown();
        
        log.info("所有SSE连接已关闭");
    }

    /**
     * 获取连接统计信息
     *
     * @return 统计信息
     */
    public Map<String, Object> getConnectionStatistics() {
        long currentTime = System.currentTimeMillis();
        Map<String, Object> stats = new java.util.HashMap<>();

        // 基本统计
        stats.put("totalConnections", sseConnections.size());
        stats.put("onlineUsers", sseConnections.keySet());
        stats.put("serverTime", currentTime);

        // 心跳统计
        int activeConnections = 0;
        int timeoutConnections = 0;

        for (Long userId : sseConnections.keySet()) {
            Long lastResponse = heartbeatLastResponse.get(userId);
            if (lastResponse != null && (currentTime - lastResponse) <= HEARTBEAT_TIMEOUT) {
                activeConnections++;
            } else {
                timeoutConnections++;
            }
        }

        stats.put("activeConnections", activeConnections);
        stats.put("timeoutConnections", timeoutConnections);
        stats.put("heartbeatInterval", HEARTBEAT_INTERVAL);
        stats.put("heartbeatTimeout", HEARTBEAT_TIMEOUT);

        return stats;
    }

    /**
     * 获取连接健康状态
     *
     * @return 健康状态信息
     */
    public Map<String, Object> getConnectionHealth() {
        long currentTime = System.currentTimeMillis();
        Map<String, Object> health = new java.util.HashMap<>();

        Map<Long, Map<String, Object>> userHealth = new java.util.HashMap<>();

        for (Long userId : sseConnections.keySet()) {
            Map<String, Object> userInfo = new java.util.HashMap<>();

            Long lastActive = connectionLastActive.get(userId);
            Long lastResponse = heartbeatLastResponse.get(userId);

            userInfo.put("lastActive", lastActive);
            userInfo.put("lastResponse", lastResponse);
            userInfo.put("isActive", lastResponse != null && (currentTime - lastResponse) <= HEARTBEAT_TIMEOUT);
            userInfo.put("timeSinceLastResponse", lastResponse != null ? currentTime - lastResponse : null);

            userHealth.put(userId, userInfo);
        }

        health.put("users", userHealth);
        health.put("checkTime", currentTime);

        return health;
    }

    /**
     * 向指定用户发送自定义事件
     *
     * @param userId 用户ID
     * @param eventName 事件名称
     * @param data 数据
     * @return 是否发送成功
     */
    public boolean sendCustomEvent(Long userId, String eventName, Object data) {
        SseEmitter emitter = sseConnections.get(userId);
        if (emitter == null) {
            log.debug("用户 {} 未建立SSE连接", userId);
            return false;
        }

        try {
            emitter.send(SseEmitter.event()
                    .name(eventName)
                    .data(data));
            log.debug("向用户 {} 发送自定义事件 {} 成功", userId, eventName);
            return true;
        } catch (IOException e) {
            log.error("向用户 {} 发送自定义事件 {} 失败", userId, eventName, e);
            removeConnection(userId);
            return false;
        }
    }

    /**
     * 批量发送通知给指定用户列表
     *
     * @param userIds 用户ID列表
     * @param message 消息内容
     * @return 成功发送的用户数量
     */
    public int sendNotificationToUsers(java.util.List<Long> userIds, Object message) {
        int successCount = 0;
        
        for (Long userId : userIds) {
            if (sendNotificationToUser(userId, message)) {
                successCount++;
            }
        }
        
        log.info("批量发送通知完成，目标用户: {}, 成功发送: {}", userIds.size(), successCount);
        return successCount;
    }

    /**
     * 检查用户是否连接
     */
    public boolean isUserConnected(Long userId) {
        return sseConnections.containsKey(userId);
    }

    /**
     * 获取连接数量
     */
    public int getConnectionCount() {
        return sseConnections.size();
    }

    /**
     * 获取在线用户列表
     */
    public Set<Long> getOnlineUsers() {
        return new HashSet<>(sseConnections.keySet());
    }
}
