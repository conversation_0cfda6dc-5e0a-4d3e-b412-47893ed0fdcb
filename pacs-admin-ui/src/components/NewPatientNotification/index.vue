<template>
  <div class="new-patient-notification">
    <!-- 音频元素 -->
    <audio
      ref="notificationAudio"
      :src="soundFile"
      preload="auto"
      @error="handleAudioError"
    ></audio>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { ElNotification, ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { checkNewPatients, getNotificationConfig } from '@/api/diagnosis/diagnosis'

// 组件属性
const props = defineProps({
  // 是否启用自动检查
  autoCheck: {
    type: Boolean,
    default: true
  },
  // 检查间隔（毫秒）
  checkInterval: {
    type: Number,
    default: 30000
  }
})

// 响应式数据
const router = useRouter()
const soundFile = ref('/admin/sounds/new-patient-notification.mp3')
const notificationAudio = ref(null)

// 配置数据
const config = ref({
  enabled: false,
  soundEnabled: true,
  popupEnabled: true,
  checkInterval: 30000,
  soundFile: '/admin/sounds/new-patient-notification.mp3',
  volume: 0.8,
  title: '新影像待诊断',
  message: '您有{count}个新影像待诊断，请注意查看',
  maxCount: 3
})

// 状态数据
const checkTimer = ref(null)
const lastNotificationTime = ref(0)
const notificationCount = ref(0)

// 计算属性
const effectiveCheckInterval = computed(() => {
  return props.checkInterval || config.value.checkInterval || 30000
})

// 初始化
onMounted(async () => {
  await loadConfig()
  if (props.autoCheck && config.value.enabled) {
    startAutoCheck()
  }
})

// 清理
onUnmounted(() => {
  stopAutoCheck()
})

// 加载配置
const loadConfig = async () => {
  try {
    const response = await getNotificationConfig()
    if (response.code === 200) {
      Object.assign(config.value, response.data)

      // 更新相关配置
      soundFile.value = config.value.soundFile || '/admin/sounds/new-patient-notification.mp3'
      
      // 设置音频音量
      if (notificationAudio.value) {
        notificationAudio.value.volume = parseFloat(config.value.volume) || 0.8
      }
    }
  } catch (error) {
    console.error('加载提醒配置失败:', error)
  }
}

// 开始自动检查
const startAutoCheck = () => {
  if (checkTimer.value) {
    clearInterval(checkTimer.value)
  }
  
  checkTimer.value = setInterval(() => {
    checkForNewPatients()
  }, effectiveCheckInterval.value)
  
  // 立即执行一次检查
  checkForNewPatients()
}

// 停止自动检查
const stopAutoCheck = () => {
  if (checkTimer.value) {
    clearInterval(checkTimer.value)
    checkTimer.value = null
  }
}

// 检查新患者
const checkForNewPatients = async () => {
  if (!config.value.enabled) {
    return
  }
  
  try {
    const response = await checkNewPatients()
    if (response.code === 200 && response.data.hasNew) {
      const { newCount, newPatients } = response.data
      
      // 检查是否需要提醒（避免重复提醒）
      const now = Date.now()
      if (now - lastNotificationTime.value < 60000) { // 1分钟内不重复提醒
        return
      }
      
      // 检查最大提醒次数
      if (notificationCount.value >= config.value.maxCount) {
        return
      }
      
      // 播放提示音
      if (config.value.soundEnabled) {
        playNotificationSound()
      }
      
      // 显示弹窗
      if (config.value.popupEnabled) {
        showNotificationPopup(newCount, newPatients)
      }
      
      // 更新状态
      lastNotificationTime.value = now
      notificationCount.value++
      
      console.log(`检测到 ${newCount} 个新患者，已发送提醒`)
    }
  } catch (error) {
    console.error('检查新患者失败:', error)
  }
}

// 播放提示音
const playNotificationSound = () => {
  if (notificationAudio.value) {
    try {
      notificationAudio.value.currentTime = 0
      notificationAudio.value.play().catch(error => {
        console.warn('播放提示音失败:', error)
      })
    } catch (error) {
      console.warn('播放提示音异常:', error)
    }
  }
}

// 显示通知弹窗
const showNotificationPopup = (count, patients) => {
  // 生成提醒消息
  let message = config.value.message.replace('{count}', count)

  // 如果有患者详情，添加到消息中
  if (patients && patients.length > 0) {
    const patientNames = patients.slice(0, 3).map(p => p.patientName || '未知患者').join('、')
    message += `\n患者：${patientNames}`
    if (patients.length > 3) {
      message += ` 等${patients.length}人`
    }
  }

  // 使用Element Plus的Notification API
  const notification = ElNotification({
    title: config.value.title || '新影像待诊断',
    message: message,
    type: 'warning',
    position: config.value.position || 'bottom-right',
    duration: parseInt(config.value.duration) || 5000,
    showClose: true,
    dangerouslyUseHTMLString: true,
    customClass: 'new-patient-notification-popup',
    onClick: () => {
      goToDiagnosisList()
      notification.close()
    }
  })
}

// 跳转到诊断列表
const goToDiagnosisList = () => {
  router.push('/diagnosis/diagnosis')
}

// 处理音频错误
const handleAudioError = (error) => {
  console.warn('音频加载失败:', error)
}

// 手动检查（供外部调用）
const manualCheck = () => {
  checkForNewPatients()
}

// 重置提醒计数
const resetNotificationCount = () => {
  notificationCount.value = 0
  lastNotificationTime.value = 0
}

// 暴露方法给父组件
defineExpose({
  startAutoCheck,
  stopAutoCheck,
  manualCheck,
  resetNotificationCount,
  loadConfig
})
</script>

<style scoped>
.new-patient-notification {
  position: fixed;
  z-index: 9999;
}

.notification-content {
  padding: 10px 0;
}

.notification-text {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-line;
}

.notification-text i {
  margin-right: 8px;
  color: #e6a23c;
  font-size: 16px;
}

.notification-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* 自定义通知位置样式 */
.el-notification {
  min-width: 300px;
  max-width: 400px;
}

.el-notification.right {
  right: 16px;
}

.el-notification.left {
  left: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-notification {
    min-width: 280px;
    max-width: 320px;
    right: 8px !important;
    left: 8px !important;
  }
  
  .notification-actions {
    flex-direction: column;
  }
  
  .notification-actions .el-button {
    width: 100%;
  }
}
</style>
