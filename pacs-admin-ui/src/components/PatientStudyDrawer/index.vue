<template>
  <div>
    <el-drawer
      v-model="drawerVisible"
      title="患者检查信息"
      direction="rtl"
      size="30%"
      :destroy-on-close="false"
      :before-close="handleClose"
    >
      <div class="patient-study-drawer" v-loading="loading">
        <template v-if="studyData">
          <!-- 直接显示的关键信息 -->
          <div class="info-section">
            <div class="info-item">
              <span class="info-label">临床诊断:</span>
              <span class="info-value">{{ studyData.clinicalDiagnosis || '无' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">临床症状:</span>
              <span class="info-value">{{ studyData.clinicalSymptom || '无' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">病史:</span>
              <span class="info-value">{{ studyData.medicalHistory || '无' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">就诊卡号:</span>
              <span class="info-value">{{ studyData.medicalCardNo || '无' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">身份证号:</span>
              <span class="info-value">{{ studyData.idNo || '无' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">检查完成:</span>
              <span class="info-value">{{ formatTime(studyData.checkFinishTime) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">检查医生:</span>
              <span class="info-value">{{ studyData.examDoctorName || '无' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">检查科室:</span>
              <span class="info-value">{{ studyData.examDepartment || '无' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">检查部位:</span>
              <span class="info-value">{{ studyData.organ || '无' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">患者来源:</span>
              <span class="info-value">{{ translatePatientFrom(studyData.patientFrom) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">性别:</span>
              <span class="info-value">{{ translateGender(studyData.patientSex) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">年龄:</span>
              <span class="info-value">{{ calculateAge(studyData.patientBirthday) }}</span>
            </div>
          </div>

          <!-- 其他信息折叠面板 -->
          <el-divider content-position="center">更多信息</el-divider>
          <el-collapse>
            <el-collapse-item title="患者基本信息" name="1">
              <div class="info-section">
                <div class="info-item">
                  <span class="info-label">患者姓名:</span>
                  <span class="info-value">{{ studyData.patientName || '无' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">原始患者ID:</span>
                  <span class="info-value">{{ studyData.originalPatientId || '无' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">出生日期:</span>
                  <span class="info-value">{{ formatTime(studyData.patientBirthday, '{y}-{m}-{d}') }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">手机号:</span>
                  <span class="info-value">{{ studyData.mobile || '无' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">社保卡号:</span>
                  <span class="info-value">{{ studyData.socialSecurityCardNo || '无' }}</span>
                </div>
              </div>
            </el-collapse-item>
            <el-collapse-item title="检查信息" name="2">
              <div class="info-section">
                <div class="info-item">
                  <span class="info-label">检查编码:</span>
                  <span class="info-value">{{ studyData.examCode || '无' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">原始检查编码:</span>
                  <span class="info-value">{{ studyData.originalExamCode || '无' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">检查类型:</span>
                  <span class="info-value">{{ studyData.modality || '无' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">检查项目:</span>
                  <span class="info-value">{{ studyData.examItem || '无' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">设备名称:</span>
                  <span class="info-value">{{ studyData.device || '无' }}</span>
                </div>
              </div>
            </el-collapse-item>
            <el-collapse-item title="医院信息" name="3">
              <div class="info-section">
                <div class="info-item">
                  <span class="info-label">医院ID:</span>
                  <span class="info-value">{{ studyData.hospitalId || '无' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">医院名称:</span>
                  <span class="info-value">{{ studyData.hospitalName || '无' }}</span>
                </div>
              </div>
            </el-collapse-item>
            <el-collapse-item title="时间信息" name="4">
              <div class="info-section">
                <div class="info-item">
                  <span class="info-label">登记时间:</span>
                  <span class="info-value">{{ formatTime(studyData.registerTime) }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">预约时间:</span>
                  <span class="info-value">{{ formatTime(studyData.reserveTime) }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">预约到达时间:</span>
                  <span class="info-value">{{ formatTime(studyData.reserveArrivalTime) }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">同步时间:</span>
                  <span class="info-value">{{ formatTime(studyData.syncTime) }}</span>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </template>
        <el-empty v-else description="暂无患者检查信息"></el-empty>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';
import { parseTime } from '@/utils/ruoyi';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  studyData: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emits = defineEmits(['update:visible', 'close']);

const drawerVisible = ref(props.visible);

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  drawerVisible.value = newVal;
});

// 监听drawerVisible变化，同步更新父组件的visible属性
watch(() => drawerVisible.value, (newVal) => {
  emits('update:visible', newVal);
});

// 格式化时间
const formatTime = (time, format = '{y}-{m}-{d} {h}:{i}:{s}') => {
  if (!time) return '无';
  return parseTime(time, format);
};

// 翻译患者来源
const translatePatientFrom = (value) => {
  if (!value) return '无';
  const patientFromMap = {
    'InPatient': '住院患者',
    'OutPatient': '门诊患者',
    'Emergency': '急诊患者',
    'Physical': '体检患者',
    'Examination': '体检患者',
    'Other': '其他'
  };
  return patientFromMap[value] || value;
};

// 翻译性别
const translateGender = (value) => {
  if (!value) return '无';
  const genderMap = {
    'Male': '男',
    'Female': '女',
    'Other': '其他',
    'Unknown': '未知'
  };
  return genderMap[value] || value;
};

// 计算年龄
const calculateAge = (birthDate) => {
  if (!birthDate) return '无';

  try {
    // 将出生日期转换为Date对象
    let birth;
    if (typeof birthDate === 'string') {
      // 如果是字符串，尝试解析
      // 先尝试直接解析
      birth = new Date(birthDate);

      // 如果日期无效，尝试替换连字符
      if (isNaN(birth.getTime())) {
        birth = new Date(birthDate.replace(/-/g, '/'));
      }

      // 如果还是无效，尝试只使用年月日部分
      if (isNaN(birth.getTime()) && birthDate.length >= 10) {
        birth = new Date(birthDate.substring(0, 10).replace(/-/g, '/'));
      }
    } else if (birthDate instanceof Date) {
      // 如果已经是Date对象
      birth = new Date(birthDate.getFullYear(), birthDate.getMonth(), birthDate.getDate());
    } else {
      console.warn('无法识别的出生日期格式:', birthDate);
      return '无';
    }

    // 检查日期是否有效
    if (isNaN(birth.getTime())) {
      console.warn('无效的出生日期:', birthDate);
      return '无';
    }

    // 计算年龄
    const today = new Date();
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    // 如果当前月份小于出生月份，或者月份相同但当前日期小于出生日期，则年龄减1
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }

    return age + '岁';
  } catch (error) {
    console.error('计算年龄时出错:', error, birthDate);
    return '无';
  }
};

// 关闭抽屉
const handleClose = () => {
  drawerVisible.value = false;
  emits('close');
};
</script>

<style lang="scss" scoped>
.patient-study-drawer {
  padding: 0 10px;

  .info-section {
    margin-bottom: 15px;
  }

  .info-item {
    display: flex;
    margin-bottom: 10px;
    line-height: 1.5;

    .info-label {
      min-width: 100px;
      font-weight: bold;
      color: #606266;
    }

    .info-value {
      flex: 1;
      word-break: break-all;
    }
  }

  .el-divider {
    margin: 15px 0;
  }

  .el-collapse {
    border: none;

    :deep(.el-collapse-item__header) {
      font-weight: bold;
      color: #303133;
    }

    :deep(.el-collapse-item__content) {
      padding: 10px 0;
    }
  }
}
</style>
