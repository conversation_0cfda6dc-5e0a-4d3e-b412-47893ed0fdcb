<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="header-title">检查记录详情</span>
          <div class="header-actions">
            <el-button type="primary" @click="openPatientInfoDrawer">
              <el-icon><InfoFilled /></el-icon> 查看患者信息
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 其他内容 -->
      <div>
        <!-- 这里是您的主要内容 -->
      </div>
    </el-card>
    
    <!-- 患者检查信息抽屉 -->
    <patient-study-drawer
      v-model:visible="drawerVisible"
      :study-data="studyData"
      :loading="loading"
      @close="handleDrawerClose"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { InfoFilled } from '@element-plus/icons-vue';
import PatientStudyDrawer from '@/components/PatientStudyDrawer/index.vue';
import { getStudyById } from '@/api/pacs/study'; // 假设有这个API方法

// 抽屉可见性
const drawerVisible = ref(false);
// 加载状态
const loading = ref(false);
// 患者检查数据
const studyData = ref(null);

// 打开患者信息抽屉
const openPatientInfoDrawer = async () => {
  drawerVisible.value = true;
  await loadStudyData();
};

// 加载患者检查数据
const loadStudyData = async () => {
  // 假设当前页面有一个studyId
  const studyId = 'your-study-id'; // 这里应该是您实际的studyId
  
  if (!studyId) {
    ElMessage.warning('未找到检查记录ID');
    return;
  }
  
  loading.value = true;
  try {
    // 调用API获取患者检查数据
    const res = await getStudyById(studyId);
    if (res.code === 200) {
      studyData.value = res.data;
    } else {
      ElMessage.error(res.msg || '获取检查记录详情失败');
    }
  } catch (error) {
    console.error('获取检查记录详情出错', error);
    ElMessage.error('系统错误，请联系管理员');
  } finally {
    loading.value = false;
  }
};

// 处理抽屉关闭事件
const handleDrawerClose = () => {
  console.log('抽屉已关闭');
  // 可以在这里执行一些清理操作
};

// 页面加载时可以预加载数据
onMounted(() => {
  // 如果需要预加载数据，可以在这里调用loadStudyData()
});
</script>

<style lang="scss" scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  font-size: 16px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 10px;
}
</style>
