<template>
  <div class="medical-table-wrapper">
    <el-table
      v-bind="$attrs"
      :class="tableClass"
      :header-cell-style="headerCellStyle"
      :cell-style="cellStyle"
      :row-style="rowStyle"
      @row-click="handleRowClick"
      @selection-change="handleSelectionChange"
    >
      <slot />
    </el-table>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { MEDICAL_CLASSES } from '@/utils/medical-theme'

// 定义props
const props = defineProps({
  // 是否启用医疗主题
  medicalTheme: {
    type: Boolean,
    default: true
  },
  // 自定义表格类名
  customClass: {
    type: String,
    default: ''
  },
  // 是否显示斑马纹
  stripe: {
    type: Boolean,
    default: true
  }
})

// 定义emits
const emit = defineEmits(['row-click', 'selection-change'])

// 计算表格类名
const tableClass = computed(() => {
  const classes = []
  
  if (props.medicalTheme) {
    classes.push(MEDICAL_CLASSES.TABLE)
  }
  
  if (props.customClass) {
    classes.push(props.customClass)
  }
  
  return classes.join(' ')
})

// 表头单元格样式
const headerCellStyle = computed(() => ({
  background: 'var(--table-header-bg, #f0f8ff)',
  color: 'var(--table-header-text, #0066cc)',
  fontWeight: '600',
  fontSize: '14px',
  height: '48px',
  borderBottom: '2px solid var(--medical-primary, #0066cc)'
}))

// 单元格样式
const cellStyle = computed(() => ({
  borderBottom: '1px solid #f0f0f0',
  padding: '12px 0'
}))

// 行样式
const rowStyle = computed(() => ({
  transition: 'all 0.2s ease'
}))

// 处理行点击事件
const handleRowClick = (row, column, event) => {
  emit('row-click', row, column, event)
}

// 处理选择变化事件
const handleSelectionChange = (selection) => {
  emit('selection-change', selection)
}
</script>

<style lang="scss" scoped>
.medical-table-wrapper {
  .medical-table {
    border: 1px solid var(--table-border, #e6f7ff);
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 102, 204, 0.08);

    :deep(.el-table__header-wrapper) {
      .el-table__header {
        th {
          position: relative;

          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--medical-primary, #0066cc) 0%, var(--medical-primary-light, #4d94ff) 100%);
          }

          .cell {
            padding: 0 16px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }

    :deep(.el-table__body-wrapper) {
      .el-table__body {
        tr {
          &:hover {
            background-color: var(--table-hover-bg, #f0f8ff) !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 102, 204, 0.1);
          }

          td {
            .cell {
              padding: 0 16px;
              line-height: 1.5;
              font-size: 13px;
            }
          }

          &:nth-child(even) {
            background-color: #fafcff;
          }
        }
      }
    }
  }
}
</style>
