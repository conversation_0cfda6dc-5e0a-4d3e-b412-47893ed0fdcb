<template>
  <el-card
    :class="cardClass"
    :shadow="shadow"
    :body-style="bodyStyle"
    v-bind="$attrs"
  >
    <template #header v-if="$slots.header || title">
      <div class="medical-card-header">
        <slot name="header">
          <div class="card-header-title">
            <el-icon v-if="icon" class="header-icon">
              <component :is="icon" />
            </el-icon>
            {{ title }}
          </div>
        </slot>
      </div>
    </template>
    
    <slot />
  </el-card>
</template>

<script setup>
import { computed } from 'vue'
import { MEDICAL_CLASSES } from '@/utils/medical-theme'

// 定义props
const props = defineProps({
  // 是否启用医疗主题
  medicalTheme: {
    type: Boolean,
    default: true
  },
  // 卡片标题
  title: {
    type: String,
    default: ''
  },
  // 标题图标
  icon: {
    type: [String, Object],
    default: null
  },
  // 阴影效果
  shadow: {
    type: String,
    default: 'hover'
  },
  // 自定义类名
  customClass: {
    type: String,
    default: ''
  },
  // 卡片类型
  type: {
    type: String,
    default: 'default', // default, stats, info, warning, danger
    validator: (value) => ['default', 'stats', 'info', 'warning', 'danger'].includes(value)
  }
})

// 计算卡片类名
const cardClass = computed(() => {
  const classes = []
  
  if (props.medicalTheme) {
    if (props.type === 'stats') {
      classes.push(MEDICAL_CLASSES.STATS_CARD)
    } else {
      classes.push(MEDICAL_CLASSES.CARD)
    }
  }
  
  if (props.customClass) {
    classes.push(props.customClass)
  }
  
  if (props.type !== 'default') {
    classes.push(`medical-card--${props.type}`)
  }
  
  return classes.join(' ')
})

// 计算body样式
const bodyStyle = computed(() => ({
  padding: '20px'
}))
</script>

<style lang="scss" scoped>
.medical-card {
  border: 1px solid #e6f7ff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 102, 204, 0.08);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 102, 204, 0.15);
    transform: translateY(-2px);
  }

  :deep(.el-card__header) {
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%);
    border-bottom: 2px solid var(--medical-primary, #0066cc);
    padding: 16px 20px;

    .medical-card-header {
      .card-header-title {
        color: var(--medical-primary, #0066cc);
        font-weight: 600;
        font-size: 16px;
        display: flex;
        align-items: center;
        gap: 8px;

        .header-icon {
          font-size: 18px;
        }
      }
    }
  }

  :deep(.el-card__body) {
    padding: 20px;
  }

  // 不同类型的卡片样式
  &.medical-card--info {
    border-color: var(--medical-primary, #0066cc);
    
    :deep(.el-card__header) {
      background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
      border-bottom-color: var(--medical-primary, #0066cc);
    }
  }

  &.medical-card--warning {
    border-color: var(--medical-warning, #fa8c16);
    
    :deep(.el-card__header) {
      background: linear-gradient(135deg, #fff7e6 0%, #fff2d9 100%);
      border-bottom-color: var(--medical-warning, #fa8c16);
      
      .card-header-title {
        color: var(--medical-warning, #fa8c16);
      }
    }
  }

  &.medical-card--danger {
    border-color: var(--medical-danger, #ff4d4f);
    
    :deep(.el-card__header) {
      background: linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%);
      border-bottom-color: var(--medical-danger, #ff4d4f);
      
      .card-header-title {
        color: var(--medical-danger, #ff4d4f);
      }
    }
  }
}

.medical-stats-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fcff 100%);
  border: 1px solid #e6f7ff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 102, 204, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--medical-primary, #0066cc) 0%, var(--medical-primary-light, #4d94ff) 100%);
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 102, 204, 0.15);
  }

  :deep(.el-card__header) {
    background: transparent;
    border: none;
    padding: 0;
    margin-bottom: 12px;
  }

  :deep(.el-card__body) {
    padding: 0;
  }
}
</style>
