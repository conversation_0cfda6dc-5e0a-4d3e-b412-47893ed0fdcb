<template>
  <div class="ai-analysis-panel">
    <div class="panel-header">
      <h4>AI 影像分析</h4>
      <div class="action-buttons">
        <el-button 
          type="primary" 
          size="small" 
          @click="requestAnalysis" 
          :loading="analysisLoading"
          :disabled="!studyInstanceUid || analysisLoading"
        >
          <el-icon><cpu /></el-icon>
          开始AI分析
        </el-button>
        <el-button 
          type="success" 
          size="small" 
          @click="applyToReport" 
          :disabled="!currentAnalysis || !diagnosisId"
          v-if="currentAnalysis"
        >
          <el-icon><document-add /></el-icon>
          应用到报告
        </el-button>
      </div>
    </div>
    
    <div class="panel-content">
      <!-- 加载状态 -->
      <div v-if="analysisLoading" class="analysis-loading">
        <el-skeleton :rows="3" animated />
        <div class="loading-text">
          <el-icon><loading /></el-icon>
          AI正在分析影像数据，请稍候...
        </div>
      </div>
      
      <!-- 分析结果 -->
      <div v-else-if="currentAnalysis" class="analysis-result">
        <div class="result-header">
          <span class="result-title">分析结果</span>
          <span class="result-time">{{ formatTime(currentAnalysis.createTime) }}</span>
        </div>
        
        <!-- 分割结果展示 -->
        <div v-if="currentAnalysis.segmentationResults && currentAnalysis.segmentationResults.length > 0" class="segmentation-results">
          <h5>器官分割结果</h5>
          <el-row :gutter="10">
            <el-col :span="8" v-for="(item, index) in currentAnalysis.segmentationResults" :key="index">
              <el-card class="segmentation-card" shadow="hover">
                <template #header>
                  <div class="card-header">
                    <span>{{ item.organName }}</span>
                    <el-tag size="small" :type="getTagType(item.confidence)">
                      置信度: {{ (item.confidence * 100).toFixed(1) }}%
                    </el-tag>
                  </div>
                </template>
                <div class="card-content">
                  <div v-if="item.thumbnailUrl" class="thumbnail">
                    <el-image 
                      :src="item.thumbnailUrl" 
                      fit="contain"
                      :preview-src-list="[item.imageUrl || item.thumbnailUrl]"
                    />
                  </div>
                  <div class="metrics">
                    <div v-if="item.volume" class="metric-item">
                      <span class="metric-label">体积:</span>
                      <span class="metric-value">{{ item.volume }} cm³</span>
                    </div>
                    <div v-if="item.dimensions" class="metric-item">
                      <span class="metric-label">尺寸:</span>
                      <span class="metric-value">{{ item.dimensions }}</span>
                    </div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
        
        <!-- 病变检测结果 -->
        <div v-if="currentAnalysis.lesionDetections && currentAnalysis.lesionDetections.length > 0" class="lesion-detections">
          <h5>病变检测结果</h5>
          <el-table :data="currentAnalysis.lesionDetections" style="width: 100%">
            <el-table-column prop="type" label="类型" width="120" />
            <el-table-column prop="location" label="位置" width="150" />
            <el-table-column prop="size" label="大小" width="120" />
            <el-table-column prop="confidence" label="置信度">
              <template #default="scope">
                <el-progress 
                  :percentage="scope.row.confidence * 100" 
                  :color="getProgressColor(scope.row.confidence)"
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="scope">
                <el-button 
                  type="primary" 
                  link 
                  size="small" 
                  @click="viewLesionDetail(scope.row)"
                >
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        
        <!-- AI诊断建议 -->
        <div v-if="currentAnalysis.diagnosticSuggestions" class="diagnostic-suggestions">
          <h5>AI诊断建议</h5>
          <div class="suggestion-content">
            <el-alert
              type="info"
              :closable="false"
              show-icon
            >
              <template #title>
                <div class="suggestion-disclaimer">
                  以下内容仅供参考，最终诊断请以医生判断为准
                </div>
              </template>
            </el-alert>
            <div class="suggestion-text">
              {{ currentAnalysis.diagnosticSuggestions }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- 无分析结果状态 -->
      <div v-else class="empty-analysis">
        <el-empty description="暂无AI分析结果">
          <template #description>
            <p>点击"开始AI分析"按钮，使用人工智能分析当前影像</p>
          </template>
        </el-empty>
      </div>
      
      <!-- 历史分析记录 -->
      <div v-if="analysisHistory && analysisHistory.length > 0" class="analysis-history">
        <el-divider content-position="center">历史分析记录</el-divider>
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in analysisHistory"
            :key="index"
            :timestamp="formatTime(item.createTime)"
            :type="item.id === currentAnalysis?.id ? 'primary' : ''"
          >
            <el-card shadow="hover" :class="{'active-analysis': item.id === currentAnalysis?.id}">
              <div class="history-item-content">
                <div class="history-item-info">
                  <span class="history-item-title">AI分析 #{{ item.id }}</span>
                  <span class="history-item-status">{{ getStatusText(item.status) }}</span>
                </div>
                <div class="history-item-actions">
                  <el-button 
                    type="primary" 
                    link 
                    size="small" 
                    @click="loadAnalysisResult(item.id)"
                    :disabled="item.status !== 'completed'"
                  >
                    查看结果
                  </el-button>
                </div>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>
    
    <!-- 病变详情对话框 -->
    <el-dialog
      v-model="lesionDetailVisible"
      title="病变详情"
      width="50%"
      destroy-on-close
    >
      <div v-if="selectedLesion" class="lesion-detail">
        <div class="lesion-images">
          <el-carousel height="300px" indicator-position="outside" v-if="selectedLesion.images && selectedLesion.images.length > 0">
            <el-carousel-item v-for="(image, index) in selectedLesion.images" :key="index">
              <el-image :src="image" fit="contain" style="width: 100%; height: 100%;" />
            </el-carousel-item>
          </el-carousel>
          <el-image 
            v-else-if="selectedLesion.imageUrl" 
            :src="selectedLesion.imageUrl" 
            fit="contain" 
            style="width: 100%; height: 300px;"
          />
          <el-empty v-else description="无病变图像" />
        </div>
        
        <el-descriptions title="病变信息" :column="2" border>
          <el-descriptions-item label="类型">{{ selectedLesion.type }}</el-descriptions-item>
          <el-descriptions-item label="位置">{{ selectedLesion.location }}</el-descriptions-item>
          <el-descriptions-item label="大小">{{ selectedLesion.size }}</el-descriptions-item>
          <el-descriptions-item label="置信度">{{ (selectedLesion.confidence * 100).toFixed(1) }}%</el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">{{ selectedLesion.description || '无详细描述' }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Cpu, DocumentAdd, Loading } from '@element-plus/icons-vue';
import { parseTime } from '@/utils/ruoyi';
import { 
  requestAiAnalysis, 
  getAiAnalysisResult, 
  getAiAnalysisHistory,
  applyAiAnalysisToDiagnosis
} from '@/api/ai/imageAnalysis';

const props = defineProps({
  studyInstanceUid: {
    type: String,
    default: ''
  },
  diagnosisId: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['analysis-applied']);

// 状态变量
const analysisLoading = ref(false);
const currentAnalysis = ref(null);
const analysisHistory = ref([]);
const lesionDetailVisible = ref(false);
const selectedLesion = ref(null);
const pollingTimer = ref(null);

// 监听studyInstanceUid变化，加载历史分析记录
watch(() => props.studyInstanceUid, (newVal) => {
  if (newVal) {
    loadAnalysisHistory();
  } else {
    // 清空数据
    currentAnalysis.value = null;
    analysisHistory.value = [];
  }
}, { immediate: true });

// 在组件卸载时清除轮询定时器
onBeforeUnmount(() => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
  }
});

// 加载历史分析记录
const loadAnalysisHistory = async () => {
  if (!props.studyInstanceUid) return;
  
  try {
    const res = await getAiAnalysisHistory(props.studyInstanceUid);
    if (res.code === 200) {
      analysisHistory.value = res.data || [];
      
      // 如果有已完成的分析，默认加载最新的一条
      const completedAnalysis = analysisHistory.value.find(item => item.status === 'completed');
      if (completedAnalysis) {
        loadAnalysisResult(completedAnalysis.id);
      }
    }
  } catch (error) {
    console.error('加载AI分析历史记录失败', error);
  }
};

// 请求AI分析
const requestAnalysis = async () => {
  if (!props.studyInstanceUid) {
    ElMessage.warning('请先选择检查记录');
    return;
  }
  
  try {
    analysisLoading.value = true;
    const res = await requestAiAnalysis(props.studyInstanceUid);
    if (res.code === 200) {
      ElMessage.success('AI分析请求已提交，正在处理中');
      
      // 添加到历史记录
      const newAnalysis = res.data;
      analysisHistory.value = [newAnalysis, ...analysisHistory.value];
      
      // 开始轮询结果
      startPollingResult(newAnalysis.id);
    } else {
      ElMessage.error(res.msg || 'AI分析请求失败');
      analysisLoading.value = false;
    }
  } catch (error) {
    console.error('请求AI分析出错', error);
    ElMessage.error('系统错误，请联系管理员');
    analysisLoading.value = false;
  }
};

// 开始轮询分析结果
const startPollingResult = (analysisId) => {
  // 清除之前的轮询
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
  }
  
  // 设置轮询间隔（5秒）
  pollingTimer.value = setInterval(async () => {
    try {
      const res = await getAiAnalysisResult(analysisId);
      if (res.code === 200) {
        // 更新历史记录中的状态
        const index = analysisHistory.value.findIndex(item => item.id === analysisId);
        if (index !== -1) {
          analysisHistory.value[index] = res.data;
        }
        
        // 如果分析完成或失败，停止轮询
        if (res.data.status === 'completed' || res.data.status === 'failed') {
          clearInterval(pollingTimer.value);
          analysisLoading.value = false;
          
          if (res.data.status === 'completed') {
            currentAnalysis.value = res.data;
            ElMessage.success('AI分析已完成');
          } else {
            ElMessage.error('AI分析失败: ' + (res.data.errorMessage || '未知错误'));
          }
        }
      }
    } catch (error) {
      console.error('轮询AI分析结果出错', error);
      // 出错时不停止轮询，继续尝试
    }
  }, 5000);
};

// 加载分析结果
const loadAnalysisResult = async (analysisId) => {
  try {
    analysisLoading.value = true;
    const res = await getAiAnalysisResult(analysisId);
    if (res.code === 200) {
      currentAnalysis.value = res.data;
    } else {
      ElMessage.error(res.msg || '加载AI分析结果失败');
    }
  } catch (error) {
    console.error('加载AI分析结果出错', error);
    ElMessage.error('系统错误，请联系管理员');
  } finally {
    analysisLoading.value = false;
  }
};

// 应用分析结果到诊断报告
const applyToReport = async () => {
  if (!currentAnalysis.value || !props.diagnosisId) return;
  
  try {
    ElMessageBox.confirm(
      '确定要将AI分析结果应用到诊断报告吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(async () => {
      const res = await applyAiAnalysisToDiagnosis(currentAnalysis.value.id, props.diagnosisId);
      if (res.code === 200) {
        ElMessage.success('AI分析结果已应用到诊断报告');
        emit('analysis-applied', res.data);
      } else {
        ElMessage.error(res.msg || '应用AI分析结果失败');
      }
    }).catch(() => {
      // 用户取消操作
    });
  } catch (error) {
    console.error('应用AI分析结果出错', error);
    ElMessage.error('系统错误，请联系管理员');
  }
};

// 查看病变详情
const viewLesionDetail = (lesion) => {
  selectedLesion.value = lesion;
  lesionDetailVisible.value = true;
};

// 格式化时间
const formatTime = (time) => {
  if (!time) return '';
  return parseTime(time);
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'pending': '等待处理',
    'processing': '处理中',
    'completed': '已完成',
    'failed': '处理失败'
  };
  return statusMap[status] || status;
};

// 获取标签类型
const getTagType = (confidence) => {
  if (confidence >= 0.8) return 'success';
  if (confidence >= 0.6) return '';
  if (confidence >= 0.4) return 'warning';
  return 'danger';
};

// 获取进度条颜色
const getProgressColor = (confidence) => {
  if (confidence >= 0.8) return '#67c23a';
  if (confidence >= 0.6) return '#409eff';
  if (confidence >= 0.4) return '#e6a23c';
  return '#f56c6c';
};
</script>

<style lang="scss" scoped>
.ai-analysis-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #ebeef5;
    
    h4 {
      margin: 0;
      font-size: 16px;
    }
    
    .action-buttons {
      display: flex;
      gap: 10px;
    }
  }
  
  .panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 10px 0;
    
    .analysis-loading {
      padding: 20px 0;
      
      .loading-text {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 20px;
        color: #909399;
        
        .el-icon {
          margin-right: 8px;
          animation: rotating 2s linear infinite;
        }
      }
    }
    
    .analysis-result {
      .result-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        
        .result-title {
          font-weight: bold;
          font-size: 16px;
        }
        
        .result-time {
          color: #909399;
          font-size: 12px;
        }
      }
      
      h5 {
        margin: 15px 0 10px;
        font-size: 14px;
        color: #303133;
      }
      
      .segmentation-results {
        margin-bottom: 20px;
        
        .segmentation-card {
          margin-bottom: 10px;
          
          .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
          
          .card-content {
            .thumbnail {
              height: 120px;
              display: flex;
              justify-content: center;
              align-items: center;
              margin-bottom: 10px;
              
              .el-image {
                max-height: 100%;
                max-width: 100%;
              }
            }
            
            .metrics {
              .metric-item {
                display: flex;
                margin-bottom: 5px;
                
                .metric-label {
                  color: #909399;
                  margin-right: 5px;
                }
                
                .metric-value {
                  font-weight: bold;
                }
              }
            }
          }
        }
      }
      
      .lesion-detections {
        margin-bottom: 20px;
      }
      
      .diagnostic-suggestions {
        margin-bottom: 20px;
        
        .suggestion-content {
          background-color: #f5f7fa;
          border-radius: 4px;
          padding: 15px;
          
          .suggestion-disclaimer {
            font-size: 12px;
            color: #909399;
          }
          
          .suggestion-text {
            margin-top: 10px;
            white-space: pre-line;
            line-height: 1.6;
          }
        }
      }
    }
    
    .empty-analysis {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }
    
    .analysis-history {
      margin-top: 20px;
      
      .history-item-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .history-item-info {
          .history-item-title {
            font-weight: bold;
            margin-right: 10px;
          }
          
          .history-item-status {
            font-size: 12px;
            color: #909399;
          }
        }
      }
      
      .active-analysis {
        border-color: #409eff;
      }
    }
  }
}

.lesion-detail {
  .lesion-images {
    margin-bottom: 20px;
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
