<template>
  <transition name="notification-slide" appear>
    <div 
      class="enhanced-notification-card"
      :class="{ 'urgent': isUrgent }"
      @click="handleCardClick"
    >
      <!-- 左侧图标区域 -->
      <div class="notification-icon">
        <div class="icon-wrapper">
          <el-icon :size="28" :color="iconColor">
            <component :is="iconComponent" />
          </el-icon>
        </div>
        <div class="pulse-ring" v-if="isUrgent"></div>
      </div>

      <!-- 主要内容区域 -->
      <div class="notification-content">
        <!-- 标题和时间 -->
        <div class="notification-header">
          <h3 class="notification-title">{{ notification.title || '新的会诊申请' }}</h3>
          <span class="notification-time">{{ formatTime(notification.createTime) }}</span>
        </div>

        <!-- 详细信息 -->
        <div class="notification-details">
          <div class="detail-row" v-if="notification.requesterName">
            <span class="detail-label">申请医生</span>
            <span class="detail-value">{{ notification.requesterName }}</span>
          </div>
          
          <div class="detail-row" v-if="notification.patientName">
            <span class="detail-label">患者姓名</span>
            <span class="detail-value">{{ notification.patientName }}</span>
          </div>
          
          <div class="detail-row" v-if="notification.requestNo">
            <span class="detail-label">申请编号</span>
            <span class="detail-value request-no">{{ notification.requestNo }}</span>
          </div>

          <div class="detail-row" v-if="notification.urgencyLevel">
            <span class="detail-label">紧急程度</span>
            <el-tag 
              :type="getUrgencyTagType(notification.urgencyLevel)" 
              size="small"
              effect="dark"
            >
              {{ getUrgencyText(notification.urgencyLevel) }}
            </el-tag>
          </div>
        </div>

        <!-- 描述内容 -->
        <div class="notification-description" v-if="notification.content">
          <p>{{ notification.content }}</p>
        </div>

        <!-- 操作按钮区域 -->
        <div class="notification-actions">
          <el-button 
            type="primary" 
            size="small" 
            @click.stop="handleViewDetails"
            :icon="View"
          >
            查看详情
          </el-button>
          
          <el-button 
            v-if="showQuickActions" 
            type="success" 
            size="small" 
            @click.stop="handleQuickAccept"
            :icon="Check"
          >
            快速接受
          </el-button>
          
          <el-button 
            size="small" 
            @click.stop="handleClose"
            :icon="Close"
          >
            关闭
          </el-button>
        </div>
      </div>

      <!-- 右上角关闭按钮 -->
      <div class="close-button" @click.stop="handleClose">
        <el-icon :size="16">
          <Close />
        </el-icon>
      </div>

      <!-- 进度条（可选） -->
      <div class="notification-progress" v-if="showProgress">
        <div class="progress-bar" :style="{ width: progressWidth + '%' }"></div>
      </div>
    </div>
  </transition>
</template>

<script setup>
import { computed, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Close, 
  View, 
  Check, 
  User, 
  Document, 
  Warning, 
  SuccessFilled,
  InfoFilled,
  Bell
} from '@element-plus/icons-vue'

const props = defineProps({
  notification: {
    type: Object,
    required: true
  },
  showQuickActions: {
    type: Boolean,
    default: true
  },
  showProgress: {
    type: Boolean,
    default: false
  },
  autoClose: {
    type: Boolean,
    default: false
  },
  autoCloseDelay: {
    type: Number,
    default: 8000
  }
})

const emit = defineEmits(['close', 'action', 'view'])

const router = useRouter()
const progressWidth = ref(100)

// 计算属性
const isUrgent = computed(() => {
  return props.notification.urgencyLevel === 'URGENT' || 
         props.notification.priority >= 3
})

const iconComponent = computed(() => {
  const typeMap = {
    'REQUEST': User,
    'ACCEPT': SuccessFilled,
    'REJECT': Warning,
    'COMPLETE': Check,
    'CANCEL': Close,
    'URGENT': Warning,
    'SYSTEM': Bell
  }
  return typeMap[props.notification.type] || User
})

const iconColor = computed(() => {
  if (isUrgent.value) return '#E6A23C'
  
  const colorMap = {
    'REQUEST': '#409EFF',
    'ACCEPT': '#67C23A',
    'REJECT': '#F56C6C',
    'COMPLETE': '#67C23A',
    'CANCEL': '#909399',
    'SYSTEM': '#409EFF'
  }
  return colorMap[props.notification.type] || '#409EFF'
})

// 方法
const getUrgencyTagType = (urgencyLevel) => {
  const typeMap = {
    'URGENT': 'danger',
    'HIGH': 'warning',
    'NORMAL': 'info',
    'LOW': 'info'
  }
  return typeMap[urgencyLevel] || 'info'
}

const getUrgencyText = (urgencyLevel) => {
  const textMap = {
    'URGENT': '紧急',
    'HIGH': '高',
    'NORMAL': '普通',
    'LOW': '低'
  }
  return textMap[urgencyLevel] || '普通'
}

const formatTime = (time) => {
  if (!time) return '刚刚'
  
  const date = new Date(time)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) {
    return '刚刚'
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}

const handleCardClick = () => {
  handleViewDetails()
}

const handleViewDetails = () => {
  console.log('🔔 查看会诊详情')
  emit('view', props.notification)
  
  // 跳转到我的会诊页面
  const routeUrl = router.resolve('/consultation/my-consultation')
  window.open(routeUrl.href, '_blank')
  
  // 关闭通知
  handleClose()
}

const handleQuickAccept = () => {
  console.log('🔔 快速接受会诊')
  emit('action', {
    type: 'accept',
    notification: props.notification
  })
  
  ElMessage.success('会诊申请已接受')
  handleClose()
}

const handleClose = () => {
  console.log('🔔 关闭通知')
  emit('close')
}

// 自动关闭逻辑
const startAutoClose = () => {
  if (!props.autoClose) return
  
  const interval = setInterval(() => {
    progressWidth.value -= (100 / (props.autoCloseDelay / 100))
    
    if (progressWidth.value <= 0) {
      clearInterval(interval)
      handleClose()
    }
  }, 100)
}

// 组件挂载
onMounted(() => {
  if (props.autoClose) {
    startAutoClose()
  }
})
</script>

<style scoped>
.enhanced-notification-card {
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  padding: 20px;
  min-width: 380px;
  max-width: 420px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.enhanced-notification-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.enhanced-notification-card.urgent {
  border-left: 4px solid #E6A23C;
  animation: urgentPulse 2s infinite;
}

@keyframes urgentPulse {
  0%, 100% {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }
  50% {
    box-shadow: 0 8px 32px rgba(230, 162, 60, 0.3);
  }
}

.enhanced-notification-card {
  display: flex;
  gap: 16px;
}

.notification-icon {
  position: relative;
  flex-shrink: 0;
}

.icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #409EFF 0%, #66b3ff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.urgent .icon-wrapper {
  background: linear-gradient(135deg, #E6A23C 0%, #f0b90b 100%);
  box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);
}

.pulse-ring {
  position: absolute;
  top: -4px;
  left: -4px;
  width: 56px;
  height: 56px;
  border: 2px solid #E6A23C;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.3);
    opacity: 0;
  }
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.notification-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  line-height: 1.4;
}

.notification-time {
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
  margin-left: 8px;
}

.notification-details {
  margin-bottom: 12px;
}

.detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  font-size: 14px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-weight: 500;
  color: #606266;
  min-width: 70px;
  margin-right: 8px;
}

.detail-value {
  color: #303133;
  flex: 1;
}

.request-no {
  font-family: 'Courier New', monospace;
  background: #f0f2f5;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.notification-description {
  margin-bottom: 16px;
  padding: 12px;
  background: rgba(64, 158, 255, 0.05);
  border-radius: 8px;
  border-left: 3px solid #409EFF;
}

.notification-description p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  color: #606266;
}

.notification-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.close-button {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  color: #909399;
}

.close-button:hover {
  background: rgba(0, 0, 0, 0.2);
  color: #606266;
}

.notification-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #409EFF 0%, #66b3ff 100%);
  transition: width 0.1s linear;
}

/* 动画效果 */
.notification-slide-enter-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.notification-slide-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notification-slide-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.notification-slide-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .enhanced-notification-card {
    min-width: 320px;
    max-width: 360px;
    padding: 16px;
  }
  
  .notification-actions {
    flex-direction: column;
  }
  
  .notification-actions .el-button {
    width: 100%;
  }
}
</style>
