<template>
  <div class="consultation-notification-simple">
    <!-- 连接状态指示器 -->
    <div class="connection-status" :class="{ connected: isConnected }" @click="handleStatusClick">
      <div class="status-dot"></div>
      <div class="status-tooltip">
        {{ isConnected ? '通知连接正常，双击检查通知' : '通知连接异常，点击重连' }}
      </div>
    </div>

    <!-- 通知通过Element Plus显示，无需额外容器 -->
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { ElNotification, ElMessage } from 'element-plus'
import { getToken } from '@/utils/auth'

// 调试模式已移除

// 状态变量
const isConnected = ref(false)
const currentUserId = ref(null)
const currentToken = ref(null)
let eventSource = null

// 获取用户信息
const getUserInfo = async () => {
  try {
    const token = getToken()
    if (!token) {
      console.log('❌ 未获取到token')
      return false
    }
    
    currentToken.value = token
    console.log('✅ Token获取成功')
    
    // 获取用户信息
    const response = await fetch('/dev-api/system/user/profile', {
      headers: {
        'Authorization': 'Bearer ' + token
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      console.log('📋 API响应数据:', data)

      // 尝试不同的数据结构
      let user = null
      if (data.data && data.data.user) {
        user = data.data.user
      } else if (data.user) {
        user = data.user
      } else if (data.data) {
        user = data.data
      }

      if (user) {
        currentUserId.value = user.userId || user.id || user.user_id
        console.log('✅ 用户信息获取成功，用户ID:', currentUserId.value)
        console.log('📋 用户对象:', user)
        return true
      } else {
        console.log('❌ 用户信息结构异常:', data)
        return false
      }
    }

    console.log('❌ 用户信息获取失败，HTTP状态:', response.status)
    return false
  } catch (error) {
    console.error('❌ 获取用户信息异常:', error)
    return false
  }
}

// 建立SSE连接
const connectSSE = async () => {
  try {
    console.log('🔄 开始建立SSE连接...')
    
    // 先获取用户信息
    const userInfoOk = await getUserInfo()
    if (!userInfoOk) {
      console.log('❌ 用户信息获取失败，无法建立连接')
      return
    }
    
    // 关闭现有连接
    if (eventSource) {
      eventSource.close()
      eventSource = null
    }
    
    // 建立新连接
    const url = `/dev-api/consultation/reliable-notification/connect?token=${encodeURIComponent(currentToken.value)}`
    console.log('🔄 连接URL:', url)
    
    eventSource = new EventSource(url, {
      withCredentials: true
    })
    
    // 连接打开
    eventSource.onopen = function(event) {
      isConnected.value = true
      console.log('✅ SSE连接已建立')
    }
    
    // 连接确认
    eventSource.addEventListener('connected', function(event) {
      console.log('✅ 收到连接确认:', event.data)
    })
    
    // 接收会诊通知
    eventSource.addEventListener('consultation-notification', function(event) {
      console.log('🔔 收到会诊通知:', event.data)
      console.log('🔔 准备显示通知...')

      try {
        const notification = JSON.parse(event.data)
        console.log('🔔 解析通知数据成功:', notification)
        showSimpleNotification(notification)
      } catch (error) {
        console.error('❌ 解析通知数据失败:', error)
        console.log('🔔 显示原始通知...')
        // 显示原始通知
        ElNotification({
          title: '新的会诊通知',
          message: event.data,
          type: 'info',
          position: 'bottom-right',
          duration: 0
        })
        console.log('🔔 原始通知已显示')
      }
    })
    
    // 心跳
    eventSource.addEventListener('heartbeat', function(event) {
      console.log('💓 心跳:', event.data)
    })
    
    // 连接错误
    eventSource.onerror = function(event) {
      isConnected.value = false
      console.error('❌ SSE连接错误:', event)
      
      // 5秒后重连
      setTimeout(() => {
        console.log('🔄 尝试重连...')
        connectSSE()
      }, 5000)
    }
    
  } catch (error) {
    console.error('❌ 建立SSE连接失败:', error)
  }
}

// 显示简单通知
const showSimpleNotification = (notification) => {
  console.log('🔔 开始显示通知:', notification)

  // 构建通知内容
  let message = ''
  if (notification.requesterName) {
    message += `申请医生：${notification.requesterName}\n`
  }
  if (notification.patientName) {
    message += `患者姓名：${notification.patientName}\n`
  }
  if (notification.requestNo) {
    message += `申请编号：${notification.requestNo}\n`
  }
  if (notification.content) {
    message += `详细说明：${notification.content}`
  }

  console.log('🔔 通知内容:', message)

  try {
    // 使用自定义HTML内容的Element Plus通知
    console.log('🔔 调用增强ElNotification...')
    const notificationInstance = ElNotification({
      title: notification.title || '新的会诊申请',
      dangerouslyUseHTMLString: true,
      message: createEnhancedNotificationHTML(notification, message),
      type: 'info',
      position: 'bottom-right',
      duration: 0, // 不自动关闭
      showClose: true,
      customClass: 'enhanced-consultation-notification',
      onClick: () => {
        console.log('🔔 通知被点击')
        // 跳转到我的会诊页面
        window.open('/consultation/my-consultation', '_blank')
        notificationInstance.close()
      }
    })

    console.log('✅ 增强ElNotification调用成功')

  } catch (error) {
    console.error('❌ ElNotification调用失败:', error)
    // 备选方案：使用alert
    alert(`${notification.title || '新的会诊申请'}\n${message || '您有新的会诊申请，请及时处理'}`)
  }

  // 发送确认
  if (notification.notificationId) {
    sendAcknowledgment(notification.notificationId)
  }
}

// 发送确认
const sendAcknowledgment = async (notificationId) => {
  try {
    console.log('🔄 发送确认，通知ID:', notificationId)
    
    const response = await fetch(`/dev-api/consultation/reliable-notification/acknowledge/${notificationId}`, {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer ' + currentToken.value
      }
    })
    
    if (response.ok) {
      console.log('✅ 确认发送成功')
    } else {
      console.log('❌ 确认发送失败:', response.status)
    }
  } catch (error) {
    console.error('❌ 发送确认异常:', error)
  }
}

// 手动连接方法已移除

// 创建增强通知HTML内容
const createEnhancedNotificationHTML = (notification, message) => {
  const urgencyLevel = notification.urgencyLevel || 'NORMAL'
  const urgencyColor = {
    'URGENT': '#E6A23C',
    'HIGH': '#F56C6C',
    'NORMAL': '#409EFF',
    'LOW': '#67C23A'
  }[urgencyLevel] || '#409EFF'

  const urgencyText = {
    'URGENT': '紧急',
    'HIGH': '高',
    'NORMAL': '普通',
    'LOW': '低'
  }[urgencyLevel] || '普通'

  return `
    <div style="
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      line-height: 1.5;
      color: #303133;
    ">
      <div style="
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #EBEEF5;
      ">
        <div style="
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: ${urgencyColor};
          margin-right: 8px;
          ${urgencyLevel === 'URGENT' ? 'animation: pulse 2s infinite;' : ''}
        "></div>
        <span style="
          background: ${urgencyColor};
          color: white;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
        ">${urgencyText}</span>
      </div>

      <div style="margin-bottom: 12px;">
        ${message.split('\n').map(line =>
          line ? `<div style="
            margin-bottom: 6px;
            padding: 4px 0;
            display: flex;
            align-items: center;
          ">
            <span style="
              font-weight: 500;
              color: #606266;
              min-width: 70px;
              margin-right: 8px;
            ">${line.split('：')[0]}：</span>
            <span style="
              color: #303133;
              ${line.includes('申请编号') ? 'font-family: monospace; background: #F5F7FA; padding: 2px 6px; border-radius: 4px; font-size: 12px;' : ''}
            ">${line.split('：')[1] || ''}</span>
          </div>` : ''
        ).join('')}
      </div>

      <div style="
        background: linear-gradient(135deg, #409EFF10 0%, #409EFF20 100%);
        border-left: 3px solid #409EFF;
        padding: 12px;
        border-radius: 6px;
        margin-bottom: 12px;
        font-size: 14px;
        color: #606266;
      ">
        💡 点击此通知可快速跳转到我的会诊页面查看详情
      </div>

      <style>
        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.5; }
        }
      </style>
    </div>
  `
}

// 处理状态指示器点击
const handleStatusClick = () => {
  if (!isConnected.value) {
    console.log('🔄 手动重连SSE...')
    connectSSE()
    ElMessage.info('正在重新连接...')
  } else {
    // 双击检查最近通知
    checkRecentNotifications()
  }
}

// 检查最近通知
const checkRecentNotifications = async () => {
  try {
    console.log('🔍 检查最近通知...')
    const response = await fetch('/dev-api/consultation/reliable-notification/list?pageNum=1&pageSize=5', {
      headers: {
        'Authorization': 'Bearer ' + currentToken.value
      }
    })

    if (response.ok) {
      const data = await response.json()
      const notifications = data.rows || []
      console.log('📋 最近通知列表:', notifications)

      if (notifications.length > 0) {
        const latestNotification = notifications[0]
        console.log('📋 最新通知:', latestNotification)

        // 如果最新通知是PENDING状态，手动显示
        if (latestNotification.status === 'PENDING') {
          console.log('🔔 发现待显示通知，手动显示...')
          showSimpleNotification({
            notificationId: latestNotification.id,
            title: latestNotification.title,
            content: latestNotification.content,
            requesterName: '手动检查',
            patientName: '手动检查',
            requestNo: latestNotification.id
          })
          ElMessage.success('发现1条待显示通知')
        } else {
          ElMessage.info('没有待显示的通知')
        }
      } else {
        ElMessage.info('没有找到通知记录')
      }
    }
  } catch (error) {
    console.error('❌ 检查通知失败:', error)
    ElMessage.error('检查通知失败')
  }
}

// 测试方法已移除

// 断开连接
const disconnect = () => {
  if (eventSource) {
    eventSource.close()
    eventSource = null
    isConnected.value = false
    console.log('🔌 SSE连接已断开')
  }
}

// 组件挂载
onMounted(() => {
  console.log('🚀 ConsultationNotificationSimple组件已挂载')
  // 延迟1秒后连接，确保页面完全加载
  setTimeout(() => {
    connectSSE()
  }, 1000)
})

// 组件卸载
onBeforeUnmount(() => {
  console.log('🔌 ConsultationNotificationSimple组件即将卸载')
  disconnect()
})

// 暴露方法
defineExpose({
  connect: connectSSE,
  disconnect,
  isConnected: () => isConnected.value
})
</script>

<style scoped>
.consultation-notification-simple {
  position: relative;
}

/* 连接状态指示器 */
.connection-status {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 12px;
  height: 12px;
  z-index: 9998;
  cursor: pointer;
  position: relative;
}

.status-dot {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: #F56C6C;
  transition: all 0.3s;
}

.connection-status.connected .status-dot {
  background: #67C23A;
  animation: pulse-green 2s infinite;
}

.status-tooltip {
  position: absolute;
  top: -35px;
  right: 0;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s;
}

.connection-status:hover .status-tooltip {
  opacity: 1;
}

@keyframes pulse-green {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

/* 调试样式已移除 */

.notification-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 3000;
  pointer-events: none;
}

.notification-container > * {
  pointer-events: auto;
}

/* 确保通知在最顶层 */
.notification-container {
  z-index: 9999 !important;
}

/* 增强通知样式 */
:deep(.enhanced-consultation-notification) {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
  border: 1px solid rgba(64, 158, 255, 0.2) !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
  backdrop-filter: blur(10px) !important;
  min-width: 380px !important;
  max-width: 420px !important;
}

:deep(.enhanced-consultation-notification .el-notification__content) {
  margin: 0 !important;
  padding: 0 !important;
}

:deep(.enhanced-consultation-notification .el-notification__title) {
  font-size: 16px !important;
  font-weight: 600 !important;
  color: #303133 !important;
  margin-bottom: 8px !important;
}

:deep(.enhanced-consultation-notification .el-notification__closeBtn) {
  top: 12px !important;
  right: 12px !important;
  font-size: 16px !important;
  color: #909399 !important;
}

:deep(.enhanced-consultation-notification .el-notification__closeBtn:hover) {
  color: #606266 !important;
}

/* 紧急通知动画 */
:deep(.enhanced-consultation-notification.urgent) {
  border-left: 4px solid #E6A23C !important;
  animation: urgentGlow 2s infinite !important;
}

@keyframes urgentGlow {
  0%, 100% {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
  }
  50% {
    box-shadow: 0 8px 32px rgba(230, 162, 60, 0.3) !important;
  }
}

/* 悬停效果 */
:deep(.enhanced-consultation-notification:hover) {
  transform: translateY(-2px) !important;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}
</style>
