<template>
  <div class="diagnosis-dict-selector">
    <el-tree
      ref="treeRef"
      :data="treeData"
      :props="defaultProps"
      node-key="id"
      :load="loadNode"
      lazy
      :expand-on-click-node="true"
      @node-click="handleNodeClick"
      highlight-current
    ></el-tree>
  </div>
</template>

<script setup>
import {ref, onMounted} from 'vue';
import {listDictCategory} from '@/api/diagnosis/dictCategory';
import {getDiagnosisByCategory} from '@/api/diagnosis/diagnosisTree';

const emit = defineEmits(['select']);

const treeRef = ref(null);
const treeData = ref([]);

const defaultProps = {
  label: 'label',
  children: 'children',
  isLeaf: 'isLeaf'
};





// Load child nodes (either subcategories or diagnosis items)
const loadNode = async (node, resolve) => {
  // Root level - load top level categories
  if (node.level === 0) {
    try {
      // 只加载没有父ID的一级节点
      const response = await listDictCategory({enableFlag: 'Y', parentId: 0});
      if (response && response.data) {
        // Transform data to match tree format
        const nodes = response.data.map(item => ({
          id: item.id,
          label: item.name,
          isLeaf: false,
          type: 'category',
          data: item
        }));
        resolve(nodes);
      } else {
        resolve([]);
      }
    } catch (error) {
      console.error('Failed to load diagnosis categories:', error);
      resolve([]);
    }
    return;
  }

  // Child nodes
  try {
    // First check if there are subcategories
    const categoryResponse = await listDictCategory({parentId: node.data.id, enableFlag: 'Y'});
    const categories = categoryResponse.data || [];

    // Then get diagnosis items for this category
    const diagnosisResponse = await getDiagnosisByCategory(node.data.id);
    const diagnosisItems = diagnosisResponse.rows || [];

    // Combine both into a single array
    const children = [
      ...categories.map(item => ({
        id: item.id,
        label: item.name,
        isLeaf: false,
        type: 'category',
        data: item
      })),
      ...diagnosisItems.map(item => ({
        id: `dict_${item.id}`,
        label: item.content,
        isLeaf: true,
        type: 'diagnosis',
        data: item
      }))
    ];

    resolve(children);
  } catch (error) {
    console.error('Failed to load diagnosis data:', error);
    resolve([]);
  }
};

// Handle node click
const handleNodeClick = (data, node) => {
  if (!data) return;

  // 如果是诊断节点，发送选择事件
  if (data.type === 'diagnosis') {
    emit('select', {
      id: data.id,
      type: data.type,
      data: data.data
    });
  }
  // 分类节点的展开/折叠由树组件自动处理（expand-on-click-node="true"）
};




</script>

<style scoped>
.diagnosis-dict-selector {
  width: 100%;
  position: relative;
  max-height: 100%;
  overflow: auto;
}

/* 自定义树节点样式 */
:deep(.el-tree-node__content) {
  height: 32px;
  line-height: 32px;
}

/* 分类节点样式 */
:deep(.el-tree-node.is-expanded > .el-tree-node__content) {
  font-weight: bold;
  color: #409EFF;
}

/* 强化分类节点的可点击性 */
:deep(.el-tree-node:not(.is-leaf) > .el-tree-node__content) {
  cursor: pointer;
}

:deep(.el-tree-node:not(.is-leaf) > .el-tree-node__content:hover) {
  background-color: #f5f7fa;
}

/* 叶子节点样式 */
:deep(.el-tree-node.is-leaf > .el-tree-node__content) {
  cursor: pointer;
}

:deep(.el-tree-node.is-leaf > .el-tree-node__content:hover) {
  background-color: #f5f7fa;
}
</style>
