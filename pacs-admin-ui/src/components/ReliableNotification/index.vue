<template>
  <div class="reliable-notification">
    <!-- 连接状态指示器 -->
    <div v-if="showConnectionStatus" class="connection-status" :class="connectionStatusClass">
      <el-icon><Connection /></el-icon>
      <span>{{ connectionStatusText }}</span>
    </div>

    <!-- 通知卡片容器 -->
    <div class="notification-container">
      <ConsultationNotificationCard
        v-for="notification in activeNotifications"
        :key="notification.id"
        :notification="notification"
        @close="handleNotificationClose(notification)"
        @action="handleNotificationAction"
      />
    </div>

    <!-- 离线通知提示 -->
    <el-dialog
      v-model="showOfflineDialog"
      title="离线通知"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="offline-notifications">
        <p>您有 {{ offlineNotifications.length }} 条离线通知：</p>
        <div class="notification-list">
          <div
            v-for="notification in offlineNotifications"
            :key="notification.id"
            class="notification-item"
          >
            <div class="notification-header">
              <span class="notification-title">{{ notification.title }}</span>
              <span class="notification-time">{{ formatTime(notification.createTime) }}</span>
            </div>
            <div class="notification-content">{{ notification.content }}</div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="markAllAsRead">全部标记为已读</el-button>
          <el-button type="primary" @click="showOfflineDialog = false">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Connection } from '@element-plus/icons-vue'
import { connectConsultationNotification, acknowledgeNotification, getOfflineNotifications, sendHeartbeatResponse } from '@/api/consultation/reliableNotification'
import { acceptConsultationRequest, rejectConsultationRequest, getConsultationRequest } from '@/api/consultation/consultation'
import ConsultationNotificationCard from './ConsultationNotificationCard.vue'

// 响应式数据
const eventSource = ref(null)
const isConnected = ref(false)
const reconnectAttempts = ref(0)
const maxReconnectAttempts = ref(10)
const reconnectInterval = ref(null)
const activeNotifications = ref([])
const offlineNotifications = ref([])
const showOfflineDialog = ref(false)
const showConnectionStatus = ref(false)
const heartbeatInterval = ref(null)
const lastHeartbeat = ref(Date.now())

// 新增：消息去重和本地存储相关
const processedMessageIds = ref(new Set())
const pendingNotifications = ref([])
const isOnline = ref(navigator.onLine)
const networkStatusInterval = ref(null)

// 配置
const config = reactive({
  reconnectDelay: 1000, // 初始重连延迟
  maxReconnectDelay: 30000, // 最大重连延迟
  heartbeatInterval: 30000, // 心跳间隔（与后端保持一致）
  heartbeatTimeout: 90000, // 心跳超时时间（与后端保持一致，允许3次心跳失败）
  notificationTimeout: 5000, // 通知显示时间
  ackTimeout: 30000, // 确认超时时间
  enableSound: true, // 启用声音提醒
  enableVibration: true, // 启用震动提醒（移动端）
  // 新增配置
  localStorageKey: 'reliable_notifications_pending', // 本地存储键名
  localStorageExpiry: 24 * 60 * 60 * 1000, // 本地存储过期时间（24小时）
  networkCheckInterval: 5000, // 网络状态检查间隔
  priorityLevels: {
    LOW: 0,
    NORMAL: 1,
    HIGH: 2,
    URGENT: 3
  }
})

// 计算属性
const connectionStatusClass = computed(() => ({
  'status-connected': isConnected.value,
  'status-disconnected': !isConnected.value,
  'status-reconnecting': reconnectAttempts.value > 0 && !isConnected.value
}))

const connectionStatusText = computed(() => {
  if (isConnected.value) {
    return '通知连接正常'
  } else if (reconnectAttempts.value > 0) {
    return `正在重连... (${reconnectAttempts.value}/${maxReconnectAttempts.value})`
  } else {
    return '通知连接断开'
  }
})

// 连接到通知服务
const connectToNotificationService = async () => {
  try {
    console.log('建立可靠通知连接...')

    // 加载本地存储的待处理通知
    loadPendingNotificationsFromStorage()

    eventSource.value = connectConsultationNotification()

    // 连接成功事件
    eventSource.value.addEventListener('connected', handleConnected)

    // 接收通知
    eventSource.value.addEventListener('consultation-notification', handleNotification)

    // 接收广播通知
    eventSource.value.addEventListener('consultation-broadcast', handleBroadcast)

    // 心跳响应
    eventSource.value.addEventListener('heartbeat', handleHeartbeat)

    // 离线通知
    eventSource.value.addEventListener('offline-notifications', handleOfflineNotifications)

    // 连接错误处理
    eventSource.value.onerror = handleConnectionError

    // 连接打开事件
    eventSource.value.onopen = handleConnectionOpen

  } catch (error) {
    console.error('建立通知连接失败:', error)
    ElMessage.error('建立通知连接失败')
    scheduleReconnect()
  }
}

// 处理连接成功
const handleConnected = (event) => {
  console.log('通知连接已建立:', event.data)
  isConnected.value = true
  reconnectAttempts.value = 0
  showConnectionStatus.value = false
  
  // 启动心跳
  startHeartbeat()
  
  // 请求离线通知
  requestOfflineNotifications()
}

// 处理通知
const handleNotification = (event) => {
  try {
    const notification = JSON.parse(event.data)
    console.log('收到通知:', notification)

    // 消息去重检查
    if (isDuplicateMessage(notification)) {
      console.log('重复消息，已忽略:', notification.notificationId)
      return
    }

    // 标记消息为已处理
    markMessageAsProcessed(notification)

    // 添加到待处理列表并保存到本地存储
    addToPendingNotifications(notification)

    // 显示通知
    showNotification(notification)

    // 发送确认
    if (notification.notificationId) {
      acknowledgeNotificationReceived(notification.notificationId)
    }

    // 播放提示音（根据优先级）
    if (config.enableSound) {
      playNotificationSound(notification.priority)
    }

    // 震动提醒（移动端，根据优先级）
    if (config.enableVibration && 'vibrate' in navigator) {
      const vibrationPattern = getVibrationPattern(notification.priority)
      navigator.vibrate(vibrationPattern)
    }

  } catch (error) {
    console.error('处理通知失败:', error)
  }
}

// 处理广播通知
const handleBroadcast = (event) => {
  try {
    const notification = JSON.parse(event.data)
    console.log('收到广播通知:', notification)
    showNotification(notification)
  } catch (error) {
    console.error('处理广播通知失败:', error)
  }
}

// 处理心跳
const handleHeartbeat = (event) => {
  try {
    lastHeartbeat.value = Date.now()
    console.debug('收到心跳:', event.data)

    // 发送心跳响应给服务端
    sendHeartbeatResponseToServer()

  } catch (error) {
    console.error('处理心跳失败:', error)
  }
}

// 发送心跳响应给服务端
const sendHeartbeatResponseToServer = async () => {
  try {
    await sendHeartbeatResponse()
    console.debug('心跳响应发送成功')
  } catch (error) {
    console.warn('心跳响应发送失败:', error)
    // 心跳响应失败不影响主要功能，只记录警告
  }
}

// 处理离线通知
const handleOfflineNotifications = (event) => {
  try {
    const notifications = JSON.parse(event.data)
    if (notifications && notifications.length > 0) {
      offlineNotifications.value = notifications
      showOfflineDialog.value = true
      console.log('收到离线通知:', notifications.length, '条')
    }
  } catch (error) {
    console.error('处理离线通知失败:', error)
  }
}

// 处理连接错误
const handleConnectionError = (error) => {
  console.error('通知连接错误:', error)
  isConnected.value = false
  showConnectionStatus.value = true
  
  if (eventSource.value && eventSource.value.readyState === EventSource.CLOSED) {
    console.log('连接已关闭，尝试重连...')
    scheduleReconnect()
  }
}

// 处理连接打开
const handleConnectionOpen = (event) => {
  console.log('通知连接已打开:', event)
  isConnected.value = true
}

// 显示通知
const showNotification = (notification) => {
  const priority = notification.priority || config.priorityLevels.NORMAL

  // 转换通知数据格式以适配ConsultationNotificationCard
  const notificationConfig = {
    id: notification.notificationId || Date.now(),
    notificationId: notification.notificationId,
    title: notification.title,
    content: notification.content,
    type: notification.type,
    priority: priority,
    urgencyLevel: getUrgencyLevel(priority),
    createTime: notification.createTime || Date.now(),
    patientName: notification.patientName,
    studyDescription: notification.studyDescription,
    requestingDoctor: notification.requestingDoctor,
    requestNo: notification.requestNo,
    requesterName: notification.requesterName,
    url: notification.url
  }

  // 添加到活动通知列表
  activeNotifications.value.push(notificationConfig)

  // 设置自动关闭（如果不是紧急通知）
  if (!isUrgentNotification(notificationConfig)) {
    const delay = getAutoCloseDelay(notificationConfig)
    if (delay > 0) {
      setTimeout(() => {
        handleNotificationClose(notificationConfig)
      }, delay)
    }
  }
}

// 获取通知类型
const getNotificationType = (type, priority = config.priorityLevels.NORMAL) => {
  // 紧急消息统一使用error类型
  if (priority >= config.priorityLevels.URGENT) {
    return 'error'
  }

  const typeMap = {
    'REQUEST': 'info',
    'ACCEPT': 'success',
    'REJECT': 'warning',
    'COMPLETE': 'success',
    'CANCEL': 'warning',
    'URGENT': 'error'
  }
  return typeMap[type] || 'info'
}

// 处理通知点击（保留兼容性，但主要逻辑已移到handleNotificationView）
const handleNotificationClick = (notification) => {
  console.log('通知被点击:', notification)
  handleNotificationView(notification)
}

// 处理通知关闭
const handleNotificationClose = (notification) => {
  const notificationId = notification.id || notification.notificationId
  const index = activeNotifications.value.findIndex(n =>
    (n.id || n.notificationId) === notificationId
  )

  if (index > -1) {
    activeNotifications.value.splice(index, 1)
  }

  // 从待处理列表中移除并更新本地存储
  removeFromPendingNotifications(notificationId)

  // 确认通知（如果有notificationId）
  if (notification.notificationId) {
    acknowledgeNotificationReceived(notification.notificationId)
  }
}

// 消息去重检查
const isDuplicateMessage = (notification) => {
  const messageId = notification.notificationId || notification.id
  if (!messageId) return false
  return processedMessageIds.value.has(messageId.toString())
}

// 标记消息为已处理
const markMessageAsProcessed = (notification) => {
  const messageId = notification.notificationId || notification.id
  if (messageId) {
    processedMessageIds.value.add(messageId.toString())

    // 限制Set大小，避免内存泄漏
    if (processedMessageIds.value.size > 1000) {
      const firstItem = processedMessageIds.value.values().next().value
      processedMessageIds.value.delete(firstItem)
    }
  }
}

// 添加到待处理通知列表
const addToPendingNotifications = (notification) => {
  const pendingNotification = {
    ...notification,
    timestamp: Date.now(),
    priority: notification.priority || config.priorityLevels.NORMAL
  }

  // 根据优先级插入到合适位置
  const insertIndex = findInsertIndex(pendingNotification)
  pendingNotifications.value.splice(insertIndex, 0, pendingNotification)

  // 保存到本地存储
  savePendingNotificationsToStorage()
}

// 从待处理通知列表中移除
const removeFromPendingNotifications = (notificationId) => {
  const index = pendingNotifications.value.findIndex(n =>
    (n.notificationId || n.id) === notificationId
  )
  if (index > -1) {
    pendingNotifications.value.splice(index, 1)
    savePendingNotificationsToStorage()
  }
}

// 根据优先级找到插入位置
const findInsertIndex = (notification) => {
  const priority = notification.priority || config.priorityLevels.NORMAL

  for (let i = 0; i < pendingNotifications.value.length; i++) {
    const existingPriority = pendingNotifications.value[i].priority || config.priorityLevels.NORMAL
    if (priority > existingPriority) {
      return i
    }
  }

  return pendingNotifications.value.length
}

// 确认通知已收到
const acknowledgeNotificationReceived = async (notificationId) => {
  try {
    await acknowledgeNotification(notificationId)
    console.log('通知确认成功:', notificationId)

    // 从待处理列表中移除
    removeFromPendingNotifications(notificationId)
  } catch (error) {
    console.error('通知确认失败:', notificationId, error)
  }
}

// 保存待处理通知到本地存储
const savePendingNotificationsToStorage = () => {
  try {
    const data = {
      notifications: pendingNotifications.value,
      timestamp: Date.now()
    }
    localStorage.setItem(config.localStorageKey, JSON.stringify(data))
  } catch (error) {
    console.warn('保存通知到本地存储失败:', error)
  }
}

// 从本地存储加载待处理通知
const loadPendingNotificationsFromStorage = () => {
  try {
    const stored = localStorage.getItem(config.localStorageKey)
    if (!stored) return

    const data = JSON.parse(stored)
    const now = Date.now()

    // 检查数据是否过期
    if (now - data.timestamp > config.localStorageExpiry) {
      localStorage.removeItem(config.localStorageKey)
      return
    }

    // 过滤掉过期的通知
    const validNotifications = data.notifications.filter(notification => {
      const notificationAge = now - (notification.timestamp || 0)
      return notificationAge < config.localStorageExpiry
    })

    pendingNotifications.value = validNotifications

    // 重新显示未确认的通知
    validNotifications.forEach(notification => {
      if (!isDuplicateMessage(notification)) {
        showNotification(notification)
        markMessageAsProcessed(notification)
      }
    })

    console.log('从本地存储恢复通知:', validNotifications.length, '条')
  } catch (error) {
    console.warn('从本地存储加载通知失败:', error)
    localStorage.removeItem(config.localStorageKey)
  }
}

// 清理本地存储
const cleanupLocalStorage = () => {
  try {
    const stored = localStorage.getItem(config.localStorageKey)
    if (!stored) return

    const data = JSON.parse(stored)
    const now = Date.now()

    // 清理过期数据
    if (now - data.timestamp > config.localStorageExpiry) {
      localStorage.removeItem(config.localStorageKey)
      return
    }

    // 清理过期通知
    const validNotifications = data.notifications.filter(notification => {
      const notificationAge = now - (notification.timestamp || 0)
      return notificationAge < config.localStorageExpiry
    })

    if (validNotifications.length !== data.notifications.length) {
      pendingNotifications.value = validNotifications
      savePendingNotificationsToStorage()
    }
  } catch (error) {
    console.warn('清理本地存储失败:', error)
  }
}

// 请求离线通知
const requestOfflineNotifications = async () => {
  try {
    const response = await getOfflineNotifications()
    if (response.data && response.data.length > 0) {
      offlineNotifications.value = response.data
      showOfflineDialog.value = true
    }
  } catch (error) {
    console.error('获取离线通知失败:', error)
  }
}

// 标记所有通知为已读
const markAllAsRead = async () => {
  try {
    for (const notification of offlineNotifications.value) {
      if (notification.notificationId) {
        await acknowledgeNotificationReceived(notification.notificationId)
      }
    }
    offlineNotifications.value = []
    showOfflineDialog.value = false
    ElMessage.success('所有通知已标记为已读')
  } catch (error) {
    console.error('标记通知为已读失败:', error)
    ElMessage.error('标记通知为已读失败')
  }
}

// 安排重连
const scheduleReconnect = () => {
  if (reconnectAttempts.value >= maxReconnectAttempts.value) {
    console.error('达到最大重连次数，停止重连')
    ElMessage.error('通知连接失败，请刷新页面重试')
    return
  }

  // 根据网络状态调整重连策略
  let delay = Math.min(
    config.reconnectDelay * Math.pow(2, reconnectAttempts.value),
    config.maxReconnectDelay
  )

  // 如果网络离线，延长重连间隔
  if (!isOnline.value) {
    delay = Math.max(delay, 10000) // 至少10秒
  }

  reconnectAttempts.value++
  console.log(`${delay}ms后进行第${reconnectAttempts.value}次重连`)

  reconnectInterval.value = setTimeout(() => {
    // 重连前再次检查网络状态
    if (isOnline.value) {
      connectToNotificationService()
    } else {
      console.log('网络离线，延迟重连')
      scheduleReconnect()
    }
  }, delay)
}

// 网络状态监听
const setupNetworkStatusMonitoring = () => {
  // 监听网络状态变化
  window.addEventListener('online', handleNetworkOnline)
  window.addEventListener('offline', handleNetworkOffline)

  // 定期检查网络状态
  networkStatusInterval.value = setInterval(() => {
    const currentOnlineStatus = navigator.onLine
    if (currentOnlineStatus !== isOnline.value) {
      isOnline.value = currentOnlineStatus
      console.log('网络状态变化:', currentOnlineStatus ? '在线' : '离线')
    }
  }, config.networkCheckInterval)
}

// 网络恢复处理
const handleNetworkOnline = () => {
  console.log('网络已恢复')
  isOnline.value = true

  // 如果连接断开，立即尝试重连
  if (!isConnected.value) {
    console.log('网络恢复，立即尝试重连')
    reconnectAttempts.value = 0 // 重置重连次数
    connectToNotificationService()
  }
}

// 网络断开处理
const handleNetworkOffline = () => {
  console.log('网络已断开')
  isOnline.value = false
  showConnectionStatus.value = true
}

// 启动心跳
const startHeartbeat = () => {
  if (heartbeatInterval.value) {
    clearInterval(heartbeatInterval.value)
  }
  
  heartbeatInterval.value = setInterval(() => {
    const now = Date.now()
    if (now - lastHeartbeat.value > config.heartbeatTimeout) {
      console.warn('心跳超时，连接可能已断开')
      isConnected.value = false
      scheduleReconnect()
    }
  }, config.heartbeatInterval)
}

// 播放通知声音
const playNotificationSound = (priority = config.priorityLevels.NORMAL) => {
  try {
    let soundFile = '/static/sounds/notification.mp3'
    let volume = 0.5

    // 根据优先级选择不同的声音和音量
    switch (priority) {
      case config.priorityLevels.URGENT:
        soundFile = '/static/sounds/urgent.mp3'
        volume = 0.8
        break
      case config.priorityLevels.HIGH:
        soundFile = '/static/sounds/high.mp3'
        volume = 0.7
        break
      case config.priorityLevels.LOW:
        soundFile = '/static/sounds/low.mp3'
        volume = 0.3
        break
      default:
        soundFile = '/static/sounds/notification.mp3'
        volume = 0.5
    }

    const audio = new Audio(soundFile)
    audio.volume = volume
    audio.play().catch(error => {
      console.warn('播放通知声音失败:', error)
      // 如果特定声音文件不存在，使用默认声音
      if (soundFile !== '/static/sounds/notification.mp3') {
        const defaultAudio = new Audio('/static/sounds/notification.mp3')
        defaultAudio.volume = volume
        defaultAudio.play().catch(err => console.warn('播放默认声音失败:', err))
      }
    })
  } catch (error) {
    console.warn('创建音频对象失败:', error)
  }
}

// 获取震动模式
const getVibrationPattern = (priority = config.priorityLevels.NORMAL) => {
  switch (priority) {
    case config.priorityLevels.URGENT:
      return [200, 100, 200, 100, 200, 100, 200] // 长震动
    case config.priorityLevels.HIGH:
      return [200, 100, 200, 100, 200] // 中等震动
    case config.priorityLevels.LOW:
      return [100] // 短震动
    default:
      return [200, 100, 200] // 默认震动
  }
}

// 获取优先级样式类
const getPriorityClass = (priority = config.priorityLevels.NORMAL) => {
  switch (priority) {
    case config.priorityLevels.URGENT:
      return 'notification-urgent'
    case config.priorityLevels.HIGH:
      return 'notification-high'
    case config.priorityLevels.LOW:
      return 'notification-low'
    default:
      return 'notification-normal'
  }
}

// 获取紧急程度级别（适配ConsultationNotificationCard）
const getUrgencyLevel = (priority = config.priorityLevels.NORMAL) => {
  switch (priority) {
    case config.priorityLevels.URGENT:
      return 'URGENT'
    case config.priorityLevels.HIGH:
      return 'HIGH'
    case config.priorityLevels.LOW:
      return 'LOW'
    default:
      return 'NORMAL'
  }
}

// 判断是否为紧急通知
const isUrgentNotification = (notification) => {
  const priority = notification.priority || config.priorityLevels.NORMAL
  return priority >= config.priorityLevels.URGENT
}

// 获取自动关闭延迟时间
const getAutoCloseDelay = (notification) => {
  const priority = notification.priority || config.priorityLevels.NORMAL

  switch (priority) {
    case config.priorityLevels.URGENT:
      return 0 // 紧急消息不自动关闭
    case config.priorityLevels.HIGH:
      return 10000 // 高优先级10秒
    case config.priorityLevels.LOW:
      return 3000 // 低优先级3秒
    default:
      return config.notificationTimeout // 默认5秒
  }
}

// 处理通知操作
const handleNotificationAction = async (actionData) => {
  console.log('处理通知操作:', actionData)

  const { type, notification } = actionData

  try {
    switch (type) {
      case 'accept':
        await handleAcceptConsultation(notification)
        break
      case 'reject':
        await handleRejectConsultation(notification)
        break
      case 'view':
        await handleViewConsultation(notification)
        break
      default:
        console.log('未知操作类型:', type)
        ElMessage.warning('未知操作类型')
        return
    }
  } catch (error) {
    console.error('处理通知操作失败:', error)
    ElMessage.error('操作失败: ' + (error.message || '未知错误'))
  }

  // 确认通知
  if (notification.notificationId) {
    acknowledgeNotificationReceived(notification.notificationId)
  }
}

// 处理接受会诊
const handleAcceptConsultation = async (notification) => {
  console.log('处理接受会诊:', notification)

  // 从通知中提取会诊申请ID
  const consultationId = extractConsultationId(notification)
  if (!consultationId) {
    throw new Error('无法获取会诊申请ID')
  }

  // 显示确认对话框
  const confirmed = await showConfirmDialog(
    '确认接受会诊',
    `确定要接受来自 ${notification.requesterName || '未知医生'} 的会诊申请吗？`,
    'success'
  )

  if (!confirmed) {
    return
  }

  // 调用接受会诊API
  const acceptData = {
    acceptTime: new Date().toISOString(),
    acceptRemark: '通过通知快速接受'
  }

  await acceptConsultationRequest(consultationId, acceptData)

  ElMessage.success('会诊申请已接受')
  console.log('会诊申请接受成功:', consultationId)
}

// 处理拒绝会诊
const handleRejectConsultation = async (notification) => {
  console.log('处理拒绝会诊:', notification)

  // 从通知中提取会诊申请ID
  const consultationId = extractConsultationId(notification)
  if (!consultationId) {
    throw new Error('无法获取会诊申请ID')
  }

  // 显示确认对话框和拒绝原因输入
  const result = await showRejectDialog(
    '拒绝会诊申请',
    `确定要拒绝来自 ${notification.requesterName || '未知医生'} 的会诊申请吗？`
  )

  if (!result.confirmed) {
    return
  }

  // 调用拒绝会诊API
  const rejectData = {
    rejectTime: new Date().toISOString(),
    rejectReason: result.reason || '通过通知快速拒绝'
  }

  await rejectConsultationRequest(consultationId, rejectData)

  ElMessage.warning('会诊申请已拒绝')
  console.log('会诊申请拒绝成功:', consultationId)
}

// 处理查看会诊详情
const handleViewConsultation = async (notification) => {
  console.log('查看会诊详情:', notification)

  // 从通知中提取会诊申请ID
  const consultationId = extractConsultationId(notification)
  if (!consultationId) {
    // 如果没有具体的会诊ID，跳转到会诊列表页面
    window.open('/consultation/my-consultation', '_blank')
    return
  }

  try {
    // 获取会诊详情
    const response = await getConsultationRequest(consultationId)
    console.log('会诊详情:', response.data)

    // 跳转到会诊详情页面
    const detailUrl = `/consultation/detail/${consultationId}`
    window.open(detailUrl, '_blank')

  } catch (error) {
    console.error('获取会诊详情失败:', error)
    // 如果获取详情失败，跳转到会诊列表页面
    window.open('/consultation/my-consultation', '_blank')
  }
}

// 处理通知查看
const handleNotificationView = (notification) => {
  console.log('查看通知详情:', notification)

  // 调用查看会诊详情处理
  handleViewConsultation(notification)

  // 确认通知
  if (notification.notificationId) {
    acknowledgeNotificationReceived(notification.notificationId)
  }
}

// 从通知中提取会诊申请ID
const extractConsultationId = (notification) => {
  // 尝试从多个可能的字段中提取ID
  return notification.consultationId ||
         notification.requestId ||
         notification.businessId ||
         notification.relatedId ||
         (notification.url && extractIdFromUrl(notification.url))
}

// 从URL中提取ID
const extractIdFromUrl = (url) => {
  if (!url) return null

  // 匹配URL中的数字ID，如 /consultation/detail/123
  const match = url.match(/\/(\d+)(?:\?|$)/)
  return match ? parseInt(match[1]) : null
}

// 显示确认对话框
const showConfirmDialog = (title, message, type = 'warning') => {
  return new Promise((resolve) => {
    ElMessageBox.confirm(message, title, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: type,
      center: true
    }).then(() => {
      resolve(true)
    }).catch(() => {
      resolve(false)
    })
  })
}

// 显示拒绝对话框（带原因输入）
const showRejectDialog = (title, message) => {
  return new Promise((resolve) => {
    ElMessageBox.prompt('请输入拒绝原因（可选）', title, {
      confirmButtonText: '确定拒绝',
      cancelButtonText: '取消',
      inputType: 'textarea',
      inputPlaceholder: '请输入拒绝原因...',
      beforeClose: (action, instance, done) => {
        if (action === 'confirm') {
          resolve({
            confirmed: true,
            reason: instance.inputValue || '未提供拒绝原因'
          })
        } else {
          resolve({
            confirmed: false,
            reason: null
          })
        }
        done()
      }
    }).catch(() => {
      resolve({
        confirmed: false,
        reason: null
      })
    })
  })
}

// 断开连接
const disconnect = () => {
  if (eventSource.value) {
    eventSource.value.close()
    eventSource.value = null
  }
  
  if (reconnectInterval.value) {
    clearTimeout(reconnectInterval.value)
    reconnectInterval.value = null
  }
  
  if (heartbeatInterval.value) {
    clearInterval(heartbeatInterval.value)
    heartbeatInterval.value = null
  }
  
  isConnected.value = false
  console.log('通知连接已断开')
}

// 格式化时间
const formatTime = (time) => {
  return new Date(time).toLocaleString()
}

// 组件挂载
onMounted(() => {
  // 设置网络状态监听
  setupNetworkStatusMonitoring()

  // 连接到通知服务
  connectToNotificationService()

  // 定期清理本地存储
  const cleanupInterval = setInterval(cleanupLocalStorage, 60000) // 每分钟清理一次

  // 保存清理定时器引用
  onBeforeUnmount(() => {
    clearInterval(cleanupInterval)
  })
})

// 组件卸载
onBeforeUnmount(() => {
  // 断开连接
  disconnect()

  // 清理网络状态监听
  window.removeEventListener('online', handleNetworkOnline)
  window.removeEventListener('offline', handleNetworkOffline)

  if (networkStatusInterval.value) {
    clearInterval(networkStatusInterval.value)
    networkStatusInterval.value = null
  }

  // 保存待处理通知到本地存储
  savePendingNotificationsToStorage()
})

// 监听网络状态变化
watch(isOnline, (newStatus, oldStatus) => {
  if (newStatus && !oldStatus) {
    // 网络恢复
    handleNetworkOnline()
  } else if (!newStatus && oldStatus) {
    // 网络断开
    handleNetworkOffline()
  }
})

// 暴露方法给父组件
defineExpose({
  connect: connectToNotificationService,
  disconnect,
  isConnected: () => isConnected.value,
  reconnect: () => {
    disconnect()
    setTimeout(connectToNotificationService, 1000)
  }
})
</script>

<style scoped>
.reliable-notification {
  position: relative;
}

/* 通知卡片容器 */
.notification-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 2001;
  max-width: 450px;
  max-height: 80vh;
  overflow-y: auto;
  pointer-events: none;
}

.notification-container > * {
  pointer-events: auto;
  margin-bottom: 12px;
}

.connection-status {
  position: fixed;
  top: 60px;
  right: 20px;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 2000;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.3s ease;
}

.status-connected {
  background-color: #f0f9ff;
  color: #10b981;
  border: 1px solid #10b981;
}

.status-disconnected {
  background-color: #fef2f2;
  color: #ef4444;
  border: 1px solid #ef4444;
}

.status-reconnecting {
  background-color: #fffbeb;
  color: #f59e0b;
  border: 1px solid #f59e0b;
}

.offline-notifications {
  max-height: 400px;
  overflow-y: auto;
}

.notification-list {
  margin-top: 16px;
}

.notification-item {
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin-bottom: 8px;
  background-color: #f9fafb;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.notification-title {
  font-weight: 500;
  color: #374151;
}

.notification-time {
  font-size: 12px;
  color: #6b7280;
}

.notification-content {
  color: #4b5563;
  font-size: 14px;
  line-height: 1.4;
}

/* 优先级样式 */
:deep(.notification-urgent) {
  border-left: 4px solid #ef4444 !important;
  background-color: #fef2f2 !important;
}

:deep(.notification-urgent .el-notification__title) {
  color: #ef4444 !important;
  font-weight: bold !important;
}

:deep(.notification-high) {
  border-left: 4px solid #f59e0b !important;
  background-color: #fffbeb !important;
}

:deep(.notification-high .el-notification__title) {
  color: #f59e0b !important;
  font-weight: 600 !important;
}

:deep(.notification-normal) {
  border-left: 4px solid #3b82f6 !important;
}

:deep(.notification-low) {
  border-left: 4px solid #6b7280 !important;
  opacity: 0.8;
}

:deep(.notification-low .el-notification__title) {
  color: #6b7280 !important;
}

/* 紧急通知闪烁效果 */
:deep(.notification-urgent) {
  animation: urgent-blink 1s infinite alternate;
}

@keyframes urgent-blink {
  0% {
    box-shadow: 0 0 5px rgba(239, 68, 68, 0.3);
  }
  100% {
    box-shadow: 0 0 15px rgba(239, 68, 68, 0.6);
  }
}
</style>
