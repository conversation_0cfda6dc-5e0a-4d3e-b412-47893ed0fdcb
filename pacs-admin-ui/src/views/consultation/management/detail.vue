<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>会诊申请详情</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回</el-button>
      </div>
      
      <div v-loading="loading">
        <!-- 基本信息 -->
        <el-descriptions title="基本信息" :column="2" border>
          <el-descriptions-item label="申请编号">{{ form.requestNo }}</el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ parseTime(form.requestTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
          <el-descriptions-item label="申请医生">{{ form.requesterName }}</el-descriptions-item>
          <el-descriptions-item label="申请科室">{{ form.requesterDept }}</el-descriptions-item>
          <el-descriptions-item label="会诊医生">{{ form.consultantName }}</el-descriptions-item>
          <el-descriptions-item label="会诊科室">{{ form.consultantDept }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ form.consultantPhone }}</el-descriptions-item>
          <el-descriptions-item label="紧急程度">
            <el-tag v-if="form.urgencyLevel === 'URGENT'" type="danger" size="mini">紧急</el-tag>
            <el-tag v-else-if="form.urgencyLevel === 'NORMAL'" type="primary" size="mini">普通</el-tag>
            <el-tag v-else-if="form.urgencyLevel === 'LOW'" type="info" size="mini">非紧急</el-tag>
            <span v-else>{{ form.urgencyLevel }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="当前状态">
            <el-tag v-if="form.status === 'PENDING'" type="warning" size="mini">待处理</el-tag>
            <el-tag v-else-if="form.status === 'ACCEPTED'" type="primary" size="mini">已接受</el-tag>
            <el-tag v-else-if="form.status === 'REJECTED'" type="danger" size="mini">已拒绝</el-tag>
            <el-tag v-else-if="form.status === 'COMPLETED'" type="success" size="mini">已完成</el-tag>
            <el-tag v-else-if="form.status === 'CANCELLED'" type="info" size="mini">已取消</el-tag>
            <span v-else>{{ form.status }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="期望完成时间">{{ parseTime(form.expectedCompletionTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        </el-descriptions>

        <!-- 患者信息 -->
        <el-descriptions title="患者信息" :column="2" border style="margin-top: 20px;" v-if="form.patientStudy">
          <el-descriptions-item label="患者姓名">{{ form.patientStudy.patientName }}</el-descriptions-item>
          <el-descriptions-item label="患者ID">{{ form.patientStudy.patientId }}</el-descriptions-item>
          <el-descriptions-item label="检查号">{{ form.studyId }}</el-descriptions-item>
          <el-descriptions-item label="检查部位">{{ form.patientStudy.bodyPart }}</el-descriptions-item>
          <el-descriptions-item label="检查类型">{{ form.patientStudy.modality }}</el-descriptions-item>
          <el-descriptions-item label="检查时间">{{ parseTime(form.patientStudy.studyTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        </el-descriptions>

        <!-- 申请信息 -->
        <el-descriptions title="申请信息" :column="1" border style="margin-top: 20px;">
          <el-descriptions-item label="申请原因">
            <div style="white-space: pre-wrap;">{{ form.requestReason }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="临床症状" v-if="form.clinicalSymptoms">
            <div style="white-space: pre-wrap;">{{ form.clinicalSymptoms }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="既往病史" v-if="form.medicalHistory">
            <div style="white-space: pre-wrap;">{{ form.medicalHistory }}</div>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 会诊结果 -->
        <el-descriptions title="会诊结果" :column="1" border style="margin-top: 20px;" v-if="form.status !== 'PENDING'">
          <el-descriptions-item label="响应时间" v-if="form.responseTime">{{ parseTime(form.responseTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
          <el-descriptions-item label="会诊意见" v-if="form.consultationOpinion">
            <div style="white-space: pre-wrap;">{{ form.consultationOpinion }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="诊断结果" v-if="form.diagnosisResult">
            <div style="white-space: pre-wrap;">{{ form.diagnosisResult }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="治疗建议" v-if="form.treatmentSuggestion">
            <div style="white-space: pre-wrap;">{{ form.treatmentSuggestion }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="完成时间" v-if="form.completionTime">{{ parseTime(form.completionTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
          <el-descriptions-item label="拒绝原因" v-if="form.status === 'REJECTED' && form.rejectReason">
            <div style="white-space: pre-wrap;">{{ form.rejectReason }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="取消原因" v-if="form.status === 'CANCELLED' && form.cancelReason">
            <div style="white-space: pre-wrap;">{{ form.cancelReason }}</div>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 操作日志 -->
        <el-card style="margin-top: 20px;">
          <div slot="header">
            <span>操作日志</span>
          </div>
          <el-timeline>
            <el-timeline-item
              v-for="log in auditLogs"
              :key="log.id"
              :timestamp="parseTime(log.operationTime, '{y}-{m}-{d} {h}:{i}:{s}')"
              placement="top"
            >
              <el-card>
                <h4>{{ log.operationType }}</h4>
                <p>操作人：{{ log.operatorName }}</p>
                <p v-if="log.description">{{ log.description }}</p>
                <p v-if="log.oldStatus && log.newStatus">状态变更：{{ log.oldStatus }} → {{ log.newStatus }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getConsultationRequest, getConsultationAuditLogs } from "@/api/consultation/request";

export default {
  name: "ConsultationDetail",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 表单参数
      form: {},
      // 操作日志
      auditLogs: []
    };
  },
  created() {
    const id = this.$route.query.id;
    if (id) {
      this.getDetail(id);
      this.getAuditLogs(id);
    } else {
      this.$modal.msgError("缺少必要参数");
      this.goBack();
    }
  },
  methods: {
    /** 获取详情 */
    getDetail(id) {
      this.loading = true;
      getConsultationRequest(id).then(response => {
        this.form = response.data;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
        this.$modal.msgError("获取详情失败");
      });
    },
    /** 获取操作日志 */
    getAuditLogs(id) {
      getConsultationAuditLogs(id).then(response => {
        this.auditLogs = response.data || [];
      }).catch(() => {
        this.auditLogs = [];
      });
    },
    /** 返回 */
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style scoped>
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both
}

.box-card {
  margin: 20px;
}

.el-descriptions {
  margin-top: 20px;
}

.el-descriptions__title {
  font-weight: bold;
  font-size: 16px;
}
</style>
