<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>会诊通知测试</span>
      </div>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <div slot="header">连接状态</div>
            <div>
              <el-tag :type="isConnected ? 'success' : 'danger'">
                {{ isConnected ? '已连接' : '未连接' }}
              </el-tag>
              <el-button 
                type="primary" 
                size="small" 
                @click="connectNotification"
                :disabled="isConnected"
                style="margin-left: 10px;"
              >
                连接
              </el-button>
              <el-button 
                type="danger" 
                size="small" 
                @click="disconnectNotification"
                :disabled="!isConnected"
                style="margin-left: 10px;"
              >
                断开
              </el-button>
            </div>
            
            <div style="margin-top: 20px;">
              <el-statistic title="在线用户数" :value="onlineUserCount" />
              <el-button 
                type="info" 
                size="small" 
                @click="refreshOnlineCount"
                style="margin-top: 10px;"
              >
                刷新
              </el-button>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card>
            <div slot="header">测试发送通知</div>
            <el-form :model="testForm" label-width="100px">
              <el-form-item label="接收用户ID">
                <el-input v-model="testForm.userId" placeholder="请输入用户ID" />
              </el-form-item>
              <el-form-item label="通知类型">
                <el-select v-model="testForm.type" placeholder="请选择通知类型">
                  <el-option label="会诊申请" value="REQUEST" />
                  <el-option label="申请接受" value="ACCEPT" />
                  <el-option label="申请拒绝" value="REJECT" />
                  <el-option label="会诊完成" value="COMPLETE" />
                </el-select>
              </el-form-item>
              <el-form-item label="通知标题">
                <el-input v-model="testForm.title" placeholder="请输入通知标题" />
              </el-form-item>
              <el-form-item label="通知内容">
                <el-input 
                  v-model="testForm.content" 
                  type="textarea" 
                  :rows="3"
                  placeholder="请输入通知内容" 
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="sendTestNotification">发送测试通知</el-button>
                <el-button type="success" @click="sendBackendTestNotification" style="margin-left: 10px;">后端测试通知</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
      </el-row>
      
      <el-card style="margin-top: 20px;">
        <div slot="header">通知日志</div>
        <div class="notification-log" ref="logContainer">
          <div 
            v-for="(log, index) in notificationLogs" 
            :key="index"
            :class="['log-item', log.type]"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-type">[{{ log.type.toUpperCase() }}]</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
        <el-button 
          type="warning" 
          size="small" 
          @click="clearLogs"
          style="margin-top: 10px;"
        >
          清空日志
        </el-button>
      </el-card>
    </el-card>
    
    <!-- 会诊通知组件 -->
    <consultation-notification 
      ref="consultationNotificationRef"
      :auto-connect="false"
      @notification-received="handleNotificationReceived"
    />
  </div>
</template>

<script>
import { getOnlineCount, sendNotificationToUser, testSendNotification } from '@/api/consultation/notification'
import ConsultationNotification from '@/components/ConsultationNotification/index.vue'

export default {
  name: "NotificationTest",
  components: {
    ConsultationNotification
  },
  data() {
    return {
      isConnected: false,
      onlineUserCount: 0,
      testForm: {
        userId: '',
        type: 'REQUEST',
        title: '测试通知',
        content: '这是一条测试通知消息'
      },
      notificationLogs: []
    }
  },
  mounted() {
    this.refreshOnlineCount()
    this.addLog('info', '页面已加载，可以开始测试会诊通知功能')
  },
  methods: {
    // 连接通知服务
    async connectNotification() {
      try {
        await this.$refs.consultationNotificationRef.connect()
        this.isConnected = true
        this.addLog('success', '成功连接到会诊通知服务')
      } catch (error) {
        this.addLog('error', '连接会诊通知服务失败: ' + error.message)
      }
    },
    
    // 断开通知服务
    disconnectNotification() {
      this.$refs.consultationNotificationRef.disconnect()
      this.isConnected = false
      this.addLog('warning', '已断开会诊通知服务连接')
    },
    
    // 刷新在线用户数
    async refreshOnlineCount() {
      try {
        const response = await getOnlineCount()
        this.onlineUserCount = response.data
        this.addLog('info', `当前在线用户数: ${this.onlineUserCount}`)
      } catch (error) {
        this.addLog('error', '获取在线用户数失败: ' + error.message)
      }
    },
    
    // 发送测试通知
    async sendTestNotification() {
      if (!this.testForm.userId) {
        this.$message.warning('请输入接收用户ID')
        return
      }

      try {
        const message = {
          type: this.testForm.type,
          title: this.testForm.title,
          content: this.testForm.content,
          patientName: '测试患者',
          requesterName: '测试医生',
          urgencyLevel: 'NORMAL',
          requestNo: 'TEST-' + Date.now(),
          timestamp: Date.now()
        }

        const response = await sendNotificationToUser(this.testForm.userId, message)
        if (response.code === 200) {
          this.addLog('success', `测试通知发送成功，接收用户ID: ${this.testForm.userId}`)
        } else {
          this.addLog('error', `测试通知发送失败: ${response.msg}`)
        }
      } catch (error) {
        this.addLog('error', '发送测试通知异常: ' + error.message)
      }
    },

    // 发送后端测试通知
    async sendBackendTestNotification() {
      if (!this.testForm.userId) {
        this.$message.warning('请输入接收用户ID')
        return
      }

      try {
        const response = await testSendNotification(this.testForm.userId)
        if (response.code === 200) {
          this.addLog('success', `后端测试通知发送成功: ${response.msg}`)
        } else {
          this.addLog('error', `后端测试通知发送失败: ${response.msg}`)
        }
      } catch (error) {
        this.addLog('error', '发送后端测试通知异常: ' + error.message)
      }
    },
    
    // 处理接收到的通知
    handleNotificationReceived(notification) {
      this.addLog('success', `收到通知: ${notification.title} - ${notification.content}`)
    },
    
    // 添加日志
    addLog(type, message) {
      const log = {
        type,
        message,
        time: new Date().toLocaleTimeString()
      }
      this.notificationLogs.unshift(log)
      
      // 限制日志数量
      if (this.notificationLogs.length > 100) {
        this.notificationLogs = this.notificationLogs.slice(0, 100)
      }
      
      // 自动滚动到顶部
      this.$nextTick(() => {
        if (this.$refs.logContainer) {
          this.$refs.logContainer.scrollTop = 0
        }
      })
    },
    
    // 清空日志
    clearLogs() {
      this.notificationLogs = []
      this.addLog('info', '日志已清空')
    }
  }
}
</script>

<style scoped>
.notification-log {
  height: 300px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  background-color: #f8f9fa;
}

.log-item {
  margin-bottom: 8px;
  padding: 5px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-item.info {
  background-color: #e1f5fe;
  color: #01579b;
}

.log-item.success {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.log-item.warning {
  background-color: #fff3e0;
  color: #ef6c00;
}

.log-item.error {
  background-color: #ffebee;
  color: #c62828;
}

.log-time {
  color: #666;
  margin-right: 8px;
}

.log-type {
  font-weight: bold;
  margin-right: 8px;
}

.log-message {
  word-break: break-all;
}
</style>
