<template>
  <div v-loading="loading">
    <el-row :gutter="20">
      <!-- 左侧：患者信息和影像查看器 -->
      <el-col :span="14">
        <el-card class="patient-info">
          <div slot="header">
            <span>患者信息</span>
          </div>
          <el-descriptions :column="2" border v-if="consultation.patientStudy">
            <el-descriptions-item label="患者姓名">{{ consultation.patientStudy.patientName }}</el-descriptions-item>
            <el-descriptions-item label="患者ID">{{ consultation.patientStudy.patientId }}</el-descriptions-item>
            <el-descriptions-item label="性别">{{ consultation.patientStudy.patientSex }}</el-descriptions-item>
            <el-descriptions-item label="年龄">{{ consultation.patientStudy.patientAge }}</el-descriptions-item>
            <el-descriptions-item label="检查类型">{{ consultation.patientStudy.modality }}</el-descriptions-item>
            <el-descriptions-item label="检查部位">{{ consultation.patientStudy.bodyPart }}</el-descriptions-item>
            <el-descriptions-item label="检查时间">{{ parseTime(consultation.patientStudy.studyTime) }}</el-descriptions-item>
            <el-descriptions-item label="检查描述">{{ consultation.patientStudy.studyDescription }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
        
        <el-card class="image-viewer" style="margin-top: 20px;">
          <div slot="header">
            <span>影像查看</span>
            <el-button
              style="float: right; padding: 3px 0"
              type="primary"
              link
              @click="openImageViewer"
            >
              在新窗口中打开
            </el-button>
          </div>
          <div style="height: 400px; background: #f5f5f5; display: flex; align-items: center; justify-content: center;">
            <span style="color: #909399;">影像查看器占位</span>
          </div>
        </el-card>
      </el-col>
      
      <!-- 右侧：会诊表单 -->
      <el-col :span="10">
        <el-card class="consultation-form">
          <div slot="header">
            <span>会诊诊断</span>
          </div>
          
          <!-- 申请信息 -->
          <div style="margin-bottom: 20px;">
            <h4>申请信息</h4>
            <el-descriptions :column="1" size="small">
              <el-descriptions-item label="申请编号">{{ consultation.requestNo }}</el-descriptions-item>
              <el-descriptions-item label="申请医生">{{ consultation.requesterName }}</el-descriptions-item>
              <el-descriptions-item label="申请原因">{{ consultation.requestReason }}</el-descriptions-item>
            </el-descriptions>
            <div style="margin-top: 10px;">
              <strong>病情描述：</strong>
              <p style="margin: 5px 0; padding: 10px; background: #f5f7fa; border-radius: 4px;">
                {{ consultation.requestDescription }}
              </p>
            </div>
          </div>
          
          <el-divider></el-divider>
          
          <!-- 会诊表单 -->
          <el-form ref="form" :model="form" :rules="rules" label-width="100px">
            <el-form-item label="影像所见" prop="consultationFindings">
              <el-input
                v-model="form.consultationFindings"
                type="textarea"
                :rows="6"
                placeholder="请输入影像所见"
              />
            </el-form-item>
            
            <el-form-item label="影像意见" prop="consultationOpinion">
              <el-input
                v-model="form.consultationOpinion"
                type="textarea"
                :rows="4"
                placeholder="请输入影像意见"
              />
            </el-form-item>
            
            <el-form-item label="会诊建议" prop="consultantSuggestion">
              <el-input
                v-model="form.consultantSuggestion"
                type="textarea"
                :rows="3"
                placeholder="请输入会诊建议"
              />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="handleSubmit" :loading="submitting">完成会诊</el-button>
              <el-button @click="handleCancel">取消</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getConsultationRequest, completeConsultation } from "@/api/consultation/request";

export default {
  name: "ConsultationDiagnose",
  props: {
    consultationId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      loading: false,
      submitting: false,
      consultation: {},
      form: {
        consultationFindings: '',
        consultationOpinion: '',
        consultantSuggestion: ''
      },
      rules: {
        consultationFindings: [
          { required: true, message: "影像所见不能为空", trigger: "blur" }
        ],
        consultationOpinion: [
          { required: true, message: "影像意见不能为空", trigger: "blur" }
        ],
        consultantSuggestion: [
          { required: true, message: "会诊建议不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getDetail();
  },
  methods: {
    /** 获取详情 */
    getDetail() {
      this.loading = true;
      getConsultationRequest(this.consultationId).then(response => {
        this.consultation = response.data;
        // 如果已有会诊结果，填充表单
        if (this.consultation.consultationFindings) {
          this.form.consultationFindings = this.consultation.consultationFindings;
        }
        if (this.consultation.consultationOpinion) {
          this.form.consultationOpinion = this.consultation.consultationOpinion;
        }
        if (this.consultation.consultantSuggestion) {
          this.form.consultantSuggestion = this.consultation.consultantSuggestion;
        }
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    /** 打开影像查看器 */
    openImageViewer() {
      if (this.consultation.studyId) {
        const url = `${process.env.VUE_APP_BASE_API}/dicom/viewer?studyId=${this.consultation.studyId}`;
        window.open(url, '_blank');
      }
    },
    /** 提交会诊结果 */
    handleSubmit() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.submitting = true;
          completeConsultation(this.consultationId, this.form).then(response => {
            this.$modal.msgSuccess("会诊完成");
            this.submitting = false;
            this.$emit('refresh');
            this.$emit('close');
          }).catch(() => {
            this.submitting = false;
          });
        }
      });
    },
    /** 取消 */
    handleCancel() {
      this.$emit('close');
    }
  }
};
</script>

<style scoped>
.patient-info, .consultation-form {
  margin-bottom: 20px;
}

.image-viewer {
  min-height: 450px;
}

h4 {
  margin: 10px 0 5px 0;
  color: #303133;
}

p {
  margin: 5px 0;
  color: #606266;
  line-height: 1.5;
}
</style>
