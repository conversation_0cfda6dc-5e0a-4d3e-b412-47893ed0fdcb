<template>
  <div v-loading="loading">
    <el-row :gutter="20">
      <!-- 左侧：患者信息和影像查看器 -->
      <el-col :span="14">
        <el-card class="patient-info">
          <div slot="header">
            <span>患者信息</span>
          </div>
          <el-descriptions :column="2" border v-if="consultation.patientStudy">
            <el-descriptions-item label="患者姓名">{{ consultation.patientStudy.patientName }}</el-descriptions-item>
            <el-descriptions-item label="患者ID">{{ consultation.patientStudy.patientId }}</el-descriptions-item>
            <el-descriptions-item label="性别">{{ consultation.patientStudy.patientSex }}</el-descriptions-item>
            <el-descriptions-item label="年龄">{{ consultation.patientStudy.patientAge }}</el-descriptions-item>
            <el-descriptions-item label="检查类型">{{ consultation.patientStudy.modality }}</el-descriptions-item>
            <el-descriptions-item label="检查部位">{{ consultation.patientStudy.bodyPart }}</el-descriptions-item>
            <el-descriptions-item label="检查时间">{{ parseTime(consultation.patientStudy.studyTime) }}</el-descriptions-item>
            <el-descriptions-item label="检查描述">{{ consultation.patientStudy.studyDescription }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
        
        <el-card class="image-viewer" style="margin-top: 20px;">
          <div slot="header">
            <span>影像查看</span>
            <el-button
              style="float: right; padding: 3px 0"
              type="primary"
              link
              @click="openImageViewer"
            >
              在新窗口中打开
            </el-button>
          </div>
          <div style="height: 400px; background: #f5f5f5; display: flex; align-items: center; justify-content: center;">
            <span style="color: #909399;">影像查看器占位</span>
          </div>
        </el-card>
      </el-col>
      
      <!-- 右侧：会诊信息 -->
      <el-col :span="10">
        <el-card class="consultation-info">
          <div slot="header">
            <span>会诊信息</span>
          </div>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="申请编号">{{ consultation.requestNo }}</el-descriptions-item>
            <el-descriptions-item label="申请医生">{{ consultation.requesterName }}</el-descriptions-item>
            <el-descriptions-item label="会诊医生">{{ consultation.consultantName }}</el-descriptions-item>
            <el-descriptions-item label="紧急程度">
              <el-tag :type="getUrgencyTagType(consultation.urgencyLevel)" size="small">
                {{ getUrgencyText(consultation.urgencyLevel) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusTagType(consultation.status)" size="small">
                {{ getStatusText(consultation.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="申请时间">{{ parseTime(consultation.requestTime) }}</el-descriptions-item>
            <el-descriptions-item label="响应时间" v-if="consultation.responseTime">{{ parseTime(consultation.responseTime) }}</el-descriptions-item>
            <el-descriptions-item label="完成时间" v-if="consultation.completionTime">{{ parseTime(consultation.completionTime) }}</el-descriptions-item>
            <el-descriptions-item label="期望完成时间" v-if="consultation.expectedCompletionTime">{{ parseTime(consultation.expectedCompletionTime) }}</el-descriptions-item>
          </el-descriptions>
          
          <div style="margin-top: 20px;">
            <h4>申请原因</h4>
            <p>{{ consultation.requestReason || '-' }}</p>
          </div>
          
          <div style="margin-top: 20px;">
            <h4>病情描述</h4>
            <p>{{ consultation.requestDescription || '-' }}</p>
          </div>
          
          <!-- 会诊结果 -->
          <div v-if="consultation.status === 'COMPLETED'" style="margin-top: 20px;">
            <h4>会诊结果</h4>
            <el-divider></el-divider>
            
            <div style="margin-bottom: 15px;">
              <h5>影像所见</h5>
              <p>{{ consultation.consultationFindings || '-' }}</p>
            </div>
            
            <div style="margin-bottom: 15px;">
              <h5>影像意见</h5>
              <p>{{ consultation.consultationOpinion || '-' }}</p>
            </div>
            
            <div style="margin-bottom: 15px;">
              <h5>会诊建议</h5>
              <p>{{ consultation.consultantSuggestion || '-' }}</p>
            </div>
          </div>
          
          <!-- 操作按钮 -->
          <div v-if="showActions && consultation.status === 'ACCEPTED'" style="margin-top: 20px;">
            <el-button type="primary" @click="handleConsult">开始会诊</el-button>
          </div>
        </el-card>
        
        <!-- 操作日志 -->
        <el-card style="margin-top: 20px;">
          <div slot="header">
            <span>操作日志</span>
          </div>
          <el-timeline>
            <el-timeline-item
              v-for="log in auditLogs"
              :key="log.id"
              :timestamp="parseTime(log.operationTime)"
              placement="top"
            >
              <el-card>
                <h4>{{ log.operationTypeText }}</h4>
                <p>操作人：{{ log.operationUserName }}</p>
                <p v-if="log.operationDescription">{{ log.operationDescription }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getConsultationRequest, getConsultationAuditLogs } from "@/api/consultation/request";

export default {
  name: "ConsultationDetail",
  props: {
    consultationId: {
      type: [String, Number],
      required: true
    },
    showActions: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      consultation: {},
      auditLogs: []
    };
  },
  created() {
    this.getDetail();
    this.getAuditLogs();
  },
  methods: {
    /** 获取详情 */
    getDetail() {
      this.loading = true;
      getConsultationRequest(this.consultationId).then(response => {
        this.consultation = response.data;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    /** 获取操作日志 */
    getAuditLogs() {
      getConsultationAuditLogs(this.consultationId).then(response => {
        this.auditLogs = response.data || [];
      });
    },
    /** 打开影像查看器 */
    openImageViewer() {
      if (this.consultation.studyId) {
        const url = `${process.env.VUE_APP_BASE_API}/dicom/viewer?studyId=${this.consultation.studyId}`;
        window.open(url, '_blank');
      }
    },
    /** 开始会诊 */
    handleConsult() {
      this.$emit('consult', this.consultation);
    },
    /** 获取紧急程度标签类型 */
    getUrgencyTagType(urgencyLevel) {
      const tagTypeMap = {
        'URGENT': 'danger',
        'NORMAL': 'warning',
        'LOW': 'info'
      };
      return tagTypeMap[urgencyLevel] || 'info';
    },
    /** 获取紧急程度文本 */
    getUrgencyText(urgencyLevel) {
      const textMap = {
        'URGENT': '紧急',
        'NORMAL': '普通',
        'LOW': '非紧急'
      };
      return textMap[urgencyLevel] || urgencyLevel;
    },
    /** 获取状态标签类型 */
    getStatusTagType(status) {
      const tagTypeMap = {
        'PENDING': 'warning',
        'ACCEPTED': 'primary',
        'REJECTED': 'danger',
        'COMPLETED': 'success',
        'CANCELLED': 'info'
      };
      return tagTypeMap[status] || 'info';
    },
    /** 获取状态文本 */
    getStatusText(status) {
      const textMap = {
        'PENDING': '待处理',
        'ACCEPTED': '已接受',
        'REJECTED': '已拒绝',
        'COMPLETED': '已完成',
        'CANCELLED': '已取消'
      };
      return textMap[status] || status;
    }
  }
};
</script>

<style scoped>
.patient-info, .consultation-info {
  margin-bottom: 20px;
}

.image-viewer {
  min-height: 450px;
}

h4 {
  margin: 10px 0 5px 0;
  color: #303133;
}

h5 {
  margin: 10px 0 5px 0;
  color: #606266;
}

p {
  margin: 5px 0;
  color: #606266;
  line-height: 1.5;
}
</style>
