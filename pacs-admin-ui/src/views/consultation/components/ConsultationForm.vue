<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="患者ID" prop="patientId">
            <el-input v-model="form.patientId" placeholder="请输入患者ID" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="检查ID" prop="studyId">
            <el-input v-model="form.studyId" placeholder="请输入检查ID" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="会诊医生" prop="consultantId">
            <el-select v-model="form.consultantId" placeholder="请选择会诊医生" filterable style="width: 100%">
              <el-option
                v-for="doctor in doctorList"
                :key="doctor.userId"
                :label="doctor.nickName"
                :value="doctor.userId">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="紧急程度" prop="urgencyLevel">
            <el-radio-group v-model="form.urgencyLevel">
              <el-radio label="URGENT">紧急</el-radio>
              <el-radio label="NORMAL">普通</el-radio>
              <el-radio label="LOW">非紧急</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="期望完成时间" prop="expectedCompletionTime">
        <el-date-picker
          v-model="form.expectedCompletionTime"
          type="datetime"
          placeholder="选择期望完成时间"
          style="width: 100%">
        </el-date-picker>
      </el-form-item>
      
      <el-form-item label="申请原因" prop="requestReason">
        <el-input v-model="form.requestReason" type="textarea" :rows="3" placeholder="请输入申请原因" />
      </el-form-item>
      
      <el-form-item label="病情描述" prop="requestDescription">
        <el-input v-model="form.requestDescription" type="textarea" :rows="5" placeholder="请输入病情描述" />
      </el-form-item>
    </el-form>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </div>
  </div>
</template>

<script>
import { listUser } from "@/api/system/user";

export default {
  name: "ConsultationForm",
  props: {
    formData: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        id: null,
        patientId: null,
        studyId: null,
        checkId: null,
        consultantId: null,
        requestReason: null,
        requestDescription: null,
        urgencyLevel: "NORMAL",
        expectedCompletionTime: null
      },
      doctorList: [],
      rules: {
        patientId: [
          { required: true, message: "患者ID不能为空", trigger: "blur" }
        ],
        studyId: [
          { required: true, message: "检查ID不能为空", trigger: "blur" }
        ],
        consultantId: [
          { required: true, message: "会诊医生不能为空", trigger: "change" }
        ],
        requestReason: [
          { required: true, message: "申请原因不能为空", trigger: "blur" }
        ],
        requestDescription: [
          { required: true, message: "病情描述不能为空", trigger: "blur" }
        ]
      }
    };
  },
  watch: {
    formData: {
      handler(newVal) {
        if (newVal) {
          this.form = { ...this.form, ...newVal };
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.getDoctorList();
  },
  methods: {
    /** 获取医生列表 */
    getDoctorList() {
      listUser({ pageNum: 1, pageSize: 1000 }).then(response => {
        this.doctorList = response.rows || [];
      });
    },
    /** 提交 */
    handleSubmit() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.$emit('submit', this.form);
        }
      });
    },
    /** 取消 */
    handleCancel() {
      this.$emit('cancel');
    }
  }
};
</script>
