<template>
  <div class="app-container">
    <!-- 紧凑搜索栏 -->
    <div v-show="showSearch" class="compact-search-bar">
      <el-form :model="queryParams" ref="queryForm" :inline="true" size="small" class="compact-form">
        <el-form-item label="申请编号" prop="requestNo">
          <el-input
            v-model="queryParams.requestNo"
            placeholder="请输入申请编号"
            clearable
            style="width: 160px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="患者ID" prop="patientId">
          <el-input
            v-model="queryParams.patientId"
            placeholder="请输入患者ID"
            clearable
            style="width: 160px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 120px">
            <el-option label="待处理" value="PENDING" />
            <el-option label="已接受" value="ACCEPTED" />
            <el-option label="已拒绝" value="REJECTED" />
            <el-option label="已完成" value="COMPLETED" />
            <el-option label="已取消" value="CANCELLED" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="refresh" size="small" @click="resetQuery">重置</el-button>
          <el-button type="primary" link icon="more" size="small" @click="showAdvancedSearch = true">更多筛选</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 高级搜索弹窗 -->
    <el-dialog
      v-model="showAdvancedSearch"
      title="高级搜索"
      width="700px"
      :before-close="handleAdvancedClose"
    >
      <el-form :model="advancedParams" label-width="100px">
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="申请编号">
              <el-input
                v-model="advancedParams.requestNo"
                placeholder="请输入申请编号"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="患者ID">
              <el-input
                v-model="advancedParams.patientId"
                placeholder="请输入患者ID"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="会诊医生">
              <el-input
                v-model="advancedParams.consultantName"
                placeholder="请输入会诊医生"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="状态">
              <el-select v-model="advancedParams.status" placeholder="请选择状态" clearable style="width: 100%">
                <el-option label="待处理" value="PENDING" />
                <el-option label="已接受" value="ACCEPTED" />
                <el-option label="已拒绝" value="REJECTED" />
                <el-option label="已完成" value="COMPLETED" />
                <el-option label="已取消" value="CANCELLED" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="紧急程度">
              <el-select v-model="advancedParams.urgencyLevel" placeholder="请选择紧急程度" clearable style="width: 100%">
                <el-option label="紧急" value="URGENT" />
                <el-option label="普通" value="NORMAL" />
                <el-option label="非紧急" value="LOW" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请时间">
              <el-date-picker
                v-model="advancedDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleAdvancedClose">取消</el-button>
          <el-button @click="resetAdvancedSearch">重置</el-button>
          <el-button type="primary" @click="handleAdvancedSearch">搜索</el-button>
        </div>
      </template>
    </el-dialog>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="plus"
          size="small"
          @click="handleAdd"
          v-hasPermi="['consultation:request:create']"
        >发起会诊</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="delete"
          size="small"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['consultation:request:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="consultationList" @selection-change="handleSelectionChange" class="consultation-table">
      <el-table-column type="selection" width="50" align="center" />

      <!-- 基本信息列 -->
      <el-table-column label="申请信息" width="280" align="left">
        <template #default="scope">
          <div v-if="scope && scope.row" class="info-cell">
            <div class="info-row primary">
              <span class="request-no">{{ scope.row.requestNo }}</span>
              <el-tag :type="getUrgencyTagType(scope.row.urgencyLevel)" size="small">
                {{ getUrgencyText(scope.row.urgencyLevel) }}
              </el-tag>
            </div>
            <div class="info-row secondary">
              <span class="consultant-info">会诊医生：{{ scope.row.consultantName || '-' }}</span>
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 患者信息列 -->
      <el-table-column label="患者信息" width="220" align="left">
        <template #default="scope">
          <div v-if="scope && scope.row" class="info-cell">
            <div class="info-row primary">
              <span class="patient-name">{{ scope.row.patientStudy?.patientName || '-' }}</span>
            </div>
            <div class="info-row secondary">
              <span class="patient-id">{{ scope.row.patientStudy?.patientId || scope.row.patientId || '-' }}</span>
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 检查信息列 -->
      <el-table-column label="检查信息" width="200" align="left">
        <template #default="scope">
          <div v-if="scope && scope.row" class="info-cell">
            <div class="info-row primary">
              <span class="modality-info">{{ scope.row.patientStudy?.modality || '-' }} - {{ scope.row.patientStudy?.bodyPart || '-' }}</span>
            </div>
            <div class="info-row secondary">
              <span class="study-id">{{ scope.row.studyId }}</span>
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 状态与时间列 -->
      <el-table-column label="状态与时间" width="160" align="center">
        <template #default="scope">
          <div v-if="scope && scope.row" class="info-cell">
            <div class="info-row primary">
              <el-tag :type="getStatusTagType(scope.row.status)" size="small">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </div>
            <div class="info-row secondary">
              <span class="time-info">{{ parseTime(scope.row.requestTime, '{m}-{d} {h}:{i}') }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <!-- 申请原因列 -->
      <el-table-column label="申请原因" min-width="200" align="left">
        <template #default="scope">
          <div v-if="scope && scope.row" class="reason-cell">
            <el-tooltip :content="scope.row.requestReason" placement="top" :disabled="!scope.row.requestReason || scope.row.requestReason.length <= 50">
              <span class="reason-text">{{ scope.row.requestReason || '-' }}</span>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column label="操作" width="120" align="center" fixed="right">
        <template #default="scope">
          <div v-if="scope && scope.row" class="action-buttons">
            <el-button
              type="primary"
              size="small"
              @click="handleView(scope.row)"
              v-hasPermi="['consultation:request:query']"
              class="action-btn"
            >
              <el-icon><View /></el-icon>
            </el-button>

            <el-dropdown @command="handleCommand" trigger="click">
              <el-button type="info" size="small" class="action-btn">
                <el-icon><More /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    :command="{action: 'edit', row: scope.row}"
                    v-if="scope.row.status === 'PENDING' && checkPermi(['consultation:request:edit'])"
                  >
                    <el-icon><Edit /></el-icon>
                    修改
                  </el-dropdown-item>
                  <el-dropdown-item
                    :command="{action: 'cancel', row: scope.row}"
                    v-if="(scope.row.status === 'PENDING' || scope.row.status === 'ACCEPTED') && checkPermi(['consultation:request:cancel'])"
                  >
                    <el-icon><Close /></el-icon>
                    取消
                  </el-dropdown-item>
                  <el-dropdown-item
                    :command="{action: 'delete', row: scope.row}"
                    v-if="checkPermi(['consultation:request:remove'])"
                    divided
                  >
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改会诊申请对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <consultation-form 
        ref="consultationForm"
        :form-data="form"
        :is-edit="isEdit"
        @submit="submitForm"
        @cancel="cancel"
      />
    </el-dialog>

    <!-- 会诊详情对话框 -->
    <el-dialog title="会诊详情" v-model="detailOpen" width="1200px" append-to-body>
      <consultation-detail 
        v-if="detailOpen"
        :consultation-id="currentId"
        @close="detailOpen = false"
      />
    </el-dialog>

    <!-- 取消会诊对话框 -->
    <el-dialog title="取消会诊申请" v-model="cancelOpen" width="500px" append-to-body>
      <el-form ref="cancelForm" :model="cancelForm" label-width="100px">
        <el-form-item label="取消原因" prop="cancelReason">
          <el-input
            v-model="cancelForm.cancelReason"
            type="textarea"
            :rows="4"
            placeholder="请输入取消原因"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelOpen = false">取 消</el-button>
        <el-button type="primary" @click="confirmCancel">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMyRequests, getConsultationRequest, createConsultationRequest, updateConsultationRequest, delConsultationRequest, cancelConsultation } from "@/api/consultation/request";
import ConsultationForm from "../components/ConsultationForm";
import ConsultationDetail from "../components/ConsultationDetail";
import { View, More, Edit, Close, Delete } from '@element-plus/icons-vue';
import { checkPermi } from "@/utils/permission";

export default {
  name: "ConsultationRequest",
  components: {
    ConsultationForm,
    ConsultationDetail,
    View,
    More,
    Edit,
    Close,
    Delete
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 显示高级搜索弹窗
      showAdvancedSearch: false,
      // 总条数
      total: 0,
      // 会诊申请表格数据
      consultationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      detailOpen: false,
      // 是否显示取消弹出层
      cancelOpen: false,
      // 是否编辑
      isEdit: false,
      // 当前ID
      currentId: null,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        requestNo: null,
        patientId: null,
        consultantName: null,
        status: null,
        urgencyLevel: null
      },
      // 高级搜索参数
      advancedParams: {
        requestNo: '',
        patientId: '',
        consultantName: '',
        status: '',
        urgencyLevel: ''
      },
      // 高级搜索日期范围
      advancedDateRange: [],
      // 表单参数
      form: {},
      // 取消表单
      cancelForm: {
        cancelReason: ""
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 权限检查方法
    checkPermi(permissions) {
      return checkPermi(permissions);
    },
    /** 查询会诊申请列表 */
    getList() {
      this.loading = true;
      listMyRequests(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.consultationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        requestNo: null,
        patientId: null,
        studyId: null,
        checkId: null,
        consultantId: null,
        requestReason: null,
        requestDescription: null,
        urgencyLevel: "NORMAL",
        expectedCompletionTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 高级搜索弹窗关闭处理 */
    handleAdvancedClose() {
      this.showAdvancedSearch = false;
    },
    /** 重置高级搜索 */
    resetAdvancedSearch() {
      this.advancedParams = {
        requestNo: '',
        patientId: '',
        consultantName: '',
        status: '',
        urgencyLevel: ''
      };
      this.advancedDateRange = [];
    },
    /** 高级搜索 */
    handleAdvancedSearch() {
      // 将高级搜索参数同步到查询参数
      this.queryParams.requestNo = this.advancedParams.requestNo;
      this.queryParams.patientId = this.advancedParams.patientId;
      this.queryParams.consultantName = this.advancedParams.consultantName;
      this.queryParams.status = this.advancedParams.status;
      this.queryParams.urgencyLevel = this.advancedParams.urgencyLevel;

      // 处理日期范围
      this.dateRange = this.advancedDateRange;

      // 关闭弹窗并执行搜索
      this.showAdvancedSearch = false;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 处理下拉菜单命令 */
    handleCommand(command) {
      const { action, row } = command;
      switch (action) {
        case 'edit':
          this.handleUpdate(row);
          break;
        case 'cancel':
          this.handleCancel(row);
          break;
        case 'delete':
          this.handleDelete(row);
          break;
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "发起会诊申请";
      this.isEdit = false;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      if (!row && (!this.ids || this.ids.length === 0)) {
        this.$modal.msgError("请选择要修改的数据");
        return;
      }

      this.reset();
      const id = row ? row.id : this.ids;
      if (!id) {
        this.$modal.msgError("无法获取要修改的数据ID");
        return;
      }

      getConsultationRequest(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改会诊申请";
        this.isEdit = true;
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      if (!row || !row.id) {
        this.$modal.msgError("无法获取要查看的数据");
        return;
      }

      this.currentId = row.id;
      this.detailOpen = true;
    },
    /** 取消按钮操作 */
    handleCancel(row) {
      if (!row || !row.id) {
        this.$modal.msgError("无法获取要取消的数据");
        return;
      }

      this.currentId = row.id;
      this.cancelForm.cancelReason = "";
      this.cancelOpen = true;
    },
    /** 确认取消 */
    confirmCancel() {
      cancelConsultation(this.currentId, this.cancelForm).then(response => {
        this.$modal.msgSuccess("取消成功");
        this.cancelOpen = false;
        this.getList();
      });
    },
    /** 提交按钮 */
    submitForm(form) {
      if (this.isEdit) {
        updateConsultationRequest(form).then(response => {
          this.$modal.msgSuccess("修改成功");
          this.open = false;
          this.getList();
        });
      } else {
        createConsultationRequest(form).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      // 参数验证
      if (!row && (!this.ids || this.ids.length === 0)) {
        this.$modal.msgError("请选择要删除的数据");
        return;
      }

      const ids = row ? row.id : this.ids;
      if (!ids) {
        this.$modal.msgError("无法获取要删除的数据ID");
        return;
      }

      this.$modal.confirm('是否确认删除会诊申请编号为"' + ids + '"的数据项？').then(function() {
        return delConsultationRequest(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 获取紧急程度标签类型 */
    getUrgencyTagType(urgencyLevel) {
      const tagTypeMap = {
        'URGENT': 'danger',
        'NORMAL': 'warning',
        'LOW': 'info'
      };
      return tagTypeMap[urgencyLevel] || 'info';
    },
    /** 获取紧急程度文本 */
    getUrgencyText(urgencyLevel) {
      const textMap = {
        'URGENT': '紧急',
        'NORMAL': '普通',
        'LOW': '非紧急'
      };
      return textMap[urgencyLevel] || urgencyLevel;
    },
    /** 获取状态标签类型 */
    getStatusTagType(status) {
      const tagTypeMap = {
        'PENDING': 'warning',
        'ACCEPTED': 'primary',
        'REJECTED': 'danger',
        'COMPLETED': 'success',
        'CANCELLED': 'info'
      };
      return tagTypeMap[status] || 'info';
    },
    /** 获取状态文本 */
    getStatusText(status) {
      const textMap = {
        'PENDING': '待处理',
        'ACCEPTED': '已接受',
        'REJECTED': '已拒绝',
        'COMPLETED': '已完成',
        'CANCELLED': '已取消'
      };
      return textMap[status] || status;
    }
  }
};
</script>

<style scoped>
/* 紧凑搜索栏样式 */
.compact-search-bar {
  background: #fafafa;
  padding: 16px 20px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.compact-form {
  margin: 0;
}

.compact-form :deep(.el-form-item) {
  margin-right: 16px;
  margin-bottom: 0;
}

.compact-form :deep(.el-form-item__label) {
  color: #606266;
  font-weight: 500;
  font-size: 14px;
}

.compact-form :deep(.el-button) {
  margin-left: 8px;
}

.compact-form :deep(.el-button--text) {
  color: #409eff;
  padding: 8px 12px;
}

/* 表格样式 */
.consultation-table {
  border-radius: 8px;
  overflow: hidden;
}

.info-cell {
  padding: 4px 0;
}

.info-row {
  line-height: 1.4;
  margin-bottom: 2px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-row.primary {
  font-weight: 500;
  color: #303133;
}

.info-row.secondary {
  font-size: 12px;
  color: #909399;
}

.request-no {
  font-weight: 600;
  margin-right: 8px;
}

.consultant-info,
.patient-name,
.modality-info {
  font-size: 13px;
}

.patient-id,
.study-id,
.time-info {
  font-size: 12px;
}

.reason-cell {
  padding: 4px 0;
}

.reason-text {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  font-size: 13px;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 6px;
  justify-content: center;
}

.action-btn {
  width: 32px;
  height: 32px;
  padding: 0;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn .el-icon {
  font-size: 16px;
}

/* 下拉菜单样式 */
.el-dropdown-menu__item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.el-dropdown-menu__item .el-icon {
  font-size: 14px;
}
</style>
