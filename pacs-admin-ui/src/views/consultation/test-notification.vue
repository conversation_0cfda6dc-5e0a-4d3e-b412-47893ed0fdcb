<template>
  <div class="test-notification-container">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>通知功能测试</span>
          <el-tag :type="connectionStatus === 'connected' ? 'success' : 'danger'">
            {{ connectionStatusText }}
          </el-tag>
        </div>
      </template>

      <!-- 连接状态 -->
      <el-row :gutter="20" class="status-row">
        <el-col :span="8">
          <el-statistic title="连接状态" :value="connectionStatus" />
        </el-col>
        <el-col :span="8">
          <el-statistic title="在线用户数" :value="onlineUserCount" />
        </el-col>
        <el-col :span="8">
          <el-statistic title="收到通知数" :value="receivedNotifications" />
        </el-col>
      </el-row>

      <!-- 测试按钮 -->
      <el-row :gutter="20" class="button-row">
        <el-col :span="6">
          <el-button type="primary" @click="testConnection" :loading="testing">
            测试连接
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="success" @click="sendTestNotification" :loading="sending">
            发送测试通知
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="warning" @click="createTestConsultation" :loading="creating">
            创建测试会诊
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="info" @click="refreshStatus">
            刷新状态
          </el-button>
        </el-col>
      </el-row>

      <!-- 日志显示 -->
      <el-divider>测试日志</el-divider>
      <div class="log-container">
        <div
          v-for="(log, index) in logs"
          :key="index"
          :class="['log-item', `log-${log.type}`]"
        >
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>

      <!-- 清空日志 -->
      <el-button @click="clearLogs" size="small" style="margin-top: 10px;">
        清空日志
      </el-button>
    </el-card>

    <!-- 通知历史 -->
    <el-card class="history-card" style="margin-top: 20px;">
      <template #header>
        <span>通知历史</span>
      </template>
      
      <el-table :data="notificationHistory" style="width: 100%">
        <el-table-column prop="time" label="时间" width="180" />
        <el-table-column prop="title" label="标题" />
        <el-table-column prop="content" label="内容" />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="scope">
            <el-tag :type="getNotificationTypeColor(scope.row.type)">
              {{ scope.row.type }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { 
  connectConsultationNotification, 
  testNotificationSend,
  checkConnectionStatus,
  getNotificationList
} from '@/api/consultation/reliableNotification'

// 响应式数据
const connectionStatus = ref('disconnected')
const connectionStatusText = ref('未连接')
const onlineUserCount = ref(0)
const receivedNotifications = ref(0)
const testing = ref(false)
const sending = ref(false)
const creating = ref(false)

const logs = ref([])
const notificationHistory = ref([])

let eventSource = null

// 计算属性
const getNotificationTypeColor = (type) => {
  const colorMap = {
    'REQUEST': 'primary',
    'ACCEPT': 'success',
    'REJECT': 'warning',
    'COMPLETE': 'success',
    'CANCEL': 'info',
    'URGENT': 'danger',
    'TEST': 'info'
  }
  return colorMap[type] || 'info'
}

// 添加日志
const addLog = (message, type = 'info') => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message,
    type
  })
  
  // 限制日志数量
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
}

// 清空日志
const clearLogs = () => {
  logs.value = []
}

// 测试连接
const testConnection = async () => {
  testing.value = true
  addLog('开始测试连接...', 'info')
  
  try {
    // 建立SSE连接
    await connectToNotificationService()
    addLog('连接测试完成', 'success')
  } catch (error) {
    addLog(`连接测试失败: ${error.message}`, 'error')
  } finally {
    testing.value = false
  }
}

// 发送测试通知
const sendTestNotification = async () => {
  sending.value = true
  addLog('发送测试通知...', 'info')
  
  try {
    const testData = {
      title: '测试通知',
      content: `这是一条测试通知，发送时间：${new Date().toLocaleString()}`
    }
    
    const response = await testNotificationSend(testData)
    
    if (response.code === 200) {
      addLog('测试通知发送成功', 'success')
      ElMessage.success('测试通知发送成功')
    } else {
      addLog(`测试通知发送失败: ${response.msg}`, 'error')
      ElMessage.error('测试通知发送失败')
    }
  } catch (error) {
    addLog(`测试通知发送异常: ${error.message}`, 'error')
    ElMessage.error('测试通知发送异常')
  } finally {
    sending.value = false
  }
}

// 创建测试会诊
const createTestConsultation = async () => {
  creating.value = true
  addLog('创建测试会诊申请...', 'info')
  
  try {
    // 这里需要根据实际的会诊申请API调整
    const consultationData = {
      patientName: '测试患者',
      patientId: `TEST${Date.now()}`,
      studyId: `STUDY${Date.now()}`,
      consultantId: 2, // 需要根据实际情况调整
      urgencyLevel: 'NORMAL',
      clinicalInfo: '测试会诊申请 - 通知功能验证',
      consultationReason: '功能测试'
    }
    
    // 发送创建请求
    const response = await fetch('/consultation/request', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + (localStorage.getItem('token') || sessionStorage.getItem('token'))
      },
      body: JSON.stringify(consultationData)
    })
    
    const result = await response.json()
    
    if (result.code === 200) {
      addLog('测试会诊申请创建成功', 'success')
      ElMessage.success('测试会诊申请创建成功，请等待通知')
    } else {
      addLog(`测试会诊申请创建失败: ${result.msg}`, 'error')
      ElMessage.error('测试会诊申请创建失败')
    }
  } catch (error) {
    addLog(`创建测试会诊异常: ${error.message}`, 'error')
    ElMessage.error('创建测试会诊异常')
  } finally {
    creating.value = false
  }
}

// 刷新状态
const refreshStatus = async () => {
  addLog('刷新状态...', 'info')
  
  try {
    // 获取在线用户数量
    const response = await fetch('/consultation/reliable-notification/online-count', {
      headers: {
        'Authorization': 'Bearer ' + (localStorage.getItem('token') || sessionStorage.getItem('token'))
      }
    })
    
    const result = await response.json()
    if (result.code === 200) {
      onlineUserCount.value = result.data.onlineCount
      addLog(`在线用户数量: ${onlineUserCount.value}`, 'info')
    }
  } catch (error) {
    addLog(`刷新状态失败: ${error.message}`, 'error')
  }
}

// 连接到通知服务
const connectToNotificationService = async () => {
  try {
    addLog('建立SSE连接...', 'info')
    
    eventSource = connectConsultationNotification()
    
    // 连接成功事件
    eventSource.addEventListener('connected', (event) => {
      connectionStatus.value = 'connected'
      connectionStatusText.value = '已连接'
      addLog('SSE连接建立成功', 'success')
    })
    
    // 接收通知
    eventSource.addEventListener('consultation-notification', (event) => {
      const notification = JSON.parse(event.data)
      addLog(`收到通知: ${notification.title}`, 'success')
      
      // 添加到历史记录
      notificationHistory.value.unshift({
        time: new Date().toLocaleString(),
        title: notification.title,
        content: notification.content,
        type: notification.type || 'UNKNOWN'
      })
      
      // 显示通知
      ElNotification({
        title: notification.title,
        message: notification.content,
        type: getNotificationTypeColor(notification.type) === 'primary' ? 'info' : getNotificationTypeColor(notification.type),
        duration: 4500
      })
      
      receivedNotifications.value++
      
      // 发送确认（如果有notificationId）
      if (notification.notificationId) {
        acknowledgeNotification(notification.notificationId)
      }
    })
    
    // 连接错误处理
    eventSource.onerror = (error) => {
      connectionStatus.value = 'error'
      connectionStatusText.value = '连接错误'
      addLog('SSE连接发生错误', 'error')
    }
    
  } catch (error) {
    addLog(`建立连接失败: ${error.message}`, 'error')
    throw error
  }
}

// 确认通知
const acknowledgeNotification = async (notificationId) => {
  try {
    const response = await fetch(`/consultation/reliable-notification/acknowledge/${notificationId}`, {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer ' + (localStorage.getItem('token') || sessionStorage.getItem('token'))
      }
    })
    
    const result = await response.json()
    if (result.code === 200) {
      addLog(`通知确认成功: ${notificationId}`, 'info')
    }
  } catch (error) {
    addLog(`通知确认失败: ${error.message}`, 'error')
  }
}

// 断开连接
const disconnect = () => {
  if (eventSource) {
    eventSource.close()
    eventSource = null
    connectionStatus.value = 'disconnected'
    connectionStatusText.value = '未连接'
    addLog('SSE连接已断开', 'info')
  }
}

// 组件挂载
onMounted(() => {
  addLog('通知测试页面已加载', 'info')
  refreshStatus()
  // 自动建立连接
  connectToNotificationService()
})

// 组件卸载
onBeforeUnmount(() => {
  disconnect()
})
</script>

<style scoped>
.test-notification-container {
  padding: 20px;
}

.test-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-row {
  margin-bottom: 20px;
}

.button-row {
  margin-bottom: 20px;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  background-color: #f5f7fa;
}

.log-item {
  margin-bottom: 5px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-time {
  color: #909399;
  margin-right: 10px;
}

.log-message {
  color: #303133;
}

.log-success .log-message {
  color: #67c23a;
}

.log-error .log-message {
  color: #f56c6c;
}

.log-warning .log-message {
  color: #e6a23c;
}

.history-card {
  max-width: 1200px;
  margin: 0 auto;
}
</style>
