<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="通知类型" prop="notificationType">
        <el-select v-model="queryParams.notificationType" placeholder="请选择通知类型" clearable>
          <el-option label="短信" value="SMS" />
          <el-option label="邮件" value="EMAIL" />
          <el-option label="系统通知" value="SYSTEM" />
        </el-select>
      </el-form-item>
      <el-form-item label="发送状态" prop="sendStatus">
        <el-select v-model="queryParams.sendStatus" placeholder="请选择发送状态" clearable>
          <el-option label="成功" value="SUCCESS" />
          <el-option label="失败" value="FAILED" />
          <el-option label="待发送" value="PENDING" />
        </el-select>
      </el-form-item>
      <el-form-item label="接收人手机" prop="recipientPhone">
        <el-input
          v-model="queryParams.recipientPhone"
          placeholder="请输入接收人手机号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间" style="width: 308px">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Refresh"
          size="small"
          :disabled="multiple"
          @click="handleRetryFailed"
          v-hasPermi="['consultation:notification:retry']"
        >重试失败通知</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Connection"
          size="small"
          @click="handleCheckOnlineUsers"
        >在线用户</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="notificationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" width="80" />
      <el-table-column label="会诊申请编号" align="center" prop="consultationRequestNo" width="150" />
      <el-table-column label="通知类型" align="center" prop="notificationType" width="100">
        <template #default="scope">
          <dict-tag :options="notificationTypeOptions" :value="scope.row.notificationType"/>
        </template>
      </el-table-column>
      <el-table-column label="接收人" align="center" prop="recipientPhone" width="120" />
      <el-table-column label="发送状态" align="center" prop="sendStatus" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.sendStatus === 'SUCCESS'" type="success">成功</el-tag>
          <el-tag v-else-if="scope.row.sendStatus === 'FAILED'" type="danger">失败</el-tag>
          <el-tag v-else type="warning">待发送</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="重试次数" align="center" prop="retryCount" width="100" />
      <el-table-column label="发送时间" align="center" prop="sendTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.sendTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="错误信息" align="center" prop="errorMessage" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="View"
            @click="handleView(scope.row)"
            v-hasPermi="['consultation:notification:query']"
          >查看</el-button>
          <el-button
            v-if="scope.row.sendStatus === 'FAILED'"
            link
            type="warning"
            icon="Refresh"
            @click="handleRetry(scope.row)"
            v-hasPermi="['consultation:notification:retry']"
          >重试</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 通知详情对话框 -->
    <el-dialog title="通知详情" v-model="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" label-width="120px">
        <el-form-item label="会诊申请编号">
          <el-input v-model="form.consultationRequestNo" :disabled="true" />
        </el-form-item>
        <el-form-item label="通知类型">
          <el-input v-model="form.notificationType" :disabled="true" />
        </el-form-item>
        <el-form-item label="接收人手机号">
          <el-input v-model="form.recipientPhone" :disabled="true" />
        </el-form-item>
        <el-form-item label="模板编码">
          <el-input v-model="form.templateCode" :disabled="true" />
        </el-form-item>
        <el-form-item label="通知内容">
          <el-input v-model="form.notificationContent" type="textarea" :rows="4" :disabled="true" />
        </el-form-item>
        <el-form-item label="发送状态">
          <el-input v-model="form.sendStatus" :disabled="true" />
        </el-form-item>
        <el-form-item label="发送时间">
          <el-input v-model="form.sendTime" :disabled="true" />
        </el-form-item>
        <el-form-item label="错误信息" v-if="form.errorMessage">
          <el-input v-model="form.errorMessage" type="textarea" :rows="3" :disabled="true" />
        </el-form-item>
        <el-form-item label="重试次数">
          <el-input v-model="form.retryCount" :disabled="true" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 在线用户对话框 -->
    <el-dialog title="在线用户统计" v-model="onlineUsersOpen" width="400px" append-to-body>
      <div style="text-align: center; padding: 20px;">
        <el-statistic title="当前在线用户数" :value="onlineUserCount" />
        <div style="margin-top: 20px;">
          <el-button type="primary" @click="refreshOnlineCount">刷新</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listConsultationNotification, getConsultationNotification, retryFailedNotifications } from "@/api/consultation/notification";
import { getOnlineCount } from "@/api/consultation/notification";

export default {
  name: "ConsultationNotification",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 会诊通知表格数据
      notificationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 在线用户对话框
      onlineUsersOpen: false,
      // 在线用户数量
      onlineUserCount: 0,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        notificationType: null,
        sendStatus: null,
        recipientPhone: null
      },
      // 表单参数
      form: {},
      // 通知类型字典
      notificationTypeOptions: [
        { label: "短信", value: "SMS" },
        { label: "邮件", value: "EMAIL" },
        { label: "系统通知", value: "SYSTEM" }
      ]
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询会诊通知列表 */
    getList() {
      this.loading = true;
      listConsultationNotification(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.notificationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        consultationId: null,
        notificationType: null,
        recipientId: null,
        recipientPhone: null,
        recipientEmail: null,
        notificationContent: null,
        templateCode: null,
        sendStatus: null,
        sendTime: null,
        errorMessage: null,
        retryCount: null,
        createTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryRef");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.reset();
      const id = row.id || this.ids;
      getConsultationNotification(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "通知详情";
      });
    },
    /** 重试失败通知 */
    handleRetryFailed() {
      this.$modal.confirm('是否确认重试所有失败的通知？').then(() => {
        return retryFailedNotifications();
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("重试操作已提交");
      }).catch(() => {});
    },
    /** 重试单个通知 */
    handleRetry(row) {
      this.$modal.confirm('是否确认重试该通知？').then(() => {
        // 这里可以调用单个重试的API
        this.$modal.msgSuccess("重试操作已提交");
        this.getList();
      }).catch(() => {});
    },
    /** 查看在线用户 */
    handleCheckOnlineUsers() {
      this.refreshOnlineCount();
      this.onlineUsersOpen = true;
    },
    /** 刷新在线用户数量 */
    refreshOnlineCount() {
      getOnlineCount().then(response => {
        this.onlineUserCount = response.data;
      });
    }
  }
};
</script>
