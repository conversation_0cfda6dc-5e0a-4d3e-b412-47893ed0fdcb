<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>会诊数据调试</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="loadDebugData">刷新数据</el-button>
      </div>
      
      <div v-if="debugData">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="当前用户ID" :value="debugData.currentUserId" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="总会诊申请数" :value="debugData.totalCount" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="作为申请人" :value="debugData.asRequesterCount" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="作为会诊医生" :value="debugData.asConsultantCount" />
          </el-col>
        </el-row>
        
        <div style="margin-top: 20px;">
          <h3>当前用户: {{ debugData.currentUsername }} (ID: {{ debugData.currentUserId }})</h3>
        </div>
        
        <el-table :data="debugData.allConsultations" style="width: 100%; margin-top: 20px;" border>
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="requestNo" label="申请编号" width="150" />
          <el-table-column prop="requesterId" label="申请人ID" width="100" />
          <el-table-column prop="requesterName" label="申请人姓名" width="120" />
          <el-table-column prop="consultantId" label="会诊医生ID" width="120" />
          <el-table-column prop="consultantName" label="会诊医生姓名" width="120" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="requestTime" label="申请时间" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.requestTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="角色" width="120">
            <template #default="scope">
              <div>
                <el-tag v-if="debugData.currentUserId === scope.row.requesterId" type="primary" size="small">申请人</el-tag>
                <el-tag v-if="debugData.currentUserId === scope.row.consultantId" type="success" size="small">会诊医生</el-tag>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <div v-else style="text-align: center; padding: 50px;">
        <el-button type="primary" @click="loadDebugData">加载调试数据</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import { debugAllConsultations } from '@/api/consultation/request'

export default {
  name: "ConsultationDebug",
  data() {
    return {
      debugData: null,
      loading: false
    }
  },
  mounted() {
    this.loadDebugData()
  },
  methods: {
    async loadDebugData() {
      this.loading = true
      try {
        const response = await debugAllConsultations()
        if (response.code === 200) {
          this.debugData = response.data
          console.log('调试数据:', this.debugData)
        } else {
          this.$modal.msgError('加载调试数据失败: ' + response.msg)
        }
      } catch (error) {
        console.error('加载调试数据异常:', error)
        this.$modal.msgError('加载调试数据异常: ' + error.message)
      } finally {
        this.loading = false
      }
    },
    
    getStatusType(status) {
      const statusMap = {
        'PENDING': 'warning',
        'ACCEPTED': 'success',
        'REJECTED': 'danger',
        'COMPLETED': 'info'
      }
      return statusMap[status] || 'info'
    },
    
    getStatusText(status) {
      const statusMap = {
        'PENDING': '待处理',
        'ACCEPTED': '已接受',
        'REJECTED': '已拒绝',
        'COMPLETED': '已完成'
      }
      return statusMap[status] || status
    }
  }
}
</script>

<style scoped>
.box-card {
  margin: 20px;
}
</style>
