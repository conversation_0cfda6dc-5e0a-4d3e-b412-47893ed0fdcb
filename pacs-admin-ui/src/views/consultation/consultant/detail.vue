<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 左侧：患者信息和影像查看器 -->
      <el-col :span="14">
        <el-card class="patient-info" shadow="never">
          <div slot="header" class="clearfix">
            <span>患者信息</span>
          </div>
          <el-descriptions :column="2" border v-if="consultation.patientStudy">
            <el-descriptions-item label="患者姓名">{{ consultation.patientStudy.patientName }}</el-descriptions-item>
            <el-descriptions-item label="患者ID">{{ consultation.patientStudy.patientId }}</el-descriptions-item>
            <el-descriptions-item label="性别">{{ consultation.patientStudy.patientSex }}</el-descriptions-item>
            <el-descriptions-item label="年龄">{{ consultation.patientStudy.patientAge }}</el-descriptions-item>
            <el-descriptions-item label="检查号">{{ consultation.patientStudy.studyId }}</el-descriptions-item>
            <el-descriptions-item label="检查日期">{{ parseTime(consultation.patientStudy.studyDate, '{y}-{m}-{d}') }}</el-descriptions-item>
            <el-descriptions-item label="检查部位">{{ consultation.patientStudy.bodyPart }}</el-descriptions-item>
            <el-descriptions-item label="检查类型">{{ consultation.patientStudy.modality }}</el-descriptions-item>
            <el-descriptions-item label="检查描述" :span="2">{{ consultation.patientStudy.studyDescription }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
        
        <el-card class="image-viewer" style="margin-top: 20px;" shadow="never">
          <div slot="header" class="clearfix">
            <span>影像查看</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="openImageViewer">
              <i class="el-icon-full-screen"></i> 全屏查看
            </el-button>
          </div>
          <div class="image-viewer-container" style="height: 400px; background: #f5f5f5; display: flex; align-items: center; justify-content: center;">
            <div v-if="consultation.studyId">
              <p>影像查看器</p>
              <p>检查号：{{ consultation.studyId }}</p>
              <el-button type="primary" @click="openImageViewer">打开影像查看器</el-button>
            </div>
            <div v-else>
              <p>暂无影像数据</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 右侧：会诊信息和诊断表单 -->
      <el-col :span="10">
        <el-card class="consultation-info" shadow="never">
          <div slot="header" class="clearfix">
            <span>会诊信息</span>
          </div>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="申请编号">{{ consultation.requestNo }}</el-descriptions-item>
            <el-descriptions-item label="申请医生">{{ consultation.requesterName }}</el-descriptions-item>
            <el-descriptions-item label="紧急程度">
              <el-tag v-if="consultation.urgencyLevel === 'URGENT'" type="danger" size="small">紧急</el-tag>
              <el-tag v-else-if="consultation.urgencyLevel === 'NORMAL'" type="primary" size="small">普通</el-tag>
              <el-tag v-else-if="consultation.urgencyLevel === 'LOW'" type="info" size="small">非紧急</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag v-if="consultation.status === 'PENDING'" type="warning" size="small">待处理</el-tag>
              <el-tag v-else-if="consultation.status === 'ACCEPTED'" type="primary" size="small">已接受</el-tag>
              <el-tag v-else-if="consultation.status === 'REJECTED'" type="danger" size="small">已拒绝</el-tag>
              <el-tag v-else-if="consultation.status === 'COMPLETED'" type="success" size="small">已完成</el-tag>
              <el-tag v-else-if="consultation.status === 'CANCELLED'" type="info" size="small">已取消</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="申请时间">{{ parseTime(consultation.requestTime, '{y}-{m}-{d} {h}:{i}') }}</el-descriptions-item>
            <el-descriptions-item label="期望完成时间">{{ parseTime(consultation.expectedCompletionTime, '{y}-{m}-{d} {h}:{i}') }}</el-descriptions-item>
            <el-descriptions-item label="申请原因">{{ consultation.requestReason }}</el-descriptions-item>
            <el-descriptions-item label="病情描述">{{ consultation.requestDescription }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
        
        <!-- 会诊诊断表单 -->
        <el-card class="diagnosis-form" style="margin-top: 20px;" shadow="never" v-if="canEdit">
          <div slot="header" class="clearfix">
            <span>会诊诊断</span>
          </div>
          <el-form ref="diagnosisForm" :model="diagnosisForm" :rules="diagnosisRules" label-width="100px">
            <el-form-item label="影像所见" prop="consultationFindings">
              <el-input v-model="diagnosisForm.consultationFindings" type="textarea" :rows="6" placeholder="请输入影像所见" />
            </el-form-item>
            <el-form-item label="影像意见" prop="consultationOpinion">
              <el-input v-model="diagnosisForm.consultationOpinion" type="textarea" :rows="4" placeholder="请输入影像意见" />
            </el-form-item>
            <el-form-item label="会诊建议" prop="consultantSuggestion">
              <el-input v-model="diagnosisForm.consultantSuggestion" type="textarea" :rows="3" placeholder="请输入会诊建议" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="completeConsultation" :loading="submitting">完成会诊</el-button>
              <el-button type="success" @click="acceptConsultation" v-if="consultation.status === 'PENDING'" :loading="submitting">接受会诊</el-button>
              <el-button type="danger" @click="showRejectDialog" v-if="consultation.status === 'PENDING'">拒绝会诊</el-button>
              <el-button @click="goBack">返回</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 会诊结果显示 -->
        <el-card class="diagnosis-result" style="margin-top: 20px;" shadow="never" v-if="!canEdit && consultation.status === 'COMPLETED'">
          <div slot="header" class="clearfix">
            <span>会诊结果</span>
          </div>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="影像所见">{{ consultation.consultationFindings }}</el-descriptions-item>
            <el-descriptions-item label="影像意见">{{ consultation.consultationOpinion }}</el-descriptions-item>
            <el-descriptions-item label="会诊建议">{{ consultation.consultantSuggestion }}</el-descriptions-item>
            <el-descriptions-item label="完成时间">{{ parseTime(consultation.completionTime, '{y}-{m}-{d} {h}:{i}') }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 操作日志 -->
        <el-card class="audit-logs" style="margin-top: 20px;" shadow="never">
          <div slot="header" class="clearfix">
            <span>操作日志</span>
            <el-button style="float: right; padding: 3px 0" type="primary" link @click="loadAuditLogs">
              <i class="el-icon-refresh"></i> 刷新
            </el-button>
          </div>
          <el-timeline>
            <el-timeline-item
              v-for="log in auditLogs"
              :key="log.id"
              :timestamp="parseTime(log.operationTime, '{y}-{m}-{d} {h}:{i}')"
              placement="top">
              <el-card>
                <h4>{{ log.operationDescription }}</h4>
                <p>操作人：{{ log.operationUserName }}</p>
                <p v-if="log.oldStatus && log.newStatus">状态变更：{{ log.oldStatus }} → {{ log.newStatus }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
    </el-row>

    <!-- 拒绝会诊对话框 -->
    <el-dialog title="拒绝会诊申请" :visible.sync="rejectDialogVisible" width="500px" append-to-body>
      <el-form ref="rejectForm" :model="rejectForm" :rules="rejectRules" label-width="100px">
        <el-form-item label="拒绝原因" prop="rejectReason">
          <el-input v-model="rejectForm.rejectReason" type="textarea" :rows="4" placeholder="请输入拒绝原因" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmReject" :loading="submitting">确 定</el-button>
        <el-button @click="rejectDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getConsultationRequest, acceptConsultation, rejectConsultation, completeConsultation, getConsultationAuditLogs } from "@/api/consultation/request";

export default {
  name: "ConsultationDetail",
  data() {
    return {
      // 会诊申请信息
      consultation: {},
      // 诊断表单
      diagnosisForm: {
        consultationFindings: '',
        consultationOpinion: '',
        consultantSuggestion: ''
      },
      // 表单校验规则
      diagnosisRules: {
        consultationFindings: [
          { required: true, message: "影像所见不能为空", trigger: "blur" }
        ],
        consultationOpinion: [
          { required: true, message: "影像意见不能为空", trigger: "blur" }
        ]
      },
      // 拒绝对话框
      rejectDialogVisible: false,
      rejectForm: {
        rejectReason: ''
      },
      rejectRules: {
        rejectReason: [
          { required: true, message: "拒绝原因不能为空", trigger: "blur" }
        ]
      },
      // 操作日志
      auditLogs: [],
      // 提交状态
      submitting: false
    };
  },
  computed: {
    canEdit() {
      return this.consultation.status === 'ACCEPTED';
    }
  },
  created() {
    this.getConsultationDetail();
    this.loadAuditLogs();
  },
  methods: {
    /** 获取会诊详情 */
    getConsultationDetail() {
      const id = this.$route.query.id;
      if (!id) {
        this.$modal.msgError("缺少会诊申请ID");
        this.goBack();
        return;
      }
      
      getConsultationRequest(id).then(response => {
        this.consultation = response.data;
        // 如果是已完成状态，加载诊断结果到表单
        if (this.consultation.status === 'COMPLETED') {
          this.diagnosisForm = {
            consultationFindings: this.consultation.consultationFindings || '',
            consultationOpinion: this.consultation.consultationOpinion || '',
            consultantSuggestion: this.consultation.consultantSuggestion || ''
          };
        }
      }).catch(() => {
        this.goBack();
      });
    },
    /** 加载操作日志 */
    loadAuditLogs() {
      const id = this.$route.query.id;
      if (id) {
        getConsultationAuditLogs(id).then(response => {
          this.auditLogs = response.data || [];
        });
      }
    },
    /** 接受会诊 */
    acceptConsultation() {
      this.submitting = true;
      acceptConsultation(this.consultation.id, { acceptReason: '接受会诊申请' }).then(() => {
        this.$modal.msgSuccess("接受成功");
        this.getConsultationDetail();
        this.loadAuditLogs();
      }).finally(() => {
        this.submitting = false;
      });
    },
    /** 显示拒绝对话框 */
    showRejectDialog() {
      this.rejectForm.rejectReason = '';
      this.rejectDialogVisible = true;
    },
    /** 确认拒绝 */
    confirmReject() {
      this.$refs["rejectForm"].validate(valid => {
        if (valid) {
          this.submitting = true;
          rejectConsultation(this.consultation.id, this.rejectForm).then(() => {
            this.$modal.msgSuccess("拒绝成功");
            this.rejectDialogVisible = false;
            this.getConsultationDetail();
            this.loadAuditLogs();
          }).finally(() => {
            this.submitting = false;
          });
        }
      });
    },
    /** 完成会诊 */
    completeConsultation() {
      this.$refs["diagnosisForm"].validate(valid => {
        if (valid) {
          this.submitting = true;
          completeConsultation(this.consultation.id, this.diagnosisForm).then(() => {
            this.$modal.msgSuccess("完成成功");
            this.getConsultationDetail();
            this.loadAuditLogs();
          }).finally(() => {
            this.submitting = false;
          });
        }
      });
    },
    /** 打开影像查看器 */
    openImageViewer() {
      if (this.consultation.studyId) {
        // 这里可以集成DICOM查看器
        const url = `${process.env.VUE_APP_BASE_API}/dicom/viewer?studyId=${this.consultation.studyId}`;
        window.open(url, '_blank');
      } else {
        this.$modal.msgWarning("暂无影像数据");
      }
    },
    /** 返回 */
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style scoped>
.patient-info, .consultation-info, .diagnosis-form, .diagnosis-result, .audit-logs {
  margin-bottom: 20px;
}

.image-viewer-container {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
}

.el-descriptions {
  margin-top: 20px;
}

.el-timeline {
  padding-left: 0;
}
</style>
