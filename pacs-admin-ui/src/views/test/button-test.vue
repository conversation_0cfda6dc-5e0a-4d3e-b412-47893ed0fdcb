<template>
  <div class="button-test-page">
    <div class="container">
      <h1>按钮样式测试页面</h1>
      
      <div class="section">
        <h2>实心按钮 (默认)</h2>
        <div class="button-group">
          <el-button>默认按钮</el-button>
          <el-button type="primary">主要按钮</el-button>
          <el-button type="success">成功按钮</el-button>
          <el-button type="info">信息按钮</el-button>
          <el-button type="warning">警告按钮</el-button>
          <el-button type="danger">危险按钮</el-button>
        </div>
      </div>

      <div class="section">
        <h2>朴素按钮 (plain)</h2>
        <div class="button-group">
          <el-button plain>默认按钮</el-button>
          <el-button type="primary" plain>主要按钮</el-button>
          <el-button type="success" plain>成功按钮</el-button>
          <el-button type="info" plain>信息按钮</el-button>
          <el-button type="warning" plain>警告按钮</el-button>
          <el-button type="danger" plain>危险按钮</el-button>
        </div>
      </div>

      <div class="section">
        <h2>文字按钮 (text)</h2>
        <div class="button-group">
          <el-button text>默认按钮</el-button>
          <el-button type="primary" text>主要按钮</el-button>
          <el-button type="success" text>成功按钮</el-button>
          <el-button type="info" text>信息按钮</el-button>
          <el-button type="warning" text>警告按钮</el-button>
          <el-button type="danger" text>危险按钮</el-button>
        </div>
      </div>

      <div class="section">
        <h2>链接按钮 (link)</h2>
        <div class="button-group">
          <el-button link>默认按钮</el-button>
          <el-button type="primary" link>主要按钮</el-button>
          <el-button type="success" link>成功按钮</el-button>
          <el-button type="info" link>信息按钮</el-button>
          <el-button type="warning" link>警告按钮</el-button>
          <el-button type="danger" link>危险按钮</el-button>
        </div>
      </div>

      <div class="section">
        <h2>带图标的按钮</h2>
        <div class="button-group">
          <el-button :icon="Search">搜索</el-button>
          <el-button type="primary" :icon="Edit">编辑</el-button>
          <el-button type="success" :icon="Check">确认</el-button>
          <el-button type="info" :icon="Message">消息</el-button>
          <el-button type="warning" :icon="Warning">警告</el-button>
          <el-button type="danger" :icon="Delete">删除</el-button>
        </div>
      </div>

      <div class="section">
        <h2>不同尺寸的按钮</h2>
        <div class="button-group">
          <el-button size="large" type="primary">大型按钮</el-button>
          <el-button type="primary">默认按钮</el-button>
          <el-button size="small" type="primary">小型按钮</el-button>
        </div>
      </div>

      <div class="section">
        <h2>禁用状态</h2>
        <div class="button-group">
          <el-button disabled>默认按钮</el-button>
          <el-button type="primary" disabled>主要按钮</el-button>
          <el-button type="success" disabled>成功按钮</el-button>
          <el-button type="info" disabled>信息按钮</el-button>
          <el-button type="warning" disabled>警告按钮</el-button>
          <el-button type="danger" disabled>危险按钮</el-button>
        </div>
      </div>

      <div class="section">
        <h2>加载状态</h2>
        <div class="button-group">
          <el-button loading type="primary">加载中</el-button>
          <el-button loading type="success">保存中</el-button>
          <el-button loading type="warning">处理中</el-button>
        </div>
      </div>

      <div class="section">
        <h2>按钮组</h2>
        <el-button-group>
          <el-button type="primary" :icon="ArrowLeft">上一页</el-button>
          <el-button type="primary" :icon="ArrowRight">下一页</el-button>
        </el-button-group>
      </div>

      <div class="section">
        <h2>Info按钮特殊测试</h2>
        <div class="info-test-section">
          <h3>白色背景下的Info按钮</h3>
          <div class="white-bg">
            <div class="button-group">
              <el-button type="info">Info实心按钮</el-button>
              <el-button type="info" plain>Info朴素按钮</el-button>
              <el-button type="info" text>Info文字按钮</el-button>
              <el-button type="info" link>Info链接按钮</el-button>
            </div>
          </div>

          <h3>灰色背景下的Info按钮</h3>
          <div class="gray-bg">
            <div class="button-group">
              <el-button type="info">Info实心按钮</el-button>
              <el-button type="info" plain>Info朴素按钮</el-button>
              <el-button type="info" text>Info文字按钮</el-button>
              <el-button type="info" link>Info链接按钮</el-button>
            </div>
          </div>
        </div>
      </div>

      <div class="section">
        <h2>在不同背景下的按钮</h2>
        <div class="dark-bg">
          <h3>深色背景</h3>
          <div class="button-group">
            <el-button>默认按钮</el-button>
            <el-button type="primary">主要按钮</el-button>
            <el-button type="success">成功按钮</el-button>
            <el-button type="info">Info按钮</el-button>
            <el-button type="warning">警告按钮</el-button>
            <el-button type="danger">危险按钮</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { 
  Search, 
  Edit, 
  Check, 
  Message, 
  Warning, 
  Delete,
  ArrowLeft,
  ArrowRight
} from '@element-plus/icons-vue'
</script>

<style scoped>
.button-test-page {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

h1 {
  text-align: center;
  color: #303133;
  margin-bottom: 40px;
  font-size: 28px;
}

.section {
  margin-bottom: 40px;
}

h2 {
  color: #606266;
  margin-bottom: 20px;
  font-size: 18px;
  border-bottom: 2px solid #e4e7ed;
  padding-bottom: 10px;
}

h3 {
  color: white;
  margin-bottom: 15px;
  font-size: 16px;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

.dark-bg {
  background: #2c3e50;
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
}

.dark-bg .button-group {
  margin-top: 10px;
}

.info-test-section {
  margin-top: 20px;
}

.white-bg {
  background: white;
  padding: 20px;
  border-radius: 8px;
  border: 2px solid #e4e7ed;
  margin-bottom: 20px;
}

.gray-bg {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #dcdfe6;
}

.white-bg h3,
.gray-bg h3 {
  color: #303133;
  margin-bottom: 15px;
  font-size: 14px;
}

/* 确保在测试页面中按钮文字可见 */
.button-test-page .el-button {
  margin: 4px;
}
</style>
