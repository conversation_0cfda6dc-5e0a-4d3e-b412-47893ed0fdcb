<template>
  <div class="login-container">
    <!-- 背景装饰元素 -->
    <div class="background-decoration">
      <div class="decoration-circle"></div>
      <div class="decoration-circle"></div>
      <div class="decoration-circle"></div>
      <div class="decoration-circle"></div>
    </div>

    <!-- 左侧品牌区域 -->
    <el-row>
      <el-col :span="14">
        <div class="brand-section">
          <div class="brand-content">
            <!-- Logo区域 -->
            <div class="logo-section">
              <div class="logo-icon">
                <svg viewBox="0 0 24 24">
                  <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                </svg>
              </div>
              <div class="system-title">{{ loginConfig.systemName || 'PACS医疗影像系统' }}</div>
              <div class="system-subtitle">Picture Archiving Communication System</div>
            </div>

            <!-- 医院信息 -->
            <div class="hospital-info">
              <h2 class="hospital-name">{{ loginConfig.hospitalName || '医疗机构' }}</h2>
              <p class="hospital-desc">专业的医疗影像管理与诊断平台</p>
            </div>

            <!-- 特性展示 -->
            <div class="features">
              <div class="feature-item">
                <div class="feature-icon">
                  <svg viewBox="0 0 24 24">
                    <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                  </svg>
                </div>
                <span>影像存储与管理</span>
              </div>
              <div class="feature-item">
                <div class="feature-icon">
                  <svg viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
                <span>智能诊断辅助</span>
              </div>
              <div class="feature-item">
                <div class="feature-icon">
                  <svg viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                </div>
                <span>安全可靠保障</span>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="10">
        <!-- 右侧登录区域 -->
        <div class="login-section">
          <div class="login-content">
            <div class="login-header">
              <h3 class="login-title">系统登录</h3>
              <p class="login-subtitle">请输入您的账号和密码</p>
            </div>

            <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
              <el-form-item prop="username">
                <el-input
                    v-model="loginForm.username"
                    type="text"
                    size="large"
                    auto-complete="off"
                    placeholder="请输入账号"
                    class="login-input"
                >
                  <template #prefix><svg-icon icon-class="user" class="input-icon" /></template>
                </el-input>
              </el-form-item>

              <el-form-item prop="password">
                <el-input
                    v-model="loginForm.password"
                    type="password"
                    size="large"
                    auto-complete="off"
                    placeholder="请输入密码"
                    class="login-input"
                    @keyup.enter="handleLogin"
                >
                  <template #prefix><svg-icon icon-class="password" class="input-icon" /></template>
                </el-input>
              </el-form-item>

              <el-form-item prop="code" v-if="captchaEnabled">
                <div class="captcha-container">
                  <el-input
                      v-model="loginForm.code"
                      size="large"
                      auto-complete="off"
                      placeholder="请输入验证码"
                      class="captcha-input"
                      @keyup.enter="handleLogin"
                  >
                    <template #prefix><svg-icon icon-class="validCode" class="input-icon" /></template>
                  </el-input>
                  <div class="captcha-image" @click="getCode">
                    <img :src="codeUrl" alt="验证码" />
                  </div>
                </div>
              </el-form-item>

              <div class="login-options">
                <el-checkbox v-model="loginForm.rememberMe">记住密码</el-checkbox>
                <router-link v-if="register" class="forgot-link" :to="'/register'">立即注册</router-link>
              </div>

              <el-form-item class="login-button-item">
                <el-button
                    :loading="loading"
                    size="large"
                    type="primary"
                    class="login-button"
                    @click.prevent="handleLogin"
                >
                  <span v-if="!loading">登 录</span>
                  <span v-else>登 录 中...</span>
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>

      </el-col>
    </el-row>


    <!-- 底部版权信息 -->
    <div class="footer-copyright">
      <span>{{ loginConfig.footerText || 'Copyright © 2018-2025 All Rights Reserved.' }}</span>
    </div>
  </div>
</template>

<script setup>
import { getCodeImg, getLoginConfig } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import { buildAppPath } from "@/utils/validate";
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();

const loginForm = ref({
  username: "admin",
  password: "admin123",
  rememberMe: false,
  code: "",
  uuid: ""
});

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: true, trigger: "change", message: "请输入验证码" }]
};

const codeUrl = ref("");
const loading = ref(false);
// 验证码开关
const captchaEnabled = ref(true);
// 注册开关
const register = ref(false);
const redirect = ref(undefined);

// 登录配置
const loginConfig = ref({
  hospitalName: '',
  systemName: '',
  footerText: ''
});

watch(route, (newRoute) => {
    redirect.value = newRoute.query && newRoute.query.redirect;
}, { immediate: true });

function handleLogin() {
  proxy.$refs.loginRef.validate(valid => {
    if (valid) {
      loading.value = true;
      // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        Cookies.set("username", loginForm.value.username, { expires: 30 });
        Cookies.set("password", encrypt(loginForm.value.password), { expires: 30 });
        Cookies.set("rememberMe", loginForm.value.rememberMe, { expires: 30 });
      } else {
        // 否则移除
        Cookies.remove("username");
        Cookies.remove("password");
        Cookies.remove("rememberMe");
      }
      // 调用action的登录方法
      userStore.login(loginForm.value).then(() => {
        const query = route.query;
        const otherQueryParams = Object.keys(query).reduce((acc, cur) => {
          if (cur !== "redirect") {
            acc[cur] = query[cur];
          }
          return acc;
        }, {});
        // 使用 buildAppPath 构建正确的跳转路径
        const targetPath = redirect.value || buildAppPath("/");
        router.push({ path: targetPath, query: otherQueryParams });
      }).catch(() => {
        loading.value = false;
        // 重新获取验证码
        if (captchaEnabled.value) {
          getCode();
        }
      });
    }
  });
}

function getCode() {
  getCodeImg().then(res => {
    captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled;
    if (captchaEnabled.value) {
      codeUrl.value = "data:image/gif;base64," + res.img;
      loginForm.value.uuid = res.uuid;
    }
  });
}

function getCookie() {
  const username = Cookies.get("username");
  const password = Cookies.get("password");
  const rememberMe = Cookies.get("rememberMe");
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password: password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
  };
}

// 加载登录配置
async function loadLoginConfig() {
  try {
    const response = await getLoginConfig();
    if (response && response.code === 200 && response.data) {
      loginConfig.value = {
        hospitalName: response.data.hospitalName || '鄂托克旗人民医院',
        systemName: response.data.systemName || 'PACS医疗影像系统',
        footerText: response.data.footerText || 'Copyright © 2018-2025鄂托克旗人民医院 All Rights Reserved.'
      };
    } else {
      // 使用默认配置
      loginConfig.value = {
        hospitalName: '鄂托克旗人民医院',
        systemName: 'PACS医疗影像系统',
        footerText: 'Copyright © 2018-2025鄂托克旗人民医院 All Rights Reserved.'
      };
    }
  } catch (error) {
    console.error('加载登录配置失败:', error);
    // 使用默认配置
    loginConfig.value = {
      hospitalName: '鄂托克旗人民医院',
      systemName: 'PACS医疗影像系统',
      footerText: 'Copyright © 2018-2025鄂托克旗人民医院 All Rights Reserved.'
    };
  }
}

getCode();
getCookie();
loadLoginConfig();
</script>

<style scoped>
/* 主容器 */
.login-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #f8fafb 0%, #ebf2ff 50%, #f0f7ff 100%);
  overflow: hidden;
}

/* 背景装饰元素 */
.background-decoration {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.05) 0%, rgba(59, 130, 246, 0.03) 100%);
  animation: floatUp 8s ease-in-out infinite;
}

.decoration-circle:nth-child(1) {
  width: 120px;
  height: 120px;
  top: 10%;
  left: 15%;
  animation-delay: 0s;
}

.decoration-circle:nth-child(2) {
  width: 80px;
  height: 80px;
  top: 20%;
  right: 20%;
  animation-delay: 1s;
}

.decoration-circle:nth-child(3) {
  width: 100px;
  height: 100px;
  bottom: 15%;
  left: 10%;
  animation-delay: 2s;
}

.decoration-circle:nth-child(4) {
  width: 60px;
  height: 60px;
  bottom: 25%;
  right: 15%;
  animation-delay: 3s;
}

/* 左侧品牌区域 */
.brand-section {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px;
  position: relative;
  z-index: 2;
}

.brand-content {
  text-align: center;
}

/* Logo区域 */
.logo-section {
  margin-bottom: 40px;
}

.logo-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 50%, #60a5fa 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  box-shadow:
    0 20px 40px rgba(37, 99, 235, 0.15),
    0 8px 16px rgba(37, 99, 235, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  animation: logoFloat 3s ease-in-out infinite;
  position: relative;
}

.logo-icon::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
  border-radius: 20px;
  animation: logoShine 4s ease-in-out infinite;
}

.logo-icon svg {
  width: 48px;
  height: 48px;
  fill: white;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.system-title {
  font-family: 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', sans-serif;
  font-size: 28px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
}

.system-subtitle {
  font-family: 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #64748b;
  font-weight: 400;
  letter-spacing: 1px;
  text-transform: uppercase;
}

/* 医院信息 */
.hospital-info {
  margin-bottom: 36px;
}

.hospital-name {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 12px;
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hospital-desc {
  font-size: 16px;
  color: #64748b;
  margin: 0;
  font-weight: 400;
}

/* 特性展示 */
.features {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.feature-item:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateX(8px);
}

.feature-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.feature-icon svg {
  width: 20px;
  height: 20px;
  fill: white;
}

.feature-item span {
  font-size: 16px;
  color: #374151;
  font-weight: 500;
}

/* 右侧登录区域 */
.login-section {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-left: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 2;
}

.login-content {
  width: 80%;
  padding: 40px;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.login-title {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
  font-family: 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', sans-serif;
}

.login-subtitle {
  font-size: 14px;
  color: #64748b;
  margin: 0;
  font-weight: 400;
}

/* 登录表单 */
.login-form {
  width: 100%;
}

.login-form :deep(.el-form-item) {
  margin-bottom: 24px;
}

.login-form :deep(.el-form-item__error) {
  font-size: 12px;
  color: #ef4444;
  padding-top: 4px;
}

.login-input :deep(.el-input__wrapper) {
  height: 48px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  background: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.login-input :deep(.el-input__wrapper:hover) {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.login-input :deep(.el-input__wrapper.is-focus) {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.login-input :deep(.el-input__inner) {
  height: 46px;
  line-height: 46px;
  font-size: 14px;
  color: #374151;
  padding-left: 44px;
}

.login-input :deep(.el-input__inner::placeholder) {
  color: #9ca3af;
}

.input-icon {
  width: 16px;
  height: 16px;
  color: #6b7280;
  margin-left: 16px;
}

/* 验证码容器 */
.captcha-container {
  display: flex;
  gap: 12px;
  align-items: center;
}

.captcha-input {
  flex: 1;
}

.captcha-image {
  width: 120px;
  height: 48px;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.captcha-image:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.captcha-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 登录选项 */
.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.login-options :deep(.el-checkbox) {
  color: #374151;
  font-size: 14px;
}

.login-options :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #2563eb;
  border-color: #2563eb;
}

.forgot-link {
  color: #2563eb;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.forgot-link:hover {
  color: #1d4ed8;
}

/* 登录按钮 */
.login-button-item {
  margin-bottom: 0;
}

.login-button {
  width: 100%;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
  border: none;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
  color: #fff;
}

.login-button:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
  box-shadow: 0 8px 20px rgba(37, 99, 235, 0.4);
  transform: translateY(-2px);
}

.login-button:active {
  transform: translateY(0);
}

/* 底部版权 */
.footer-copyright {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: #64748b;
  font-size: 12px;
  z-index: 3;
}

/* 动画定义 */
@keyframes logoFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes logoShine {
  0%, 100% {
    opacity: 0;
    transform: translateX(-100%);
  }
  50% {
    opacity: 1;
    transform: translateX(100%);
  }
}

@keyframes floatUp {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.6;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .brand-section {
    width: 55%;
    max-width: 600px;
  }

  .login-section {
    width: 45%;
    min-width: 380px;
  }
}

@media (max-width: 1024px) {
  .login-container {
    flex-direction: column;
  }

  .brand-section {
    width: 100%;
    height: 35%;
    padding: 30px 20px;
    max-width: none;
  }

  .login-section {
    width: 100%;
    height: 65%;
    min-width: auto;
    max-width: none;
    border-left: none;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
  }

  .brand-content {
    max-width: 100%;
  }

  .hospital-name {
    font-size: 22px;
  }

  .hospital-info {
    margin-bottom: 24px;
  }

  .features {
    flex-direction: row;
    justify-content: center;
    gap: 16px;
  }

  .feature-item {
    flex-direction: column;
    text-align: center;
    padding: 12px;
    min-width: 120px;
  }

  .feature-item span {
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .brand-section {
    padding: 20px;
  }

  .login-content {
    padding: 20px;
  }

  .system-title {
    font-size: 20px;
  }

  .hospital-name {
    font-size: 20px;
  }

  .logo-icon {
    width: 60px;
    height: 60px;
    margin-bottom: 16px;
  }

  .logo-icon svg {
    width: 32px;
    height: 32px;
  }

  .features {
    gap: 12px;
  }

  .feature-item {
    padding: 8px;
    min-width: 100px;
  }

  .feature-icon {
    width: 32px;
    height: 32px;
  }

  .feature-icon svg {
    width: 16px;
    height: 16px;
  }
}

@media (max-width: 480px) {
  .login-container {
    height: 100vh;
  }

  .brand-section {
    height: 35%;
    padding: 16px;
  }

  .login-section {
    height: 65%;
  }

  .login-content {
    padding: 16px;
    max-width: 100%;
  }

  .features {
    display: none;
  }

  .hospital-info {
    margin-bottom: 20px;
  }
}
</style>
