<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>诊断PDF生成管理</span>
        </div>
      </template>
      
      <!-- 操作按钮区域 -->
      <div class="operation-area">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card shadow="hover">
              <template #header>
                <div class="card-header">
                  <el-icon><Document /></el-icon>
                  <span>批量生成PDF</span>
                </div>
              </template>
              <div class="card-content">
                <p>为所有已审核但未生成PDF的诊断记录生成PDF报告</p>
                <el-button 
                  type="primary" 
                  :loading="batchLoading"
                  @click="generateBatchPdf"
                  :disabled="batchLoading"
                >
                  <el-icon><Plus /></el-icon>
                  开始批量生成
                </el-button>
              </div>
            </el-card>
          </el-col>
          
          <el-col :span="8">
            <el-card shadow="hover">
              <template #header>
                <div class="card-header">
                  <el-icon><Timer /></el-icon>
                  <span>异步批量生成</span>
                </div>
              </template>
              <div class="card-content">
                <p>在后台异步执行批量PDF生成任务</p>
                <el-button 
                  type="success" 
                  :loading="asyncLoading"
                  @click="generateBatchPdfAsync"
                  :disabled="asyncLoading"
                >
                  <el-icon><Clock /></el-icon>
                  后台生成
                </el-button>
              </div>
            </el-card>
          </el-col>
          
          <el-col :span="8">
            <el-card shadow="hover">
              <template #header>
                <div class="card-header">
                  <el-icon><Edit /></el-icon>
                  <span>单个生成PDF</span>
                </div>
              </template>
              <div class="card-content">
                <p>为指定的诊断记录生成PDF报告</p>
                <el-input-number 
                  v-model="singleDiagnosisId" 
                  :min="1" 
                  placeholder="请输入诊断ID"
                  style="width: 100%; margin-bottom: 10px;"
                />
                <el-button 
                  type="warning" 
                  :loading="singleLoading"
                  @click="generateSinglePdf"
                  :disabled="singleLoading || !singleDiagnosisId"
                >
                  <el-icon><DocumentAdd /></el-icon>
                  生成PDF
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 状态统计区域 -->
      <div class="statistics-area" style="margin-top: 20px;">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><DataAnalysis /></el-icon>
              <span>PDF生成状态统计</span>
              <el-button 
                type="text" 
                @click="refreshStatistics"
                :loading="statisticsLoading"
                style="float: right;"
              >
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-number">{{ statistics.total || 0 }}</div>
                <div class="stat-label">总诊断数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-number pending">{{ statistics.pending || 0 }}</div>
                <div class="stat-label">待生成PDF</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-number completed">{{ statistics.completed || 0 }}</div>
                <div class="stat-label">已生成PDF</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-number failed">{{ statistics.failed || 0 }}</div>
                <div class="stat-label">生成失败</div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </div>

      <!-- 操作日志区域 -->
      <div class="log-area" style="margin-top: 20px;">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><List /></el-icon>
              <span>操作日志</span>
              <el-button 
                type="text" 
                @click="clearLogs"
                style="float: right;"
              >
                <el-icon><Delete /></el-icon>
                清空日志
              </el-button>
            </div>
          </template>
          
          <div class="log-content">
            <div 
              v-for="(log, index) in operationLogs" 
              :key="index"
              class="log-item"
              :class="log.type"
            >
              <span class="log-time">{{ log.time }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
            <div v-if="operationLogs.length === 0" class="no-logs">
              暂无操作日志
            </div>
          </div>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Document, 
  Plus, 
  Timer, 
  Clock, 
  Edit, 
  DocumentAdd, 
  DataAnalysis, 
  Refresh, 
  List, 
  Delete 
} from '@element-plus/icons-vue'
import request from '@/utils/request'

// 响应式数据
const batchLoading = ref(false)
const asyncLoading = ref(false)
const singleLoading = ref(false)
const statisticsLoading = ref(false)
const singleDiagnosisId = ref(null)
const statistics = ref({})
const operationLogs = ref([])

// 添加日志
const addLog = (message, type = 'info') => {
  const log = {
    time: new Date().toLocaleString(),
    message,
    type
  }
  operationLogs.value.unshift(log)
  
  // 限制日志数量
  if (operationLogs.value.length > 50) {
    operationLogs.value = operationLogs.value.slice(0, 50)
  }
}

// 批量生成PDF
const generateBatchPdf = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要开始批量生成PDF吗？这可能需要一些时间。',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    batchLoading.value = true
    addLog('开始批量生成PDF...', 'info')
    
    const response = await request({
      url: '/diagnosis/pdf/generateBatch',
      method: 'post'
    })
    
    ElMessage.success('批量生成PDF任务已启动')
    addLog('批量生成PDF任务启动成功', 'success')
    
    // 刷新统计数据
    await refreshStatistics()
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量生成PDF失败: ' + (error.message || error))
      addLog('批量生成PDF失败: ' + (error.message || error), 'error')
    }
  } finally {
    batchLoading.value = false
  }
}

// 异步批量生成PDF
const generateBatchPdfAsync = async () => {
  try {
    asyncLoading.value = true
    addLog('启动后台批量生成PDF任务...', 'info')
    
    const response = await request({
      url: '/diagnosis/pdf/generateBatchAsync',
      method: 'post'
    })
    
    ElMessage.success('后台批量生成PDF任务已启动')
    addLog('后台批量生成PDF任务启动成功', 'success')
    
  } catch (error) {
    ElMessage.error('启动后台任务失败: ' + (error.message || error))
    addLog('启动后台任务失败: ' + (error.message || error), 'error')
  } finally {
    asyncLoading.value = false
  }
}

// 单个生成PDF
const generateSinglePdf = async () => {
  if (!singleDiagnosisId.value) {
    ElMessage.warning('请输入诊断ID')
    return
  }
  
  try {
    singleLoading.value = true
    addLog(`开始为诊断ID ${singleDiagnosisId.value} 生成PDF...`, 'info')
    
    const response = await request({
      url: `/diagnosis/pdf/generate/${singleDiagnosisId.value}`,
      method: 'post'
    })
    
    ElMessage.success('PDF生成任务已启动')
    addLog(`诊断ID ${singleDiagnosisId.value} PDF生成任务启动成功`, 'success')
    
    // 清空输入框
    singleDiagnosisId.value = null
    
    // 刷新统计数据
    await refreshStatistics()
    
  } catch (error) {
    ElMessage.error('生成PDF失败: ' + (error.message || error))
    addLog(`诊断ID ${singleDiagnosisId.value} PDF生成失败: ` + (error.message || error), 'error')
  } finally {
    singleLoading.value = false
  }
}

// 刷新统计数据
const refreshStatistics = async () => {
  try {
    statisticsLoading.value = true
    
    // 这里需要实现获取统计数据的API
    // const response = await request({
    //   url: '/diagnosis/pdf/statistics',
    //   method: 'get'
    // })
    // statistics.value = response.data
    
    // 模拟数据
    statistics.value = {
      total: 150,
      pending: 25,
      completed: 120,
      failed: 5
    }
    
  } catch (error) {
    ElMessage.error('获取统计数据失败: ' + (error.message || error))
  } finally {
    statisticsLoading.value = false
  }
}

// 清空日志
const clearLogs = () => {
  operationLogs.value = []
  ElMessage.success('日志已清空')
}

// 组件挂载时执行
onMounted(() => {
  refreshStatistics()
  addLog('PDF生成管理页面已加载', 'info')
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.operation-area .card-content {
  text-align: center;
  padding: 20px 0;
}

.operation-area .card-content p {
  margin-bottom: 15px;
  color: #666;
  font-size: 14px;
}

.stat-item {
  text-align: center;
  padding: 20px 0;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-number.pending {
  color: #E6A23C;
}

.stat-number.completed {
  color: #67C23A;
}

.stat-number.failed {
  color: #F56C6C;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.log-content {
  max-height: 300px;
  overflow-y: auto;
}

.log-item {
  padding: 8px 12px;
  border-left: 3px solid #409EFF;
  margin-bottom: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.log-item.success {
  border-left-color: #67C23A;
  background-color: #f0f9ff;
}

.log-item.error {
  border-left-color: #F56C6C;
  background-color: #fef0f0;
}

.log-item.warning {
  border-left-color: #E6A23C;
  background-color: #fdf6ec;
}

.log-time {
  font-size: 12px;
  color: #999;
  margin-right: 10px;
}

.log-message {
  font-size: 14px;
  color: #333;
}

.no-logs {
  text-align: center;
  color: #999;
  padding: 40px 0;
  font-style: italic;
}
</style>
