<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>报告模版管理</span>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px" class="search-form">
        <el-row :gutter="16">
          <el-col :span="5">
            <el-form-item label="模版名称" prop="templateName">
              <el-input
                v-model="queryParams.templateName"
                placeholder="请输入模版名称"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="检查类型" prop="modalityType">
              <el-select v-model="queryParams.modalityType" placeholder="请选择检查类型" clearable style="width: 100%">
                <el-option v-for="dict in modalityTypeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="检查部位" prop="bodyPart">
              <el-input
                v-model="queryParams.bodyPart"
                placeholder="请输入检查部位"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 100%">
                <el-option
                  v-for="dict in statusOptions"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 工具栏 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['diagnosis:reportTemplate:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['diagnosis:reportTemplate:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['diagnosis:reportTemplate:remove']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['diagnosis:reportTemplate:export']"
          >导出</el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="reportTemplateList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="模版名称" align="center" prop="templateName" />
        <el-table-column label="检查类型" align="center" prop="modalityType">
          <template #default="scope">
            <dict-tag :options="modalityTypeOptions" :value="scope.row.modalityType"/>
          </template>
        </el-table-column>
        <el-table-column label="检查部位" align="center" prop="bodyPart" />
        <el-table-column label="是否默认" align="center" prop="isDefault">
          <template #default="scope">
            <el-tag v-if="scope.row.isDefault === '1'" type="success">是</el-tag>
            <el-tag v-else type="info">否</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="statusOptions" :value="scope.row.status"/>
          </template>
        </el-table-column>
        <el-table-column label="排序" align="center" prop="sortOrder" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button
              type="text"
              icon="View"
              @click="handlePreview(scope.row)"
              v-hasPermi="['diagnosis:reportTemplate:query']"
            >预览</el-button>
            <el-button
              type="text"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['diagnosis:reportTemplate:edit']"
            >修改</el-button>
            <el-button
              v-if="scope.row.isDefault !== '1'"
              type="text"
              icon="Star"
              @click="handleSetDefault(scope.row)"
              v-hasPermi="['diagnosis:reportTemplate:edit']"
            >设为默认</el-button>
            <el-button
              type="text"
              icon="Delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['diagnosis:reportTemplate:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改报告模版对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="reportTemplateRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="模版名称" prop="templateName">
              <el-input v-model="form.templateName" placeholder="请输入模版名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检查类型" prop="modalityType">
              <el-select v-model="form.modalityType" placeholder="请选择检查类型" style="width: 100%">
                <el-option v-for="dict in modalityTypeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="检查部位" prop="bodyPart">
              <el-input v-model="form.bodyPart" placeholder="请输入检查部位" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sortOrder">
              <el-input-number v-model="form.sortOrder" :min="0" :max="9999" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否默认" prop="isDefault">
              <el-radio-group v-model="form.isDefault">
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio label="1">启用</el-radio>
                <el-radio label="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="模版JSON" prop="templateJson">
          <el-input 
            v-model="form.templateJson" 
            type="textarea" 
            :rows="10" 
            placeholder="请输入ActiveReports RDLX-JSON模版内容"
            style="font-family: monospace;"
          />
          <div class="mt-2">
            <el-button size="small" @click="handleValidateJson">验证JSON格式</el-button>
            <el-button size="small" @click="handleFormatJson">格式化JSON</el-button>
          </div>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 模版预览对话框 -->
    <el-dialog title="模版预览" v-model="previewOpen" width="80%" append-to-body>
      <div class="template-preview">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="模版名称">{{ previewTemplate.templateName }}</el-descriptions-item>
          <el-descriptions-item label="检查类型">{{ previewTemplate.modalityType }}</el-descriptions-item>
          <el-descriptions-item label="检查部位">{{ previewTemplate.bodyPart }}</el-descriptions-item>
          <el-descriptions-item label="是否默认">
            <el-tag v-if="previewTemplate.isDefault === '1'" type="success">是</el-tag>
            <el-tag v-else type="info">否</el-tag>
          </el-descriptions-item>
        </el-descriptions>
        <div class="mt-4">
          <h4>模版JSON内容：</h4>
          <el-input 
            v-model="previewTemplate.templateJson" 
            type="textarea" 
            :rows="15" 
            readonly
            style="font-family: monospace; font-size: 12px;"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="ReportTemplate">
import { listReportTemplate, getReportTemplate, delReportTemplate, addReportTemplate, updateReportTemplate, setDefaultTemplate, validateJson } from "@/api/diagnosis/reportTemplate";

const { proxy } = getCurrentInstance();
const { sys_normal_disable } = proxy.useDict('sys_normal_disable');

const reportTemplateList = ref([]);
const open = ref(false);
const previewOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const previewTemplate = ref({});

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    templateName: null,
    modalityType: null,
    bodyPart: null,
    status: null
  },
  rules: {
    templateName: [
      { required: true, message: "模版名称不能为空", trigger: "blur" }
    ],
    modalityType: [
      { required: true, message: "检查类型不能为空", trigger: "change" }
    ],
    templateJson: [
      { required: true, message: "模版JSON不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

// 检查类型选项
const modalityTypeOptions = ref([
  { label: "通用", value: "COMMON" },
  { label: "CT", value: "CT" },
  { label: "MRI", value: "MRI" },
  { label: "DR", value: "DR" },
  { label: "CR", value: "CR" },
  { label: "DX", value: "DX" },
  { label: "超声", value: "US" },
  { label: "乳腺摄影", value: "MG" },
  { label: "透视", value: "RF" },
  { label: "血管造影", value: "XA" },
  { label: "内镜", value: "ES" },
  { label: "核医学", value: "NM" },
  { label: "PET", value: "PT" },
  { label: "其他", value: "OT" }
]);

// 状态选项
const statusOptions = ref([
  { label: "启用", value: "1" },
  { label: "禁用", value: "0" }
]);

/** 查询报告模版列表 */
function getList() {
  loading.value = true;
  listReportTemplate(queryParams.value).then(response => {
    reportTemplateList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    templateName: null,
    modalityType: null,
    bodyPart: null,
    templateJson: null,
    isDefault: "0",
    sortOrder: 0,
    status: "1",
    remark: null
  };
  proxy.resetForm("reportTemplateRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加报告模版";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getReportTemplate(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改报告模版";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["reportTemplateRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateReportTemplate(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addReportTemplate(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除报告模版编号为"' + _ids + '"的数据项？').then(function() {
    return delReportTemplate(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('diagnosis/reportTemplate/export', {
    ...queryParams.value
  }, `reportTemplate_${new Date().getTime()}.xlsx`)
}

/** 预览按钮操作 */
function handlePreview(row) {
  previewTemplate.value = { ...row };
  // 格式化JSON显示
  try {
    const jsonObj = JSON.parse(row.templateJson);
    previewTemplate.value.templateJson = JSON.stringify(jsonObj, null, 2);
  } catch (e) {
    // JSON格式错误时保持原样
  }
  previewOpen.value = true;
}

/** 设为默认操作 */
function handleSetDefault(row) {
  proxy.$modal.confirm('是否确认将此模版设为"' + row.modalityType + '"类型的默认模版？').then(function() {
    return setDefaultTemplate(row.id, row.modalityType);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("设置成功");
  }).catch(() => {});
}

/** 验证JSON格式 */
function handleValidateJson() {
  if (!form.value.templateJson) {
    proxy.$modal.msgWarning("请先输入模版JSON内容");
    return;
  }
  
  validateJson(form.value.templateJson).then(response => {
    proxy.$modal.msgSuccess("JSON格式验证通过");
  }).catch(error => {
    proxy.$modal.msgError("JSON格式不正确：" + error.msg);
  });
}

/** 格式化JSON */
function handleFormatJson() {
  if (!form.value.templateJson) {
    proxy.$modal.msgWarning("请先输入模版JSON内容");
    return;
  }
  
  try {
    const jsonObj = JSON.parse(form.value.templateJson);
    form.value.templateJson = JSON.stringify(jsonObj, null, 2);
    proxy.$modal.msgSuccess("JSON格式化完成");
  } catch (e) {
    proxy.$modal.msgError("JSON格式错误，无法格式化");
  }
}

getList();
</script>

<style scoped>
.search-form {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin-bottom: 20px;
}

.template-preview {
  max-height: 70vh;
  overflow-y: auto;
}

.mt-2 {
  margin-top: 8px;
}

.mt-4 {
  margin-top: 16px;
}
</style>