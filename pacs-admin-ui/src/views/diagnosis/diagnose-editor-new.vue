<template>
  <div class="app-container diagnosis-report">
    <el-card class="report-card">
      <!-- 申请信息 -->
      <div class="apply-info">
        <h3>申请信息</h3>
        <div class="patient-info">
          <div class="info-row">
            <div class="info-item">
              <span class="label">患者姓名：</span>
              <span class="value">{{ patientInfo.name }}</span>
            </div>
            <div class="info-item">
              <span class="label">{{ patientInfo.gender === 'Female' ? '女' : '男' }}</span>
              <span class="value">{{ patientInfo.age }}岁</span>
            </div>
            <div class="info-item">
              <span class="label">检查时间：</span>
              <span class="value">{{ formatTime(patientInfo.examTime) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 报告标签页 -->
      <div class="report-tabs">
        <div class="tabs-header">
          <el-button 
            v-for="tab in tabs" 
            :key="tab.name"
            :class="['tab-button', { active: activeTab === tab.name }]"
            @click="activeTab = tab.name"
          >
            {{ tab.label }}
          </el-button>
        </div>
      </div>

      <!-- 报告内容 -->
      <div class="report-content">
        <!-- 医院信息 -->
        <div class="hospital-info">
          <div class="hospital-name">鄂托克旗人民医院</div>
          <div class="report-title">CT报告单</div>
        </div>

        <!-- 患者基本信息 -->
        <div class="patient-info-grid">
          <div class="info-row">
            <div class="info-item">
              <span class="info-label">影像号：</span>
              <span class="info-value">{{ reportData.imageId || 'S-202505153560' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">检查时间：</span>
              <span class="info-value">{{ formatTime(reportData.examTime) || '2025-05-15' }}</span>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <span class="info-label">病人姓名：</span>
              <span class="info-value">{{ reportData.patientName || '张润琴' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">性别：</span>
              <span class="info-value">{{ reportData.gender === 'Male' ? '男' : '女' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">年龄：</span>
              <span class="info-value">{{ reportData.age || '67' }}岁</span>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <span class="info-label">科室：</span>
              <span class="info-value">{{ reportData.department || '' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">设备类型：</span>
              <span class="info-value">{{ reportData.deviceType || 'CT' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">检查部位：</span>
              <span class="info-value">{{ reportData.examPart || 'BRAIN' }}</span>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <span class="info-label">住院号：</span>
              <span class="info-value">{{ reportData.inPatientNo || '' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">床号：</span>
              <span class="info-value">{{ reportData.bedNo || '' }}</span>
            </div>
          </div>
        </div>

        <div class="report-divider"></div>

        <!-- 报告编辑区域 -->
        <div class="report-edit-area">
          <div class="findings-section">
            <div class="section-title">影像结论：</div>
            <div class="section-content">
              <div class="editor-toolbar">
                <button class="toolbar-btn" title="加粗"><b>B</b></button>
                <button class="toolbar-btn" title="斜体"><i>I</i></button>
                <button class="toolbar-btn" title="下划线"><u>U</u></button>
                <button class="toolbar-btn" title="删除线"><s>S</s></button>
                <button class="toolbar-btn" title="撤销">↩</button>
                <button class="toolbar-btn" title="重做">↪</button>
              </div>
              <el-input
                v-model="reportData.findings"
                type="textarea"
                :rows="8"
                placeholder="请输入影像所见内容"
                :disabled="isReadOnly"
              ></el-input>
            </div>
          </div>

          <div class="diagnosis-section">
            <div class="section-title">诊断结果：</div>
            <div class="section-content">
              <el-input
                v-model="reportData.diagnosis"
                type="textarea"
                :rows="4"
                placeholder="请输入诊断结果"
                :disabled="isReadOnly"
              ></el-input>
            </div>
          </div>

          <!-- 签名区域 -->
          <div class="signature-area">
            <div class="signature-row">
              <div class="signature-item">
                <span class="signature-label">报告医师：</span>
                <span class="signature-value">{{ reportData.reportDoctor || '' }}</span>
              </div>
              <div class="signature-item">
                <span class="signature-label">审核医师：</span>
                <span class="signature-value">{{ reportData.auditDoctor || '' }}</span>
              </div>
              <div class="signature-item">
                <span class="signature-label">报告时间：</span>
                <span class="signature-value">{{ formatTime(reportData.reportTime) || '' }}</span>
              </div>
            </div>
          </div>

          <div class="report-note">
            <p>注：此报告仅对本影像负责，如有疑问请与医院联系。</p>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="report-actions">
        <div class="action-buttons">
          <el-button type="primary" @click="onSave" :disabled="isReadOnly">
            <el-icon><Document /></el-icon>
            保存
          </el-button>
          <el-button type="success" @click="onSubmit" :disabled="isReadOnly">
            <el-icon><Check /></el-icon>
            提交
          </el-button>
          <el-button type="warning" @click="onAudit" :disabled="isReadOnly || !hasAuditPermission">
            <el-icon><Stamp /></el-icon>
            审核
          </el-button>
          <el-button @click="onPrint">
            <el-icon><Printer /></el-icon>
            打印
          </el-button>
          <el-button @click="onDownload">
            <el-icon><Download /></el-icon>
            下载
          </el-button>
          <el-button @click="onShare">
            <el-icon><Share /></el-icon>
            分享
          </el-button>
        </div>
      </div>

      <!-- 右侧快捷操作 -->
      <div class="quick-actions">
        <el-button circle type="primary" title="查看影像" @click="viewImages">
          <el-icon><PictureFilled /></el-icon>
        </el-button>
        <el-button circle title="申请" @click="onApply">
          <el-icon><Document /></el-icon>
        </el-button>
        <el-button circle title="下载" @click="onDownload">
          <el-icon><Download /></el-icon>
        </el-button>
        <el-button circle title="打印" @click="onPrint">
          <el-icon><Printer /></el-icon>
        </el-button>
        <el-button circle title="分享" @click="onShare">
          <el-icon><Share /></el-icon>
        </el-button>
      </div>
    </el-card>

    <!-- 影像查看对话框 -->
    <el-dialog
      v-model="imageViewerVisible"
      title="影像查看器"
      fullscreen
      :destroy-on-close="false"
      :show-close="true"
    >
      <div class="image-viewer-container">
        <iframe :src="viewerUrl" frameborder="0" class="full-iframe"></iframe>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Document, Check, Stamp, Printer, Download, Share, PictureFilled } from '@element-plus/icons-vue';
import { parseTime } from '@/utils/ruoyi';
import { getStudy, syncDicomData } from '@/api/pacs/study';
import { 
  getDiagnosis, 
  addDiagnosis, 
  updateDiagnosis, 
  auditDiagnosis as apiAuditDiagnosis,
  exportDiagnosisReport,
  shareDiagnosis
} from '@/api/diagnosis/diagnosis';
import useUserStore from '@/store/modules/user';

const route = useRoute();
const userStore = useUserStore();

// 获取URL参数
const studyId = ref(route.query.studyId);
const diagnosisId = ref(route.query.diagnosisId);

// 页签数据
const tabs = [
  { name: 'report', label: '本院报告' },
  { name: 'remote', label: '远程报告' },
  { name: 'specialist', label: '专家报告' },
  { name: 'ris', label: 'RIS报告' },
  { name: 'ai', label: 'AI报告' }
];
const activeTab = ref('report');

// 患者基本信息
const patientInfo = reactive({
  name: '',
  gender: '',
  age: '',
  examTime: ''
});

// 报告数据
const reportData = reactive({
  imageId: '',
  examTime: '',
  patientName: '',
  gender: '',
  age: '',
  department: '',
  deviceType: '',
  examPart: '',
  inPatientNo: '',
  bedNo: '',
  findings: '',
  diagnosis: '',
  reportDoctor: '',
  auditDoctor: '',
  reportTime: '',
  status: '0' // 0:草稿, 1:待审核, 2:已审核
});

// 权限和状态
const isReadOnly = computed(() => {
  // 如果状态为已审核(2)或当前用户不是报告医师，则为只读
  return reportData.status === '2' || 
    (reportData.reportDoctor && reportData.reportDoctor !== userStore.name);
});

const hasAuditPermission = computed(() => {
  // 判断是否有审核权限
  return userStore.permissions.includes('diagnosis:diagnosis:audit');
});

// 影像查看器相关
const imageViewerVisible = ref(false);
const viewerUrl = ref('');

// 格式化时间
const formatTime = (time, format = '{y}-{m}-{d} {h}:{i}:{s}') => {
  if (!time) return '';
  return parseTime(time, format);
};

// 加载检查和诊断数据
const loadData = async () => {
  if (!studyId.value) {
    ElMessage.warning('未找到检查ID');
    return;
  }

  try {
    // 加载检查数据
    const studyRes = await getStudy(studyId.value);
    if (studyRes.code === 200 && studyRes.data) {
      const studyData = studyRes.data;
      
      // 更新患者信息
      patientInfo.name = studyData.patientName;
      patientInfo.gender = studyData.patientSex;
      patientInfo.age = calculateAge(studyData.patientBirthday);
      patientInfo.examTime = studyData.checkFinishTime;
      
      // 更新报告基本信息
      reportData.imageId = studyData.originalPatientId;
      reportData.examTime = studyData.checkFinishTime;
      reportData.patientName = studyData.patientName;
      reportData.gender = studyData.patientSex;
      reportData.age = calculateAge(studyData.patientBirthday);
      reportData.department = studyData.examDepartment;
      reportData.deviceType = studyData.modality;
      reportData.examPart = studyData.organ;
      reportData.inPatientNo = studyData.inPatientId;
      reportData.bedNo = studyData.bedNo;
    }

    // 如果有诊断ID，加载诊断数据
    if (diagnosisId.value) {
      const diagnosisRes = await getDiagnosis(diagnosisId.value);
      if (diagnosisRes.code === 200 && diagnosisRes.data) {
        const diagnosisData = diagnosisRes.data;
        
        reportData.findings = diagnosisData.diagnose;
        reportData.diagnosis = diagnosisData.recommendation;
        reportData.reportDoctor = diagnosisData.doctor || userStore.name;
        reportData.auditDoctor = diagnosisData.auditBy;
        reportData.reportTime = diagnosisData.createTime;
        reportData.status = diagnosisData.status;
      }
    } else {
      // 新报告默认设置当前用户为报告医师
      reportData.reportDoctor = userStore.name;
      reportData.reportTime = new Date();
    }
  } catch (error) {
    console.error('加载数据失败', error);
    ElMessage.error('加载数据失败');
  }
};

// 计算年龄
const calculateAge = (birthDate) => {
  if (!birthDate) return '0';

  try {
    let birth;
    if (typeof birthDate === 'string') {
      birth = new Date(birthDate.replace(/-/g, '/'));
      if (isNaN(birth.getTime()) && birthDate.length >= 10) {
        birth = new Date(birthDate.substring(0, 10).replace(/-/g, '/'));
      }
    } else if (birthDate instanceof Date) {
      birth = birthDate;
    } else {
      return '0';
    }

    if (isNaN(birth.getTime())) {
      return '0';
    }

    const today = new Date();
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }

    return age.toString();
  } catch (error) {
    console.error('计算年龄时出错:', error);
    return '0';
  }
};

// 保存报告
const onSave = async () => {
  if (!reportData.findings) {
    ElMessage.warning('请填写影像结论');
    return;
  }

  try {
    const data = {
      id: diagnosisId.value,
      studyId: studyId.value,
      diagnose: reportData.findings,
      recommendation: reportData.diagnosis,
      status: '0', // 草稿状态
      doctor: reportData.reportDoctor || userStore.name
    };

    const api = diagnosisId.value ? updateDiagnosis : addDiagnosis;
    const res = await api(data);

    if (res.code === 200) {
      ElMessage.success('保存成功');
      // 如果是新建报告，更新诊断ID
      if (!diagnosisId.value && res.data && res.data.id) {
        diagnosisId.value = res.data.id;
      }
    } else {
      ElMessage.error(res.msg || '保存失败');
    }
  } catch (error) {
    console.error('保存报告失败', error);
    ElMessage.error('保存报告失败');
  }
};

// 提交报告
const onSubmit = async () => {
  if (!reportData.findings) {
    ElMessage.warning('请填写影像结论');
    return;
  }

  try {
    const data = {
      id: diagnosisId.value,
      studyId: studyId.value,
      diagnose: reportData.findings,
      recommendation: reportData.diagnosis,
      status: '1', // 待审核状态
      doctor: reportData.reportDoctor || userStore.name
    };

    const api = diagnosisId.value ? updateDiagnosis : addDiagnosis;
    const res = await api(data);

    if (res.code === 200) {
      ElMessage.success('提交成功');
      if (!diagnosisId.value && res.data && res.data.id) {
        diagnosisId.value = res.data.id;
      }
      // 更新状态
      reportData.status = '1';
    } else {
      ElMessage.error(res.msg || '提交失败');
    }
  } catch (error) {
    console.error('提交报告失败', error);
    ElMessage.error('提交报告失败');
  }
};

// 审核报告
const onAudit = async () => {
  if (!diagnosisId.value) {
    ElMessage.warning('请先保存报告');
    return;
  }

  try {
    const res = await apiAuditDiagnosis(diagnosisId.value);
    if (res.code === 200) {
      ElMessage.success('审核成功');
      // 更新状态和审核人
      reportData.status = '2';
      reportData.auditDoctor = userStore.name;
    } else {
      ElMessage.error(res.msg || '审核失败');
    }
  } catch (error) {
    console.error('审核报告失败', error);
    ElMessage.error('审核报告失败');
  }
};

// 打印报告
const onPrint = () => {
  window.print();
};

// 下载报告
const onDownload = async () => {
  if (!diagnosisId.value) {
    ElMessage.warning('请先保存报告');
    return;
  }

  try {
    await exportDiagnosisReport(diagnosisId.value);
  } catch (error) {
    console.error('下载报告失败', error);
    ElMessage.error('下载报告失败');
  }
};

// 分享报告
const onShare = async () => {
  if (!diagnosisId.value) {
    ElMessage.warning('请先保存报告');
    return;
  }

  try {
    const res = await shareDiagnosis({
      diagnosisId: diagnosisId.value,
      expireTime: 24 // 24小时过期
    });

    if (res.code === 200 && res.data) {
      ElMessageBox.alert(
        `<div style="word-break:break-all;">${res.data}</div>`, 
        '分享链接', 
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '复制链接',
          callback: () => {
            navigator.clipboard.writeText(res.data)
              .then(() => ElMessage.success('链接已复制'))
              .catch(() => ElMessage.error('复制失败'));
          }
        }
      );
    } else {
      ElMessage.error(res.msg || '生成分享链接失败');
    }
  } catch (error) {
    console.error('分享报告失败', error);
    ElMessage.error('分享报告失败');
  }
};

// 申请操作
const onApply = () => {
  ElMessage.info('申请功能暂未实现');
};

// 查看影像
const viewImages = async () => {
  if (!studyId.value) {
    ElMessage.warning('未找到检查ID');
    return;
  }

  try {
    // 构建影像查看器URL
    const patientId = reportData.imageId;
    if (patientId) {
      // 全屏查看使用桌面端查看器
      const baseUrl = 'https://xgyyx.etkqrmyy.com/desktop/index.html';
      const params = new URLSearchParams({
        studyId: studyId.value,
        fullscreen: 'true',
        mode: 'advanced'
      });
      viewerUrl.value = `${baseUrl}?${params.toString()}`;
      imageViewerVisible.value = true;
    } else {
      ElMessage.warning('未找到影像数据');
    }
  } catch (error) {
    console.error('查看影像失败', error);
    ElMessage.error('查看影像失败');
  }
};

onMounted(() => {
  loadData();
});
</script>

<style lang="scss" scoped>
.diagnosis-report {
  position: relative;
}

.report-card {
  position: relative;
  margin-bottom: 20px;
}

.apply-info {
  margin-bottom: 15px;
  
  h3 {
    font-size: 18px;
    margin-bottom: 10px;
    border-left: 4px solid #409EFF;
    padding-left: 10px;
  }
}

.patient-info {
  background-color: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  
  .info-row {
    display: flex;
    flex-wrap: wrap;
  }
  
  .info-item {
    margin-right: 20px;
    margin-bottom: 5px;
    
    .label {
      font-weight: 500;
      margin-right: 5px;
    }
  }
}

.report-tabs {
  margin-bottom: 15px;
  
  .tabs-header {
    display: flex;
    border-bottom: 1px solid #e4e7ed;
    
    .tab-button {
      padding: 8px 16px;
      border: none;
      background: none;
      border-radius: 0;
      margin-right: 5px;
      
      &.active {
        color: #409EFF;
        border-bottom: 2px solid #409EFF;
      }
    }
  }
}

.report-content {
  background-color: #fff;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.hospital-info {
  text-align: center;
  margin-bottom: 15px;
  
  .hospital-name {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 5px;
  }
  
  .report-title {
    font-size: 16px;
    font-weight: 500;
  }
}

.patient-info-grid {
  margin-bottom: 15px;
  
  .info-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10px;
  }
  
  .info-item {
    flex: 1;
    min-width: 200px;
    display: flex;
    align-items: center;
    
    .info-label {
      font-weight: 500;
      margin-right: 5px;
      min-width: 80px;
    }
  }
}

.report-divider {
  height: 1px;
  background-color: #dcdfe6;
  margin: 15px 0;
}

.report-edit-area {
  .findings-section,
  .diagnosis-section {
    margin-bottom: 20px;
  }
  
  .section-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 10px;
    color: #303133;
  }
  
  .section-content {
    margin-bottom: 15px;
  }
  
  .editor-toolbar {
    display: flex;
    background-color: #f5f7fa;
    border: 1px solid #dcdfe6;
    border-bottom: none;
    padding: 5px;
    border-radius: 4px 4px 0 0;
    
    .toolbar-btn {
      margin-right: 5px;
      padding: 2px 8px;
      background: none;
      border: 1px solid transparent;
      cursor: pointer;
      border-radius: 3px;
      
      &:hover {
        background-color: #ecf5ff;
        border-color: #c6e2ff;
      }
    }
  }
}

.signature-area {
  margin-top: 30px;
  
  .signature-row {
    display: flex;
    justify-content: space-between;
  }
  
  .signature-item {
    .signature-label {
      font-weight: 500;
      margin-right: 5px;
    }
  }
}

.report-note {
  margin-top: 20px;
  font-size: 12px;
  color: #909399;
  text-align: center;
}

.report-actions {
  margin-top: 20px;
  
  .action-buttons {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
  }
}

.quick-actions {
  position: fixed;
  right: 30px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 10;
}

.image-viewer-container {
  height: 100%;
  
  .full-iframe {
    width: 100%;
    height: calc(100vh - 60px);
    border: none;
  }
}

@media print {
  .report-actions,
  .quick-actions,
  .apply-info,
  .report-tabs {
    display: none;
  }
  
  .report-content {
    border: none;
    box-shadow: none;
  }
}
</style> 