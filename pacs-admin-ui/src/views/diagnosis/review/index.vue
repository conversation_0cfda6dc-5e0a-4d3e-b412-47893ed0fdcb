<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="诊断医生" prop="doctor">
        <el-input
          v-model="queryParams.doctor"
          placeholder="请输入诊断医生"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="患者ID" prop="patientId">
        <el-input
          v-model="queryParams.patientId"
          placeholder="请输入患者ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="检查编号" prop="studyId">
        <el-input
          v-model="queryParams.studyId"
          placeholder="请输入检查编号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="诊断类型" prop="diagnosisType">
        <el-select v-model="queryParams.diagnosisType" placeholder="请选择诊断类型" clearable>
          <el-option
            v-for="dict in diagnosis_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="View"
          @click="handleView"
          v-hasPermi="['diagnosis:review:query']"
        >查看</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Check"
          @click="handleApprove"
          v-hasPermi="['diagnosis:review:approve']"
        >通过</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Close"
          @click="handleReject"
          v-hasPermi="['diagnosis:review:reject']"
        >驳回</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Edit"
          @click="handleEdit"
          v-hasPermi="['diagnosis:diagnosis:edit']"
        >修改</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="diagnosisList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="患者ID" align="center" prop="patientId" :show-overflow-tooltip="true" />
      <el-table-column label="检查编号" align="center" prop="studyId" :show-overflow-tooltip="true" />
      <el-table-column label="诊断医生" align="center" prop="doctor" />
      <el-table-column label="诊断时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="诊断类型" align="center" prop="diagnosisType">
        <template #default="scope">
          <dict-tag :options="diagnosis_type" :value="scope.row.diagnosisType"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            type="text"
            icon="View"
            @click="handleView(scope.row)"
            v-hasPermi="['diagnosis:review:query']"
          >查看</el-button>
          <el-button
            type="text"
            icon="Check"
            @click="handleApprove(scope.row)"
            v-hasPermi="['diagnosis:review:approve']"
          >通过</el-button>
          <el-button
            type="text"
            icon="Close"
            @click="handleReject(scope.row)"
            v-hasPermi="['diagnosis:review:reject']"
          >驳回</el-button>
          <el-button
            type="text"
            icon="Edit"
            @click="handleEdit(scope.row)"
            v-hasPermi="['diagnosis:diagnosis:edit']"
          >修改</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 审核对话框 -->
    <el-dialog :title="reviewTitle" v-model="reviewOpen" width="500px" append-to-body>
      <el-form ref="reviewFormRef" :model="reviewForm" :rules="reviewRules" label-width="80px">
        <el-form-item label="审核意见" prop="reviewComment">
          <el-input v-model="reviewForm.reviewComment" type="textarea" placeholder="请输入审核意见" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitReview">确 定</el-button>
          <el-button @click="cancelReview">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="DiagnosisReview">
import { listPendingReview, getDiagnosis, approveReview, rejectReview } from "@/api/diagnosis/diagnosis";
import { parseTime } from '@/utils/ruoyi';
import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter } from 'vue-router';

const router = useRouter();
const { proxy } = getCurrentInstance();
const { diagnosis_type } = proxy.useDict('diagnosis_type');

// 遮罩层
const loading = ref(false);
// 选中数组
const ids = ref([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 诊断表格数据
const diagnosisList = ref([]);
// 审核对话框标题
const reviewTitle = ref("");
// 是否显示审核对话框
const reviewOpen = ref(false);
// 审核操作类型
const reviewType = ref("");
// 当前选中的诊断ID
const currentDiagnosisId = ref(null);

// 审核表单
const reviewForm = reactive({
  id: null,
  reviewer: null,
  reviewComment: ""
});

// 审核表单校验规则
const reviewRules = {
  reviewComment: [
    { required: true, message: "审核意见不能为空", trigger: "blur" }
  ]
};

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  doctor: null,
  patientId: null,
  studyId: null,
  diagnosisType: null
});

/** 查询待审核诊断列表 */
function getList() {
  loading.value = true;
  listPendingReview(queryParams).then(response => {
    diagnosisList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1;
  getList();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 查看按钮操作 */
function handleView(row) {
  const id = row.id || ids.value[0];
  router.push({ path: '/diagnosis/view', query: { diagnosisId: id } });
}

/** 修改按钮操作 */
function handleEdit(row) {
  const id = row.id || ids.value[0];
  router.push({ path: '/diagnosis/editor', query: { diagnosisId: id } });
}

/** 审核通过按钮操作 */
function handleApprove(row) {
  const id = row.id || ids.value[0];
  if (!id) {
    ElMessage.warning("请选择要审核的诊断报告");
    return;
  }
  
  reviewTitle.value = "审核通过";
  reviewType.value = "approve";
  reviewOpen.value = true;
  currentDiagnosisId.value = id;
  
  // 重置表单
  if (proxy.$refs.reviewFormRef) {
    proxy.$refs.reviewFormRef.resetFields();
  }
  reviewForm.id = id;
  reviewForm.reviewer = proxy.getUsername();
  reviewForm.reviewComment = "";
}

/** 审核驳回按钮操作 */
function handleReject(row) {
  const id = row.id || ids.value[0];
  if (!id) {
    ElMessage.warning("请选择要驳回的诊断报告");
    return;
  }
  
  reviewTitle.value = "审核驳回";
  reviewType.value = "reject";
  reviewOpen.value = true;
  currentDiagnosisId.value = id;
  
  // 重置表单
  if (proxy.$refs.reviewFormRef) {
    proxy.$refs.reviewFormRef.resetFields();
  }
  reviewForm.id = id;
  reviewForm.reviewer = proxy.getUsername();
  reviewForm.reviewComment = "";
}

/** 提交审核 */
function submitReview() {
  proxy.$refs.reviewFormRef.validate(valid => {
    if (valid) {
      if (reviewType.value === "approve") {
        approveReview(reviewForm).then(response => {
          ElMessage.success("审核通过成功");
          reviewOpen.value = false;
          getList();
        });
      } else if (reviewType.value === "reject") {
        rejectReview(reviewForm).then(response => {
          ElMessage.success("审核驳回成功");
          reviewOpen.value = false;
          getList();
        });
      }
    }
  });
}

/** 取消审核 */
function cancelReview() {
  reviewOpen.value = false;
}

onMounted(() => {
  getList();
});
</script>
