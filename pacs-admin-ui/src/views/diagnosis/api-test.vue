<template>
  <div class="api-test">
    <h2>诊断API测试页面</h2>
    
    <!-- 测试控制面板 -->
    <el-card>
      <template #header>
        <span>API测试控制面板</span>
      </template>
      
      <div class="test-controls">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-form-item label="患者姓名">
              <el-input v-model="testParams.patientName" placeholder="请输入患者姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="患者ID">
              <el-input v-model="testParams.patientId" placeholder="请输入患者ID" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="检查编号">
              <el-input v-model="testParams.examCode" placeholder="请输入检查编号" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="诊断状态">
              <el-select v-model="testParams.diagnosisStatus" placeholder="选择状态">
                <el-option label="全部" value="" />
                <el-option label="待诊断" value="-1" />
                <el-option label="已诊断" value="1" />
                <el-option label="已审核" value="2" />
                <el-option label="院内诊断" value="9" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <div class="button-group">
          <el-button type="primary" @click="testListAPI" :loading="loading">
            测试列表API
          </el-button>
          <el-button @click="clearParams">清空参数</el-button>
          <el-button @click="clearResult">清空结果</el-button>
        </div>
      </div>
    </el-card>

    <!-- API结果显示 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <span>API调用结果</span>
          <div>
            <el-tag v-if="lastApiCall.success !== null" 
                    :type="lastApiCall.success ? 'success' : 'danger'">
              {{ lastApiCall.success ? '成功' : '失败' }}
            </el-tag>
            <span v-if="lastApiCall.time" style="margin-left: 10px; font-size: 12px; color: #999;">
              {{ lastApiCall.time }}
            </span>
          </div>
        </div>
      </template>
      
      <div class="result-container">
        <el-tabs v-model="activeResultTab">
          <el-tab-pane label="请求参数" name="request">
            <pre class="json-display">{{ formatJson(lastApiCall.request) }}</pre>
          </el-tab-pane>
          <el-tab-pane label="响应数据" name="response">
            <pre class="json-display">{{ formatJson(lastApiCall.response) }}</pre>
          </el-tab-pane>
          <el-tab-pane label="数据列表" name="data" v-if="lastApiCall.response?.rows">
            <el-table :data="lastApiCall.response.rows" style="width: 100%" max-height="400">
              <el-table-column prop="patientName" label="患者姓名" width="120" />
              <el-table-column prop="originalPatientId" label="患者ID" width="120" />
              <el-table-column prop="examCode" label="检查编号" width="150" />
              <el-table-column prop="modality" label="检查类型" width="100" />
              <el-table-column prop="hospitalName" label="医院" width="150" />
              <el-table-column prop="diagnosisStatus" label="诊断状态" width="100">
                <template #default="scope">
                  <el-tag :type="getDiagnosisStatusType(scope.row.diagnosisStatus)">
                    {{ getDiagnosisStatusText(scope.row.diagnosisStatus) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="checkFinishTime" label="检查时间" width="180" />
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>

    <!-- 统计信息 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <span>统计信息</span>
      </template>
      
      <el-row :gutter="16">
        <el-col :span="6">
          <el-statistic title="总记录数" :value="lastApiCall.response?.total || 0" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="当前页记录" :value="lastApiCall.response?.rows?.length || 0" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="响应时间" :value="lastApiCall.duration || 0" suffix="ms" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="API状态码" :value="lastApiCall.response?.code || 0" />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { listWithDiagnose } from '@/api/pacs/study'

// 响应式数据
const loading = ref(false)
const activeResultTab = ref('request')

// 测试参数
const testParams = reactive({
  patientName: '',
  patientId: '',
  examCode: '',
  diagnosisStatus: '',
  pageNum: 1,
  pageSize: 20
})

// API调用记录
const lastApiCall = reactive({
  success: null,
  time: '',
  duration: 0,
  request: null,
  response: null
})

// 测试列表API
const testListAPI = async () => {
  loading.value = true
  const startTime = Date.now()
  
  try {
    // 构建请求参数
    const params = {
      pageNum: testParams.pageNum,
      pageSize: testParams.pageSize
    }
    
    // 只添加非空参数
    if (testParams.patientName.trim()) {
      params.patientName = testParams.patientName.trim()
    }
    if (testParams.patientId.trim()) {
      params.patientId = testParams.patientId.trim()
    }
    if (testParams.examCode.trim()) {
      params.examCode = testParams.examCode.trim()
    }
    if (testParams.diagnosisStatus) {
      params.diagnosisStatus = testParams.diagnosisStatus
    }
    
    console.log('发送API请求:', params)
    
    // 调用API
    const response = await listWithDiagnose(params)
    const endTime = Date.now()
    
    // 记录结果
    lastApiCall.success = response.code === 200
    lastApiCall.time = new Date().toLocaleString()
    lastApiCall.duration = endTime - startTime
    lastApiCall.request = params
    lastApiCall.response = response
    
    if (response.code === 200) {
      ElMessage.success(`API调用成功，获取到 ${response.total} 条记录`)
      activeResultTab.value = 'data'
    } else {
      ElMessage.error(`API调用失败：${response.msg}`)
      activeResultTab.value = 'response'
    }
    
  } catch (error) {
    const endTime = Date.now()
    
    lastApiCall.success = false
    lastApiCall.time = new Date().toLocaleString()
    lastApiCall.duration = endTime - startTime
    lastApiCall.request = testParams
    lastApiCall.response = { error: error.message, stack: error.stack }
    
    console.error('API调用出错:', error)
    ElMessage.error(`API调用出错：${error.message}`)
    activeResultTab.value = 'response'
  } finally {
    loading.value = false
  }
}

// 清空参数
const clearParams = () => {
  Object.assign(testParams, {
    patientName: '',
    patientId: '',
    examCode: '',
    diagnosisStatus: '',
    pageNum: 1,
    pageSize: 20
  })
}

// 清空结果
const clearResult = () => {
  Object.assign(lastApiCall, {
    success: null,
    time: '',
    duration: 0,
    request: null,
    response: null
  })
}

// 格式化JSON
const formatJson = (obj) => {
  if (!obj) return '暂无数据'
  return JSON.stringify(obj, null, 2)
}

// 获取诊断状态类型 - 只保留系统实际使用的4个状态
const getDiagnosisStatusType = (status) => {
  const typeMap = {
    '-1': 'info',     // 待诊断
    '1': 'primary',   // 已诊断
    '2': 'success',   // 已审核
    '9': 'success'    // 院内诊断
  }
  return typeMap[status] || 'info'
}

// 获取诊断状态文本 - 只保留系统实际使用的4个状态
const getDiagnosisStatusText = (status) => {
  const textMap = {
    '-1': '待诊断',
    '1': '已诊断',
    '2': '已审核',
    '9': '院内诊断'
  }
  return textMap[status] || '未知'
}
</script>

<style scoped>
.api-test {
  padding: 20px;
}

.test-controls {
  padding: 20px 0;
}

.button-group {
  margin-top: 20px;
  display: flex;
  gap: 10px;
}

.result-container {
  min-height: 300px;
}

.json-display {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
  max-height: 400px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
