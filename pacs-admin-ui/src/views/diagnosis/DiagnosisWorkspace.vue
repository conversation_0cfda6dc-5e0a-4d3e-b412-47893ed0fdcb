<template>
  <div class="diagnosis-workspace">
    <!-- 顶部搜索栏 -->
    <div class="top-search-bar">
      <SearchBar
        :queryParams="queryParams"
        @update:queryParams="handleUpdateQueryParams"
        @search="handleSearch"
        @reset="handleReset"
        @template-manage="handleTemplateManage"
      />
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧病人列表 -->
      <div class="left-panel">
        <PatientList
          :loading="loading"
          :data="patientList"
          :total="total"
          :current-patient="currentPatient"
          @patient-select="handlePatientSelect"
          @page-change="handlePageChange"
        />
      </div>

      <!-- 中间信息展示区域 -->
      <div class="center-panel">
        <InfoTabs
          ref="infoTabsRef"
          v-if="currentPatient"
          :patient-data="currentPatient"
          :diagnosis-data="currentDiagnosis"
          @diagnosis-save="handleDiagnosisSave"
          @diagnosis-submit="handleDiagnosisSubmit"
          @diagnosis-audit="handleDiagnosisAudit"
        />
        <div v-else class="no-patient-selected">
          <el-empty description="请选择患者查看详细信息" />
        </div>
      </div>

      <!-- 右侧模板选择区域 -->
      <div class="right-panel">
        <TemplatePanel
          v-if="currentPatient"
          :modality="currentPatient.modality"
          :body-part="currentPatient.organ"
          @template-apply="handleTemplateApply"
          @add-template="handleAddTemplate"
        />
      </div>
    </div>

    <!-- 模板管理对话框 -->
    <el-dialog
      v-model="templateDialogVisible"
      title="模板管理"
      width="90%"
      :before-close="handleTemplateDialogClose"
    >
      <TemplateManagement
        v-if="templateDialogVisible"
        @template-apply="handleTemplateApply"
        @close="templateDialogVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import SearchBar from './components/SearchBar.vue'
import PatientList from './components/PatientList.vue'
import InfoTabs from './components/InfoTabs.vue'
import TemplatePanel from './components/TemplatePanel.vue'
import TemplateManagement from './template/index.vue'
import { listWithDiagnose } from '@/api/pacs/study'
import { getDiagnosis } from '@/api/diagnosis/diagnosis'

// 响应式数据
const loading = ref(false)
const total = ref(0)
const patientList = ref([])
const currentPatient = ref(null)
const currentDiagnosis = ref(null)
const infoTabsRef = ref(null)
const templateDialogVisible = ref(false)

// 状态统计数据 - 参考study-list.vue
const statusCounts = reactive({
  pending: 0,    // 待诊断
  diagnosed: 0,  // 已诊断
  audited: 0,    // 已审核
  archived: 0    // 院内诊断
})

// 状态值映射 - 参考study-list.vue
const statusValueMap = {
  pending: '-1',    // 待诊断
  diagnosed: '1',   // 已诊断
  audited: '2',     // 已审核
  archived: '9'     // 院内诊断
}

// 查询参数 - 参考study-list.vue的实现
const queryParams = reactive({
  pageNum: 1,
  pageSize: 20,
  patientId: undefined,
  patientName: undefined,
  examCode: undefined,
  examItem: undefined, // 检查项目
  modality: undefined,
  hospitalId: undefined,
  hospitalName: undefined,
  diagnosisStatus: undefined, // 默认显示全部状态
  organ: undefined, // 检查部位
  examDepartment: undefined, // 申请科室
  examDoctorName: undefined, // 申请医生
  startTime: undefined,
  endTime: undefined
})

// 获取患者列表 - 参考study-list.vue的实现
const getPatientList = async () => {
  loading.value = true
  try {
    // 构建请求参数，过滤undefined值
    const params = {}
    Object.keys(queryParams).forEach(key => {
      const value = queryParams[key]
      // 对于diagnosisStatus，空字符串也是有效值（表示查询全部）
      if (key === 'diagnosisStatus') {
        if (value !== undefined && value !== null) {
          params[key] = value
        }
      } else {
        // 其他参数过滤undefined、null和空字符串
        if (value !== undefined && value !== null && value !== '') {
          params[key] = value
        }
      }
    })



    const res = await listWithDiagnose(params)
    if (res.code === 200) {
      patientList.value = res.rows || []
      total.value = res.total || 0


    } else {
      ElMessage.error(res.msg || '获取患者列表失败')
      patientList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取患者列表出错', error)
    ElMessage.error('系统错误，请联系管理员')
    patientList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 处理查询参数更新
const handleUpdateQueryParams = (newParams) => {
  Object.assign(queryParams, newParams)
}

// 处理搜索
const handleSearch = () => {
  queryParams.pageNum = 1
  getPatientList()
}

// 处理重置 - 参考study-list.vue的实现
const handleReset = () => {
  // 重置所有查询参数为undefined，但保留分页参数
  Object.keys(queryParams).forEach(key => {
    if (key !== 'pageNum' && key !== 'pageSize') {
      queryParams[key] = undefined
    }
  })
  queryParams.pageNum = 1

  // 清空当前选中的患者
  currentPatient.value = null
  currentDiagnosis.value = null

  getPatientList()
}

// 处理患者选择
const handlePatientSelect = async (patient) => {
  currentPatient.value = patient
  
  // 加载诊断数据
  if (patient.diagnosis && patient.diagnosis.id) {
    try {
      const res = await getDiagnosis(patient.diagnosis.id)
      if (res.code === 200) {
        currentDiagnosis.value = res.data
      }
    } catch (error) {
      console.error('获取诊断数据失败', error)
    }
  } else {
    // 新建诊断
    currentDiagnosis.value = {
      studyId: patient.id,
      checkId: patient.id,
      diagnose: '',
      recommendation: '',
      status: '0'
    }
  }
}

// 处理分页变化
const handlePageChange = (page) => {
  queryParams.pageNum = page
  getPatientList()
}

// 处理诊断保存
const handleDiagnosisSave = (diagnosisData) => {
  currentDiagnosis.value = { ...currentDiagnosis.value, ...diagnosisData }
  // 更新患者列表中的诊断状态
  updatePatientDiagnosisStatus()
}

// 处理诊断提交
const handleDiagnosisSubmit = (diagnosisData) => {
  currentDiagnosis.value = { ...currentDiagnosis.value, ...diagnosisData }
  updatePatientDiagnosisStatus()
}

// 处理诊断审核
const handleDiagnosisAudit = (diagnosisData) => {
  currentDiagnosis.value = { ...currentDiagnosis.value, ...diagnosisData }
  updatePatientDiagnosisStatus()
}

// 更新患者列表中的诊断状态
const updatePatientDiagnosisStatus = () => {
  if (currentPatient.value && currentDiagnosis.value) {
    const index = patientList.value.findIndex(p => p.id === currentPatient.value.id)
    if (index !== -1) {
      patientList.value[index].diagnosis = currentDiagnosis.value
    }
  }
}

// 处理模板应用
const handleTemplateApply = (template) => {
  console.log('应用模板:', template)

  // 通过ref调用InfoTabs组件的应用模板方法
  if (infoTabsRef.value) {
    infoTabsRef.value.applyTemplate(template)
    // 关闭模板对话框
    templateDialogVisible.value = false
  } else {
    ElMessage.warning('请先选择患者')
  }
}

// 打开模板管理对话框
const handleTemplateManage = () => {
  templateDialogVisible.value = true
}

// 关闭模板对话框
const handleTemplateDialogClose = () => {
  templateDialogVisible.value = false
}

// 处理添加模板
const handleAddTemplate = () => {
  if (!currentPatient.value) {
    ElMessage.warning('请先选择患者')
    return
  }

  if (!infoTabsRef.value) {
    ElMessage.warning('请先切换到诊断报告页面')
    return
  }

  // 调用InfoTabs的保存为模板功能
  infoTabsRef.value.handleSaveAsTemplate()
}

// 组件挂载时获取数据
onMounted(() => {
  getPatientList()
})

// 暴露方法给父组件
defineExpose({
  refresh: getPatientList,
  selectPatient: handlePatientSelect
})
</script>

<style scoped>
.diagnosis-workspace {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.top-search-bar {
  background: white;
  padding: 4px 6px;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.main-content {
  flex: 1;
  display: flex;
  gap: 6px;
  padding: 6px;
}

.left-panel {
  width: 350px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: 80vh;
  overflow-y: auto;
}

.center-panel {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 80vh;
  overflow-y: auto;
}

.right-panel {
  width: 280px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: 80vh;
  overflow-y: auto;
}

.no-patient-selected {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .right-panel {
    width: 250px;
  }
}

@media (max-width: 1200px) {
  .left-panel {
    width: 300px;
  }
  .right-panel {
    width: 220px;
  }
}

@media (max-width: 1024px) {
  .main-content {
    flex-direction: column;
    gap: 8px;
  }
  
  .left-panel,
  .right-panel {
    width: 100%;
    height: 200px;
  }
  
  .center-panel {
    flex: 1;
    min-height: 400px;
  }
}
</style>
