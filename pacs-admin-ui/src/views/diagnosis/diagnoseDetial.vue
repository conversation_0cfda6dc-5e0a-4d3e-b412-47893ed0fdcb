<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="header-title">检查记录详情</span>
          <div class="header-actions">
            <el-button-group v-if="diagnosis">
              <el-button type="success" @click="handleExportReport" v-hasPermi="['diagnosis:diagnosis:export']">
                <el-icon><document /></el-icon> 导出报告
              </el-button>
              <el-button type="info" @click="showHistoryDrawer = true" v-if="hasMultipleDiagnosis">
                <el-icon><Clock /></el-icon> 历史诊断
              </el-button>
              <el-button type="warning" @click="handleShareDiagnosis">
                <el-icon><share /></el-icon> 分享诊断
              </el-button>
            </el-button-group>
            <el-button type="primary" @click="handleDiagnosis" v-hasPermi="['diagnosis:diagnosis:edit']">
              {{ diagnosisStatus === '2' ? '诊断' : diagnosisStatus === '1' ? '修改诊断' : '查看诊断' }}
            </el-button>
            <el-button @click="handleBack">返回</el-button>
          </div>
        </div>
      </template>

      <div v-loading="loading">
        <el-descriptions v-if="study" :column="2" border>
          <el-descriptions-item label="患者ID">{{ study.patientId }}</el-descriptions-item>
          <el-descriptions-item label="患者姓名">{{ study.patientName }}</el-descriptions-item>
          <el-descriptions-item label="检查编号">{{ study.studyId }}</el-descriptions-item>
          <el-descriptions-item label="检查项目">{{ study.studyDescription }}</el-descriptions-item>
          <el-descriptions-item label="检查日期">{{ parseTime(study.studyDate) }}</el-descriptions-item>
          <el-descriptions-item label="检查机构">{{ study.institutionName || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="检查状态">
            <el-tag>{{ study.status === '0' ? '正常' : '异常' }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="诊断状态">
            <el-tag :type="getDiagnosisStatusType(diagnosisStatus)">
              {{ getDiagnosisStatusLabel(diagnosisStatus) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <el-empty v-else description="未找到检查记录"></el-empty>

        <!-- 检查工具栏 -->
        <el-divider content-position="center">影像预览</el-divider>
        <div class="viewer-toolbar" v-if="study">
          <el-radio-group v-model="viewerMode" size="small" @change="handleViewerModeChange">
            <el-radio-button label="simple">简易查看器</el-radio-button>
            <el-radio-button label="advanced">专业工具</el-radio-button>
          </el-radio-group>
          <el-button-group size="small">
            <el-button type="primary" @click="handleFullscreen">
              <el-icon><full-screen /></el-icon>
            </el-button>
            <el-button type="primary" @click="handleReloadViewer">
              <el-icon><refresh /></el-icon>
            </el-button>
          </el-button-group>
        </div>

        <!-- 预览影像区域 -->
        <div v-if="study" class="viewer-content" ref="viewerContainer">
          <i-frame v-if="viewerUrl" :src="viewerUrl" ref="viewerFrame"></i-frame>
          <div v-else class="empty-viewer">
            <el-empty description="暂无相关影像"></el-empty>
          </div>
        </div>

        <!-- 诊断记录区域 -->
        <div v-if="diagnosis" class="diagnosis-info">
          <el-divider content-position="center">诊断报告</el-divider>
          <div class="diagnosis-header">
            <h3 class="diagnosis-title">{{ diagnosis.title }}</h3>
            <div class="diagnosis-meta">
              <span class="diagnosis-time">诊断时间: {{ parseTime(diagnosis.updateTime || diagnosis.createTime) }}</span>
              <span class="diagnosis-doctor">诊断医生: {{ diagnosis.createBy }}</span>
              <el-tag :type="diagnosisStatus === '0' ? 'success' : 'warning'" size="small">
                {{ getDiagnosisStatusLabel(diagnosisStatus) }}
              </el-tag>
            </div>
          </div>

          <div class="info-content">
            <div class="info-item">
              <div class="item-label">诊断内容</div>
              <div class="item-content text-content">{{ diagnosis.content }}</div>
            </div>

            <div class="info-item">
              <div class="item-label">诊断结论</div>
              <div class="item-content text-content">{{ diagnosis.conclusion }}</div>
            </div>

            <div class="info-item" v-if="diagnosis.remark">
              <div class="item-label">备注</div>
              <div class="item-content text-content">{{ diagnosis.remark }}</div>
            </div>
          </div>
        </div>
        <div v-else-if="!loading && study" class="empty-diagnosis">
          <el-empty description="暂无诊断信息">
            <template #description>
              <p>该检查记录尚未进行诊断</p>
            </template>
            <el-button type="primary" @click="handleDiagnosis">立即诊断</el-button>
          </el-empty>
        </div>
      </div>
    </el-card>

    <!-- 历史诊断抽屉 -->
    <el-drawer
      v-model="showHistoryDrawer"
      title="历史诊断记录"
      direction="rtl"
      size="50%"
    >
      <div v-loading="historyLoading">
        <div v-if="diagnosisHistory.length > 0">
          <div v-for="(item, index) in diagnosisHistory" :key="index" class="history-item">
            <div class="history-header">
              <span class="history-title">{{ item.title }}</span>
              <span class="history-date">{{ parseTime(item.createTime) }}</span>
              <span class="history-doctor">医生: {{ item.createBy }}</span>
              <el-tag size="small" :type="item.status === '0' ? 'warning' : 'success'">
                {{ item.status === '0' ? '草稿' : '已提交' }}
              </el-tag>
            </div>
            <div class="history-content">
              <div class="history-section">
                <div class="section-title">诊断内容:</div>
                <div class="section-content">{{ item.content }}</div>
              </div>
              <div class="history-section">
                <div class="section-title">诊断结论:</div>
                <div class="section-content">{{ item.conclusion }}</div>
              </div>
            </div>
            <div class="history-actions">
              <el-button type="primary" link @click="handleViewHistoryDiagnosis(item)">查看详情</el-button>
              <el-button type="success" link @click="handleExportHistoryReport(item)">导出报告</el-button>
            </div>
          </div>
        </div>
        <el-empty v-else description="没有历史诊断记录"></el-empty>
      </div>
    </el-drawer>

    <!-- 分享诊断对话框 -->
    <el-dialog
      v-model="shareDialogVisible"
      title="分享诊断报告"
      width="500px"
    >
      <div class="share-content">
        <p class="share-tip">您可以将以下链接分享给其他医生或患者，以便他们查看诊断报告：</p>
        <div class="share-link-container">
          <el-input v-model="shareLink" readonly>
            <template #append>
              <el-button @click="copyShareLink">复制</el-button>
            </template>
          </el-input>
        </div>
        <div class="share-qrcode" v-if="shareQrCodeUrl">
          <div class="qrcode-container">
            <img :src="shareQrCodeUrl" alt="分享二维码" />
          </div>
          <p class="qrcode-tip">扫描二维码查看报告</p>
        </div>
        <div class="share-options">
          <el-checkbox v-model="shareWithPassword">设置访问密码</el-checkbox>
          <el-input
            v-if="shareWithPassword"
            v-model="sharePassword"
            placeholder="输入4-6位访问密码"
            maxlength="6"
            show-password
          ></el-input>
          <div class="expire-setting">
            <span>有效期：</span>
            <el-select v-model="shareExpire" placeholder="请选择">
              <el-option label="1天" value="1"></el-option>
              <el-option label="7天" value="7"></el-option>
              <el-option label="30天" value="30"></el-option>
              <el-option label="永久有效" value="-1"></el-option>
            </el-select>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="shareDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="generateShareLink">
            {{ shareLink ? '更新链接' : '生成链接' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Document, Share, Clock, FullScreen, Refresh } from '@element-plus/icons-vue';
import { getStudy } from '@/api/pacs/study';
import { getLatestDiagnosis, listDiagnosis, exportDiagnosisReport, shareDiagnosis } from '@/api/diagnosis/diagnosis';
import { parseTime } from '@/utils/ruoyi';
import iFrame from "@/components/iFrame/index.vue";

const route = useRoute();
const router = useRouter();
const studyId = ref(route.query.id);

const loading = ref(false);
const study = ref(null);
const diagnosis = ref(null);
const diagnosisStatus = ref('2'); // 默认未诊断
const viewerUrl = ref('');
const viewerMode = ref('simple');
const viewerContainer = ref(null);
const viewerFrame = ref(null);
const showHistoryDrawer = ref(false);
const historyLoading = ref(false);
const diagnosisHistory = ref([]);
const hasMultipleDiagnosis = ref(false);

// 分享相关
const shareDialogVisible = ref(false);
const shareLink = ref('');
const shareQrCodeUrl = ref('');
const shareWithPassword = ref(false);
const sharePassword = ref('');
const shareExpire = ref('7'); // 默认7天有效期

// 获取检查记录详情
const getStudyDetail = async () => {
  if (!studyId.value) {
    ElMessage.error('未找到检查记录ID');
    return;
  }

  loading.value = true;
  try {
    const res = await getStudy(studyId.value);
    if (res.code === 200) {
      study.value = res.data;

      // 构建影像阅读器URL
      updateViewerUrl();

      // 获取诊断信息
      await getDiagnosisInfo();

      // 加载历史诊断记录
      await loadDiagnosisHistory();
    } else {
      ElMessage.error(res.msg || '获取检查记录详情失败');
    }
  } catch (error) {
    console.error('获取检查记录详情出错', error);
    ElMessage.error('系统错误，请联系管理员');
  } finally {
    loading.value = false;
  }
};

// 更新查看器URL
const updateViewerUrl = () => {
  viewerUrl.value = `/viewer?studyId=${studyId.value}&mode=${viewerMode.value}`;
};

// 处理查看器模式改变
const handleViewerModeChange = () => {
  updateViewerUrl();
};

// 获取诊断信息
const getDiagnosisInfo = async () => {
  try {
    const res = await getLatestDiagnosis(studyId.value);
    if (res.code === 200 && res.data) {
      diagnosis.value = res.data;
      diagnosisStatus.value = res.data.status;
    } else {
      diagnosis.value = null;
      diagnosisStatus.value = '2'; // 未诊断
    }
  } catch (error) {
    console.error('获取诊断信息出错', error);
    diagnosis.value = null;
    diagnosisStatus.value = '2'; // 未诊断
  }
};

// 加载历史诊断记录
const loadDiagnosisHistory = async () => {
  historyLoading.value = true;
  try {
    const res = await listDiagnosis({
      studyId: studyId.value,
      pageSize: 10,
      pageNum: 1
    });

    if (res.code === 200 && res.rows) {
      diagnosisHistory.value = res.rows;
      hasMultipleDiagnosis.value = diagnosisHistory.value.length > 1;
    }
  } catch (error) {
    console.error('获取历史诊断记录出错', error);
  } finally {
    historyLoading.value = false;
  }
};

/** 获取诊断状态类型 */
const getDiagnosisStatusType = (status) => {
  switch (status) {
    case '0': return 'warning'; // 草稿
    case '1': return 'primary'; // 待审核
    case '2': return 'success'; // 已完成
    case '3': return 'danger';  // 已驳回
    case '4': return 'info';    // 已撤销
    case '5': return 'warning'; // 已修订
    case '6': return 'success'; // 已归档
    default: return 'info';     // 未诊断
  }
};

/** 获取诊断状态文本 */
const getDiagnosisStatusLabel = (status) => {
  switch (status) {
    case '0': return '草稿';
    case '1': return '待审核';
    case '2': return '已完成';
    case '3': return '已驳回';
    case '4': return '已撤销';
    case '5': return '已修订';
    case '6': return '已归档';
    default: return '未诊断';
  }
};

// 返回按钮操作
const handleBack = () => {
  router.push('/pacs/study-list');
};

// 诊断按钮操作
const handleDiagnosis = () => {
  if (diagnosis.value) {
    router.push({
      path: '/diagnosis/editor',
      query: { studyId: studyId.value, diagnosisId: diagnosis.value.id }
    });
  } else {
    router.push({
      path: '/diagnosis/editor',
      query: { studyId: studyId.value }
    });
  }
};

// 导出诊断报告
const handleExportReport = async () => {
  if (!diagnosis.value) {
    ElMessage.warning('没有可导出的诊断报告');
    return;
  }

  try {
    const res = await exportDiagnosisReport(diagnosis.value.id);
    if (res.code === 200) {
      // 创建下载链接
      const blob = new Blob([res.data], { type: 'application/pdf' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `诊断报告_${study.value.patientName}_${parseTime(new Date(), '{y}{m}{d}')}.pdf`;
      link.click();
      URL.revokeObjectURL(link.href);

      ElMessage.success('导出成功');
    } else {
      ElMessage.error(res.msg || '导出失败');
    }
  } catch (error) {
    console.error('导出报告出错', error);
    ElMessage.error('系统错误，请联系管理员');
  }
};

// 导出历史诊断报告
const handleExportHistoryReport = async (historyItem) => {
  try {
    const res = await exportDiagnosisReport(historyItem.id);
    if (res.code === 200) {
      // 创建下载链接
      const blob = new Blob([res.data], { type: 'application/pdf' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `诊断报告_${study.value.patientName}_${parseTime(new Date(historyItem.createTime), '{y}{m}{d}')}.pdf`;
      link.click();
      URL.revokeObjectURL(link.href);

      ElMessage.success('导出成功');
    } else {
      ElMessage.error(res.msg || '导出失败');
    }
  } catch (error) {
    console.error('导出报告出错', error);
    ElMessage.error('系统错误，请联系管理员');
  }
};

// 查看历史诊断
const handleViewHistoryDiagnosis = (historyItem) => {
  router.push({
    path: '/diagnosis/view',
    query: { studyId: studyId.value, diagnosisId: historyItem.id }
  });
};

// 全屏显示影像
const handleFullscreen = () => {
  if (viewerContainer.value) {
    if (viewerContainer.value.requestFullscreen) {
      viewerContainer.value.requestFullscreen();
    } else if (viewerContainer.value.webkitRequestFullScreen) {
      viewerContainer.value.webkitRequestFullScreen();
    } else if (viewerContainer.value.mozRequestFullScreen) {
      viewerContainer.value.mozRequestFullScreen();
    } else if (viewerContainer.value.msRequestFullscreen) {
      viewerContainer.value.msRequestFullscreen();
    }
  }
};

// 重新加载查看器
const handleReloadViewer = () => {
  if (viewerFrame.value) {
    viewerFrame.value.$el.contentWindow.location.reload();
  }
};

// 打开分享对话框
const handleShareDiagnosis = () => {
  if (!diagnosis.value) {
    ElMessage.warning('没有可分享的诊断报告');
    return;
  }

  shareDialogVisible.value = true;
  shareLink.value = '';
  shareQrCodeUrl.value = '';
};

// 生成分享链接
const generateShareLink = async () => {
  if (!diagnosis.value) return;

  try {
    const params = {
      diagnosisId: diagnosis.value.id,
      expire: shareExpire.value
    };

    if (shareWithPassword) {
      if (!sharePassword.value || sharePassword.value.length < 4) {
        ElMessage.warning('请设置至少4位的访问密码');
        return;
      }
      params.password = sharePassword.value;
    }

    const res = await shareDiagnosis(params);
    if (res.code === 200) {
      shareLink.value = res.data.url;
      shareQrCodeUrl.value = res.data.qrcode;
      ElMessage.success('生成分享链接成功');
    } else {
      ElMessage.error(res.msg || '生成分享链接失败');
    }
  } catch (error) {
    console.error('生成分享链接出错', error);
    ElMessage.error('系统错误，请联系管理员');
  }
};

// 复制分享链接
const copyShareLink = () => {
  if (!shareLink.value) return;

  const input = document.createElement('input');
  input.value = shareLink.value;
  document.body.appendChild(input);
  input.select();
  document.execCommand('copy');
  document.body.removeChild(input);

  ElMessage.success('链接已复制到剪贴板');
};

onMounted(() => {
  getStudyDetail();
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  font-size: 18px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.viewer-toolbar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.viewer-content {
  height: 600px;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  overflow: hidden;
  margin-top: 10px;
}

.empty-viewer {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.diagnosis-info {
  margin-top: 20px;
}

.diagnosis-header {
  margin-bottom: 15px;
}

.diagnosis-title {
  margin: 0 0 10px 0;
  font-size: 18px;
  color: #303133;
}

.diagnosis-meta {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #909399;
  font-size: 14px;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.item-label {
  font-weight: bold;
  color: #606266;
  font-size: 14px;
}

.item-content {
  color: #303133;
}

.text-content {
  white-space: pre-wrap;
  line-height: 1.6;
  background-color: #f8f8f8;
  padding: 10px;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.empty-diagnosis {
  margin: 40px 0;
  text-align: center;
}

.history-item {
  margin-bottom: 16px;
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fff;
}

.history-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.history-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  flex-wrap: wrap;
  gap: 8px;
}

.history-title {
  font-weight: bold;
  margin-right: 12px;
}

.history-date, .history-doctor {
  font-size: 12px;
  color: #909399;
  margin-right: 12px;
}

.history-content {
  margin-top: 8px;
}

.history-section {
  margin-bottom: 8px;
}

.section-title {
  font-weight: bold;
  margin-bottom: 4px;
  color: #606266;
}

.section-content {
  white-space: pre-wrap;
  line-height: 1.5;
  color: #303133;
  background-color: #f8f8f8;
  padding: 8px;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
}

.history-actions {
  margin-top: 8px;
  display: flex;
  justify-content: flex-end;
}

.share-content {
  padding: 10px;
}

.share-tip {
  margin-bottom: 15px;
  color: #606266;
}

.share-link-container {
  margin-bottom: 20px;
}

.share-qrcode {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 10px 0 20px;
}

.qrcode-container {
  width: 200px;
  height: 200px;
  overflow: hidden;
  margin-bottom: 10px;
}

.qrcode-container img {
  width: 100%;
  height: 100%;
}

.qrcode-tip {
  color: #909399;
  font-size: 14px;
}

.share-options {
  margin-top: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.expire-setting {
  display: flex;
  align-items: center;
  gap: 10px;
}
</style>