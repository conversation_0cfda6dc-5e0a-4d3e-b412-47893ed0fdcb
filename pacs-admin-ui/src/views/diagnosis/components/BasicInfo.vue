<template>
  <div class="basic-info">
    <div class="info-container">
      <!-- 患者基本信息卡片 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><User /></el-icon>
            <span class="header-title">患者信息</span>
          </div>
        </template>
        
        <div class="info-grid">
          <div class="info-row">
            <div class="info-item">
              <label class="info-label">姓名</label>
              <span class="info-value">{{ patientData.patientName || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">性别</label>
              <span class="info-value">{{ formatGender(patientData.patientSex) }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">年龄</label>
              <span class="info-value">{{ calculateAge(patientData.patientBirthday) }}岁</span>
            </div>
            <div class="info-item">
              <label class="info-label">出生日期</label>
              <span class="info-value">{{ formatDate(patientData.patientBirthday) }}</span>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-item">
              <label class="info-label">患者ID</label>
              <span class="info-value">{{ patientData.originalPatientId || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">住院号</label>
              <span class="info-value">{{ patientData.inPatientId || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">床号</label>
              <span class="info-value">{{ patientData.bedNo || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">联系电话</label>
              <span class="info-value">{{ formatPhone(patientData.mobile) }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 检查信息卡片 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><Document /></el-icon>
            <span class="header-title">检查信息</span>
          </div>
        </template>
        
        <div class="info-grid">
          <div class="info-row">
            <div class="info-item">
              <label class="info-label">检查编号</label>
              <span class="info-value">{{ patientData.examCode || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">检查类型</label>
              <el-tag 
                :type="getModalityTagType(patientData.modality)" 
                size="small"
                class="modality-tag"
              >
                {{ patientData.modality || '-' }}
              </el-tag>
            </div>
            <div class="info-item">
              <label class="info-label">检查部位</label>
              <span class="info-value">{{ patientData.organ || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">检查项目</label>
              <span class="info-value">{{ patientData.examItem || '-' }}</span>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-item">
              <label class="info-label">检查时间</label>
              <span class="info-value">{{ formatDateTime(patientData.checkFinishTime) }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">申请科室</label>
              <span class="info-value">{{ patientData.examDepartment || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">送检医生</label>
              <span class="info-value">{{ patientData.examDoctorName || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">检查设备</label>
              <span class="info-value">{{ patientData.deviceName || '-' }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 医院信息卡片 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><OfficeBuilding /></el-icon>
            <span class="header-title">医院信息</span>
          </div>
        </template>
        
        <div class="info-grid">
          <div class="info-row">
            <div class="info-item full-width">
              <label class="info-label">医院名称</label>
              <span class="info-value">{{ patientData.hospitalName || '-' }}</span>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-item">
              <label class="info-label">医院ID</label>
              <span class="info-value">{{ patientData.hospitalId || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">科室</label>
              <span class="info-value">{{ patientData.examDepartment || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">病区</label>
              <span class="info-value">{{ patientData.wardName || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">病房</label>
              <span class="info-value">{{ patientData.roomNo || '-' }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 状态信息卡片 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><InfoFilled /></el-icon>
            <span class="header-title">状态信息</span>
          </div>
        </template>
        
        <div class="info-grid">
          <div class="info-row">
            <div class="info-item">
              <label class="info-label">DICOM状态</label>
              <el-tag 
                :type="getDicomStatusType(patientData)" 
                size="small"
              >
                {{ getDicomStatusText(patientData) }}
              </el-tag>
            </div>
            <div class="info-item">
              <label class="info-label">诊断状态</label>
              <el-tag 
                :type="getDiagnosisStatusType(patientData)" 
                size="small"
              >
                {{ getDiagnosisStatusText(patientData) }}
              </el-tag>
            </div>
            <div class="info-item">
              <label class="info-label">创建时间</label>
              <span class="info-value">{{ formatDateTime(patientData.createTime) }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">更新时间</label>
              <span class="info-value">{{ formatDateTime(patientData.updateTime) }}</span>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { User, Document, OfficeBuilding, InfoFilled } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  patientData: {
    type: Object,
    required: true
  }
})

// 格式化性别
const formatGender = (sex) => {
  if (sex === 'Male' || sex === 'M') return '男'
  if (sex === 'Female' || sex === 'F') return '女'
  return '未知'
}

// 计算年龄
const calculateAge = (birthDate) => {
  if (!birthDate) return '0'
  
  try {
    const birth = new Date(birthDate)
    const today = new Date()
    let age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--
    }
    
    return age.toString()
  } catch (error) {
    return '0'
  }
}

// 格式化日期
const formatDate = (dateTime) => {
  if (!dateTime) return '-'
  try {
    const date = new Date(dateTime)
    return date.toLocaleDateString('zh-CN')
  } catch (error) {
    return '-'
  }
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  try {
    const date = new Date(dateTime)
    return date.toLocaleString('zh-CN')
  } catch (error) {
    return '-'
  }
}

// 格式化电话号码
const formatPhone = (phone) => {
  if (!phone) return '-'
  // 简单的电话号码格式化
  if (phone.length === 11) {
    return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3')
  }
  return phone
}

// 获取检查类型标签类型
const getModalityTagType = (modality) => {
  const typeMap = {
    'CT': 'primary',
    'MRI': 'success',
    'DR': 'warning',
    'US': 'info',
    'CR': 'info',
    'DX': 'info',
    'MR': 'success',
    'MG': 'warning'
  }
  return typeMap[modality] || 'info'
}

// 获取DICOM状态类型
const getDicomStatusType = (data) => {
  if (data.dicomSyncFlag === 1) return 'success'
  return 'warning'
}

// 获取DICOM状态文本
const getDicomStatusText = (data) => {
  if (data.dicomSyncFlag === 1) return '已同步'
  return '待同步'
}

// 获取诊断状态类型
const getDiagnosisStatusType = (data) => {
  const status = data.diagnosis?.status || '-1'
  const typeMap = {
    '-1': 'info',     // 待诊断
    '0': 'warning',   // 草稿
    '1': 'primary',   // 已诊断
    '2': 'success',   // 已审核
    '9': 'success'    // 院内诊断
  }
  return typeMap[status] || 'info'
}

// 获取诊断状态文本
const getDiagnosisStatusText = (data) => {
  const status = data.diagnosis?.status || '-1'
  const textMap = {
    '-1': '待诊断',
    '0': '草稿',
    '1': '已诊断',
    '2': '已审核',
    '9': '院内诊断'
  }
  return textMap[status] || '待诊断'
}
</script>

<style scoped>
.basic-info {
  height: 100%;
  overflow-y: auto;
  padding-bottom: 20px;
  min-height: 0;
  box-sizing: border-box;
}

.info-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-height: min-content;
}

.info-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.info-card :deep(.el-card__header) {
  padding: 16px 20px;
  background: #fafbfc;
  border-bottom: 1px solid #e4e7ed;
}

.info-card :deep(.el-card__body) {
  padding: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  color: #409eff;
  font-size: 16px;
}

.header-title {
  font-weight: 600;
  color: #303133;
  font-size: 16px;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
  margin-bottom: 4px;
}

.info-value {
  font-size: 14px;
  color: #303133;
  font-weight: 400;
  word-break: break-all;
}

.modality-tag {
  align-self: flex-start;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .info-container {
    gap: 16px;
  }
  
  .info-card :deep(.el-card__header) {
    padding: 12px 16px;
  }
  
  .info-card :deep(.el-card__body) {
    padding: 16px;
  }
  
  .info-row {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
  }
  
  .info-grid {
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .info-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .header-title {
    font-size: 14px;
  }
  
  .info-value {
    font-size: 13px;
  }
}
</style>
