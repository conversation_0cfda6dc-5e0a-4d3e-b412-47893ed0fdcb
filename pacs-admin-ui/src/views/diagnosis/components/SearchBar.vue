<template>
  <div class="search-bar">
    <!-- 单行搜索栏 -->
    <div class="search-row">
      <!-- 左侧搜索条件 -->
      <div class="search-conditions">
        <!-- 检查工作台 标签 -->
        <div class="workspace-label">
          <el-icon class="label-icon"><Monitor /></el-icon>
          <span>工作台</span>
        </div>

        <!-- 搜索条件按钮组 -->
        <div class="condition-buttons">
          <el-button 
            :type="activeCondition === 'name' ? 'primary' : ''" 
            size="small"
            @click="setActiveCondition('name')"
            class="condition-btn"
          >
            姓名
          </el-button>
          <el-button 
            :type="activeCondition === 'id' ? 'primary' : ''" 
            size="small"
            @click="setActiveCondition('id')"
            class="condition-btn"
          >
            ID号
          </el-button>
          <el-button 
            :type="activeCondition === 'exam' ? 'primary' : ''" 
            size="small"
            @click="setActiveCondition('exam')"
            class="condition-btn"
          >
            检查号
          </el-button>
          <el-button 
            :type="activeCondition === 'modality' ? 'primary' : ''" 
            size="small"
            @click="setActiveCondition('modality')"
            class="condition-btn"
          >
            检查类型
          </el-button>
          <el-button 
            :type="activeCondition === 'status' ? 'primary' : ''" 
            size="small"
            @click="setActiveCondition('status')"
            class="condition-btn"
          >
            诊断状态
          </el-button>
          <el-button 
            size="small"
            @click="showAdvancedDialog = true"
            class="condition-btn"
          >
            更多
          </el-button>
        </div>

        <!-- 搜索输入框 -->
        <div class="search-input">
          <!-- 姓名搜索 -->
          <el-input
            v-if="activeCondition === 'name'"
            v-model="localQueryParams.patientName"
            placeholder="请输入患者姓名"
            clearable
            @keyup.enter="handleSearch"
            @clear="handleSearch"
            size="small"
            style="width: 200px"
          />
          <!-- ID搜索 -->
          <el-input
            v-else-if="activeCondition === 'id'"
            v-model="localQueryParams.patientId"
            placeholder="请输入患者ID"
            clearable
            @keyup.enter="handleSearch"
            @clear="handleSearch"
            size="small"
            style="width: 200px"
          />
          <!-- 检查号搜索 -->
          <el-input
            v-else-if="activeCondition === 'exam'"
            v-model="localQueryParams.examCode"
            placeholder="请输入检查编号"
            clearable
            @keyup.enter="handleSearch"
            @clear="handleSearch"
            size="small"
            style="width: 200px"
          />
          <!-- 检查类型搜索 -->
          <el-select
            v-else-if="activeCondition === 'modality'"
            v-model="localQueryParams.modality"
            placeholder="请选择检查类型"
            clearable
            @change="handleSearch"
            @clear="handleSearch"
            size="small"
            style="width: 200px"
          >
            <el-option label="CT" value="CT" />
            <el-option label="MRI" value="MRI" />
            <el-option label="DR" value="DR" />
            <el-option label="US" value="US" />
            <el-option label="其他" value="OTHER" />
          </el-select>
          <!-- 诊断状态搜索 -->
          <el-select
            v-else-if="activeCondition === 'status'"
            v-model="localQueryParams.diagnosisStatus"
            placeholder="请选择诊断状态"
            @change="handleStatusChange"
            size="small"
            style="width: 200px"
          >
            <el-option label="全部" value="" />
            <el-option label="待诊断" value="-1" />
            <el-option label="已诊断" value="1" />
            <el-option label="已审核" value="2" />
            <el-option label="院内诊断" value="9" />
          </el-select>
        </div>
      </div>

      <!-- 右侧日期和操作按钮 -->
      <div class="right-actions">
        <!-- 日期范围选择 -->
        <div class="date-range">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="—"
            start-placeholder="2024-06-25"
            end-placeholder="2024-06-25"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateRangeChange"
            size="small"
            style="width: 240px"
          />
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button type="success" size="small" @click="handleTemplateManage" plain>
            <el-icon><Document /></el-icon>
            模板
          </el-button>
          <el-button type="primary" size="small" @click="handleSearch">
            查询
          </el-button>
          <el-button size="small" @click="handleReset">
            重置
          </el-button>
          <el-button size="small" @click="showAdvancedDialog = true">
            <el-icon><Setting /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 高级搜索对话框 -->
    <el-dialog
      v-model="showAdvancedDialog"
      title="高级搜索 - 组合条件查询"
      width="800px"
      :before-close="handleAdvancedClose"
    >
      <el-form :model="localQueryParams" label-width="100px">
        <!-- 基础搜索条件 -->
        <el-divider content-position="left">基础信息</el-divider>
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="患者姓名">
              <el-input
                v-model="localQueryParams.patientName"
                placeholder="请输入患者姓名"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="患者ID">
              <el-input
                v-model="localQueryParams.patientId"
                placeholder="请输入患者ID"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="检查编号">
              <el-input
                v-model="localQueryParams.examCode"
                placeholder="请输入检查编号"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 检查信息 -->
        <el-divider content-position="left">检查信息</el-divider>
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="检查类型">
              <el-select
                v-model="localQueryParams.modality"
                placeholder="请选择检查类型"
                clearable
                style="width: 100%"
              >
                <el-option label="CT" value="CT" />
                <el-option label="MRI" value="MRI" />
                <el-option label="DR" value="DR" />
                <el-option label="US" value="US" />
                <el-option label="其他" value="OTHER" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="检查部位">
              <el-input
                v-model="localQueryParams.organ"
                placeholder="请输入检查部位"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="诊断状态">
              <el-select
                v-model="localQueryParams.diagnosisStatus"
                placeholder="请选择诊断状态"
                clearable
                style="width: 100%"
              >
                <el-option label="全部" value="" />
                <el-option label="待诊断" value="-1" />
                <el-option label="已诊断" value="1" />
                <el-option label="已审核" value="2" />
                <el-option label="院内诊断" value="9" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 医院信息 -->
        <el-divider content-position="left">医院信息</el-divider>
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="医院名称">
              <el-input
                v-model="localQueryParams.hospitalName"
                placeholder="请输入医院名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请科室">
              <el-input
                v-model="localQueryParams.examDepartment"
                placeholder="请输入申请科室"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请医生">
              <el-input
                v-model="localQueryParams.examDoctorName"
                placeholder="请输入申请医生"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 时间范围 -->
        <el-divider content-position="left">时间范围</el-divider>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="检查时间">
              <el-date-picker
                v-model="advancedDateRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="快速选择">
              <el-button-group>
                <el-button size="small" @click="setQuickDateRange('today')">今天</el-button>
                <el-button size="small" @click="setQuickDateRange('week')">本周</el-button>
                <el-button size="small" @click="setQuickDateRange('month')">本月</el-button>
                <el-button size="small" @click="setQuickDateRange('quarter')">本季度</el-button>
              </el-button-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleAdvancedReset">
            <el-icon><Refresh /></el-icon>
            重置所有条件
          </el-button>
          <el-button @click="showAdvancedDialog = false">取消</el-button>
          <el-button type="primary" @click="handleAdvancedSearch">
            <el-icon><Search /></el-icon>
            开始搜索
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { Monitor, Setting, Search, Refresh, Document } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  queryParams: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['search', 'reset', 'export', 'refresh', 'update:queryParams', 'template-manage'])

// 响应式数据
const showAdvancedDialog = ref(false)
const activeCondition = ref('name') // 当前激活的搜索条件
const dateRange = ref([])
const advancedDateRange = ref([])

// 本地查询参数（用于双向绑定）
const localQueryParams = reactive({ ...props.queryParams })

// 监听props变化，同步到本地参数
watch(() => props.queryParams, (newVal) => {
  Object.assign(localQueryParams, newVal)
}, { deep: true })

// 监听本地参数变化，同步到父组件
watch(localQueryParams, (newVal) => {
  emit('update:queryParams', newVal)
}, { deep: true })

// 设置激活的搜索条件 - 参考study-list.vue的逻辑
const setActiveCondition = (condition) => {
  // 清空其他条件，使用undefined而不是空字符串
  if (condition !== 'name') localQueryParams.patientName = undefined
  if (condition !== 'id') localQueryParams.patientId = undefined
  if (condition !== 'exam') localQueryParams.examCode = undefined
  if (condition !== 'modality') localQueryParams.modality = undefined

  // 如果切换到状态搜索，重置状态值为空（全部）
  if (condition === 'status') {
    localQueryParams.diagnosisStatus = ''
  }

  activeCondition.value = condition

  // 切换条件后不自动触发搜索，让用户选择具体的搜索值
  // setTimeout(() => {
  //   handleSearch()
  // }, 100)
}

// 处理日期范围变化
const handleDateRangeChange = (value) => {
  if (value && value.length === 2) {
    localQueryParams.startTime = value[0]
    localQueryParams.endTime = value[1]
  } else {
    localQueryParams.startTime = undefined
    localQueryParams.endTime = undefined
  }
}

// 处理搜索
const handleSearch = () => {
  emit('search')
}

// 处理状态变化
const handleStatusChange = (value) => {
  handleSearch()
}

// 处理重置 - 参考study-list.vue的逻辑
const handleReset = () => {
  // 重置所有搜索条件为undefined
  Object.keys(localQueryParams).forEach(key => {
    if (key !== 'pageNum' && key !== 'pageSize') {
      localQueryParams[key] = undefined
    }
  })

  dateRange.value = []
  advancedDateRange.value = []
  activeCondition.value = 'name'
  emit('reset')
}

// 处理导出
const handleExport = () => {
  emit('export')
}

// 处理刷新
const handleRefresh = () => {
  emit('refresh')
}

// 模板管理
const handleTemplateManage = () => {
  emit('template-manage')
}

// 高级搜索相关方法
const handleAdvancedSearch = () => {
  // 处理高级日期范围
  if (advancedDateRange.value && advancedDateRange.value.length === 2) {
    localQueryParams.startTime = advancedDateRange.value[0]
    localQueryParams.endTime = advancedDateRange.value[1]
  }
  
  showAdvancedDialog.value = false
  handleSearch()
}

const handleAdvancedReset = () => {
  localQueryParams.hospitalName = undefined
  localQueryParams.organ = undefined
  localQueryParams.examDepartment = undefined
  localQueryParams.examDoctorName = undefined
  advancedDateRange.value = []
  localQueryParams.startTime = undefined
  localQueryParams.endTime = undefined
}

const handleAdvancedClose = () => {
  showAdvancedDialog.value = false
}

// 快速日期选择
const setQuickDateRange = (type) => {
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

  switch (type) {
    case 'today':
      advancedDateRange.value = [
        today.toISOString().slice(0, 19).replace('T', ' '),
        new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1000).toISOString().slice(0, 19).replace('T', ' ')
      ]
      break
    case 'week':
      const weekStart = new Date(today.getTime() - today.getDay() * 24 * 60 * 60 * 1000)
      const weekEnd = new Date(weekStart.getTime() + 7 * 24 * 60 * 60 * 1000 - 1000)
      advancedDateRange.value = [
        weekStart.toISOString().slice(0, 19).replace('T', ' '),
        weekEnd.toISOString().slice(0, 19).replace('T', ' ')
      ]
      break
    case 'month':
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)
      const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59)
      advancedDateRange.value = [
        monthStart.toISOString().slice(0, 19).replace('T', ' '),
        monthEnd.toISOString().slice(0, 19).replace('T', ' ')
      ]
      break
    case 'quarter':
      const quarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1)
      const quarterEnd = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3 + 3, 0, 23, 59, 59)
      advancedDateRange.value = [
        quarterStart.toISOString().slice(0, 19).replace('T', ' '),
        quarterEnd.toISOString().slice(0, 19).replace('T', ' ')
      ]
      break
  }
}
</script>

<style scoped>
.search-bar {
  background: white;
  padding: 12px 20px;
  border-bottom: 1px solid #e4e7ed;
}

.search-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  height: 32px;
}

.search-conditions {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.workspace-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  color: #303133;
  font-size: 14px;
  white-space: nowrap;
}

.label-icon {
  color: #409eff;
  font-size: 16px;
}

.condition-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.condition-btn {
  height: 28px;
  padding: 0 12px;
  font-size: 12px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background: white;
  color: #606266;
  transition: all 0.3s ease;
}

.condition-btn:hover {
  border-color: #409eff;
  color: #409eff;
}

.condition-btn.el-button--primary {
  background: #409eff;
  border-color: #409eff;
  color: white;
}

.search-input {
  display: flex;
  align-items: center;
}

.right-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.date-range {
  display: flex;
  align-items: center;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-buttons .el-button {
  height: 28px;
  padding: 0 12px;
  font-size: 12px;
}

/* 高级搜索对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* 高级搜索表单样式 */
:deep(.el-dialog__body) {
  padding: 20px 20px 10px 20px;
}

:deep(.el-divider) {
  margin: 16px 0;
}

:deep(.el-divider__text) {
  font-weight: 600;
  color: #409eff;
}

:deep(.el-button-group .el-button) {
  padding: 5px 10px;
  font-size: 12px;
}

/* 美化样式 */
.search-input :deep(.el-input__wrapper) {
  border-radius: 4px;
  height: 28px;
}

.search-input :deep(.el-select .el-input__wrapper) {
  border-radius: 4px;
  height: 28px;
}

.date-range :deep(.el-date-editor) {
  height: 28px;
}

.date-range :deep(.el-date-editor .el-input__wrapper) {
  border-radius: 4px;
}

.action-buttons :deep(.el-button--primary) {
  background: #409eff;
  border-color: #409eff;
}

.action-buttons :deep(.el-button--primary:hover) {
  background: #337ecc;
  border-color: #337ecc;
}

/* 确保输入框和按钮高度一致 */
.search-input :deep(.el-input),
.search-input :deep(.el-select),
.date-range :deep(.el-date-editor),
.action-buttons :deep(.el-button) {
  height: 28px;
}

.search-input :deep(.el-input__inner),
.search-input :deep(.el-select__wrapper) {
  height: 28px;
  line-height: 28px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .search-row {
    flex-wrap: wrap;
    height: auto;
    gap: 12px;
  }

  .search-conditions {
    flex-wrap: wrap;
    gap: 12px;
  }

  .condition-buttons {
    flex-wrap: wrap;
    gap: 6px;
  }

  .right-actions {
    gap: 8px;
  }
}

@media (max-width: 768px) {
  .search-bar {
    padding: 8px 12px;
  }

  .search-row {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .search-conditions {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .condition-buttons {
    justify-content: flex-start;
  }

  .right-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .date-range {
    width: 100%;
  }

  .date-range :deep(.el-date-editor) {
    width: 100% !important;
  }

  .action-buttons {
    justify-content: center;
  }
}

/* 组件特定样式 - 移除重复的按钮修复 */
</style>
