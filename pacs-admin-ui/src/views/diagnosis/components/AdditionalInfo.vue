<template>
  <div class="additional-info">
    <div class="info-container">
      <!-- 临床信息卡片 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><Document /></el-icon>
            <span class="header-title">临床信息</span>
          </div>
        </template>
        
        <div class="info-grid">
          <div class="info-row">
            <div class="info-item full-width">
              <label class="info-label">临床症状</label>
              <div class="info-value text-content">
                {{ patientData.clinicalSymptoms || '无' }}
              </div>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-item full-width">
              <label class="info-label">临床诊断</label>
              <div class="info-value text-content">
                {{ patientData.clinicalDiagnosis || '无' }}
              </div>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-item full-width">
              <label class="info-label">检查目的</label>
              <div class="info-value text-content">
                {{ patientData.examPurpose || '无' }}
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 申请信息卡片 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><EditPen /></el-icon>
            <span class="header-title">申请信息</span>
          </div>
        </template>
        
        <div class="info-grid">
          <div class="info-row">
            <div class="info-item">
              <label class="info-label">申请医生</label>
              <span class="info-value">{{ patientData.examDoctorName || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">申请科室</label>
              <span class="info-value">{{ patientData.examDepartment || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">申请时间</label>
              <span class="info-value">{{ formatDateTime(patientData.examApplyTime) }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">申请单号</label>
              <span class="info-value">{{ patientData.examApplyNo || '-' }}</span>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-item">
              <label class="info-label">检查技师</label>
              <span class="info-value">{{ patientData.examTechnician || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">检查设备</label>
              <span class="info-value">{{ patientData.deviceName || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">检查序列</label>
              <span class="info-value">{{ patientData.examSequence || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">检查协议</label>
              <span class="info-value">{{ patientData.examProtocol || '-' }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 检查详情卡片 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><Monitor /></el-icon>
            <span class="header-title">检查详情</span>
          </div>
        </template>
        
        <div class="info-grid">
          <div class="info-row">
            <div class="info-item">
              <label class="info-label">检查开始时间</label>
              <span class="info-value">{{ formatDateTime(patientData.checkStartTime) }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">检查结束时间</label>
              <span class="info-value">{{ formatDateTime(patientData.checkFinishTime) }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">检查时长</label>
              <span class="info-value">{{ calculateDuration(patientData.checkStartTime, patientData.checkFinishTime) }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">图像数量</label>
              <span class="info-value">{{ patientData.imageCount || '0' }} 张</span>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-item">
              <label class="info-label">序列数量</label>
              <span class="info-value">{{ patientData.seriesCount || '0' }} 个</span>
            </div>
            <div class="info-item">
              <label class="info-label">数据大小</label>
              <span class="info-value">{{ formatFileSize(patientData.dataSize) }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">压缩格式</label>
              <span class="info-value">{{ patientData.compressionType || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">传输状态</label>
              <el-tag 
                :type="getTransferStatusType(patientData.transferStatus)" 
                size="small"
              >
                {{ getTransferStatusText(patientData.transferStatus) }}
              </el-tag>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 备注信息卡片 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><ChatDotRound /></el-icon>
            <span class="header-title">备注信息</span>
          </div>
        </template>
        
        <div class="info-grid">
          <div class="info-row">
            <div class="info-item full-width">
              <label class="info-label">检查备注</label>
              <div class="info-value text-content">
                {{ patientData.examRemark || '无' }}
              </div>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-item full-width">
              <label class="info-label">技师备注</label>
              <div class="info-value text-content">
                {{ patientData.technicianRemark || '无' }}
              </div>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-item full-width">
              <label class="info-label">系统备注</label>
              <div class="info-value text-content">
                {{ patientData.systemRemark || '无' }}
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { Document, EditPen, Monitor, ChatDotRound } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  patientData: {
    type: Object,
    required: true
  }
})

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  try {
    const date = new Date(dateTime)
    return date.toLocaleString('zh-CN')
  } catch (error) {
    return '-'
  }
}

// 计算检查时长
const calculateDuration = (startTime, endTime) => {
  if (!startTime || !endTime) return '-'
  
  try {
    const start = new Date(startTime)
    const end = new Date(endTime)
    const duration = end - start
    
    if (duration <= 0) return '-'
    
    const minutes = Math.floor(duration / (1000 * 60))
    const seconds = Math.floor((duration % (1000 * 60)) / 1000)
    
    if (minutes > 0) {
      return `${minutes}分${seconds}秒`
    } else {
      return `${seconds}秒`
    }
  } catch (error) {
    return '-'
  }
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '-'
  
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

// 获取传输状态类型
const getTransferStatusType = (status) => {
  const typeMap = {
    'completed': 'success',
    'transferring': 'primary',
    'failed': 'danger',
    'pending': 'warning'
  }
  return typeMap[status] || 'info'
}

// 获取传输状态文本
const getTransferStatusText = (status) => {
  const textMap = {
    'completed': '已完成',
    'transferring': '传输中',
    'failed': '传输失败',
    'pending': '等待传输'
  }
  return textMap[status] || '未知'
}
</script>

<style scoped>
.additional-info {
  height: 100%;
  overflow-y: auto;
  padding-bottom: 20px;
  min-height: 0;
  box-sizing: border-box;
}

.info-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-height: min-content;
}

.info-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.info-card :deep(.el-card__header) {
  padding: 16px 20px;
  background: #fafbfc;
  border-bottom: 1px solid #e4e7ed;
}

.info-card :deep(.el-card__body) {
  padding: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  color: #409eff;
  font-size: 16px;
}

.header-title {
  font-weight: 600;
  color: #303133;
  font-size: 16px;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
  margin-bottom: 4px;
}

.info-value {
  font-size: 14px;
  color: #303133;
  font-weight: 400;
  word-break: break-all;
}

.text-content {
  background: #f8f9fa;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  min-height: 40px;
  line-height: 1.5;
  white-space: pre-wrap;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .info-container {
    gap: 16px;
  }
  
  .info-card :deep(.el-card__header) {
    padding: 12px 16px;
  }
  
  .info-card :deep(.el-card__body) {
    padding: 16px;
  }
  
  .info-row {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
  }
  
  .info-grid {
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .info-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .header-title {
    font-size: 14px;
  }
  
  .info-value {
    font-size: 13px;
  }
}
</style>
