<template>
  <div class="patient-list">
    <!-- 列表头部 -->
    <div class="list-header">
      <div class="header-title">
        <el-icon><User /></el-icon>
        <span>患者列表</span>
        <el-badge :value="total" :max="999" class="total-badge" />
      </div>
      <div class="header-actions">
        <el-tooltip content="刷新列表" placement="top">
          <el-button 
            type="text" 
            :icon="Refresh" 
            @click="$emit('refresh')"
            :loading="loading"
            class="refresh-btn"
          />
        </el-tooltip>
      </div>
    </div>

    <!-- 患者表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="data"
        :row-class-name="getRowClassName"
        @row-click="handleRowClick"
        height="100%"
        stripe
        highlight-current-row
        class="patient-table"
      >
        <!-- 患者基本信息列 -->
        <el-table-column label="患者信息" min-width="180">
          <template #default="scope">
            <div class="patient-info">
<!--              <div class="patient-avatar">
                <el-avatar :size="36" class="avatar">
                  <el-icon><User /></el-icon>
                </el-avatar>
              </div>-->
              <div class="patient-details">
                <div class="patient-name-row">
                  <span class="patient-name">{{ scope.row.patientName }}</span>
                  <el-tag
                    :type="getDiagnosisStatusType(scope.row)"
                    size="small"
                    class="diagnosis-status-tag"
                  >
                    {{ getDiagnosisStatusText(scope.row) }}
                  </el-tag>
                </div>
                <div class="patient-meta">
                  <span class="gender">{{ formatGender(scope.row.patientSex) }}</span>
                  <span class="age">{{ calculateAge(scope.row.patientBirthday) }}岁</span>
                </div>
                <div class="patient-id-row">
                  <span class="patient-id">ID: {{ scope.row.originalPatientId }}</span>
                  <el-tag
                    :type="getDicomStatusType(scope.row)"
                    size="small"
                    class="dicom-status-tag"
                  >
                    {{ getDicomStatusText(scope.row) }}
                  </el-tag>
                </div>
                <div class="patient-id-row">
                  {{ scope.row.checkFinishTime }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 检查信息列 -->
        <el-table-column label="检查信息" min-width="180">
          <template #default="scope">
            <div class="exam-info">
              <div class="exam-code">
               {{ scope.row.examCode }} <el-tag
                  :type="getModalityTagType(scope.row.modality)"
                  size="small"
                  class="modality-tag"
              >
                {{ scope.row.modality }}
              </el-tag></div>
              <div class="exam-part">  {{ scope.row.organ || '未指定' }}</div>
            </div>
          </template>
        </el-table-column>

        <!-- 检查时间列 -->
        <el-table-column label="检查时间" width="140">
          <template #default="scope">
            <div class="time-info">
              <div class="exam-date">{{ formatDate(scope.row.checkFinishTime) }}</div>
              <div class="exam-time">{{ formatTime(scope.row.checkFinishTime) }}</div>
            </div>
          </template>
        </el-table-column>

        <!-- 医院信息列 -->
        <el-table-column label="医院" width="120" show-overflow-tooltip>
          <template #default="scope">
            <div class="hospital-info">
              {{ scope.row.hospitalName }}
            </div>
          </template>
        </el-table-column>



        <!-- 操作列 -->
        <el-table-column label="操作" width="80" align="center">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click.stop="handleDiagnose(scope.row)"
              class="diagnose-btn"
            >
              {{ getDiagnoseButtonText(scope.row) }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页器 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        small
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { User, Refresh } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  data: {
    type: Array,
    default: () => []
  },
  total: {
    type: Number,
    default: 0
  },
  currentPatient: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['patient-select', 'page-change', 'size-change', 'refresh'])

// 响应式数据
const currentPage = ref(1)
const pageSize = ref(20)

// 计算属性
const selectedPatientId = computed(() => {
  return props.currentPatient?.id
})

// 格式化性别
const formatGender = (sex) => {
  if (sex === 'Male' || sex === 'M') return '男'
  if (sex === 'Female' || sex === 'F') return '女'
  return '未知'
}

// 计算年龄
const calculateAge = (birthDate) => {
  if (!birthDate) return '0'
  
  try {
    const birth = new Date(birthDate)
    const today = new Date()
    let age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--
    }
    
    return age.toString()
  } catch (error) {
    return '0'
  }
}

// 格式化日期
const formatDate = (dateTime) => {
  if (!dateTime) return ''
  try {
    const date = new Date(dateTime)
    return date.toLocaleDateString('zh-CN')
  } catch (error) {
    return ''
  }
}

// 格式化时间
const formatTime = (dateTime) => {
  if (!dateTime) return ''
  try {
    const date = new Date(dateTime)
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } catch (error) {
    return ''
  }
}

// 获取检查类型标签类型
const getModalityTagType = (modality) => {
  const typeMap = {
    'CT': 'primary',
    'MRI': 'success',
    'DR': 'warning',
    'US': 'info'
  }
  return typeMap[modality] || 'default'
}

// 获取DICOM状态类型
const getDicomStatusType = (row) => {
  if (row.dicomSyncFlag === 1) return 'success'
  return 'warning'
}

// 获取DICOM状态文本
const getDicomStatusText = (row) => {
  if (row.dicomSyncFlag === 1) return '已同步'
  return '待同步'
}

// 获取诊断状态类型 - 只保留系统实际使用的4个状态
const getDiagnosisStatusType = (row) => {
  // 优先使用 diagnosisStatus 字段，如果没有则使用 diagnosis.status
  const status = row.diagnosisStatus || row.diagnosis?.status || '-1'
  const typeMap = {
    '-1': 'info',     // 待诊断
    '1': 'primary',   // 已诊断
    '2': 'success',   // 已审核
    '9': 'success'    // 院内诊断
  }
  return typeMap[status] || 'info'
}

// 获取诊断状态文本 - 只保留系统实际使用的4个状态
const getDiagnosisStatusText = (row) => {
  // 优先使用 diagnosisStatus 字段，如果没有则使用 diagnosis.status
  const status = row.diagnosisStatus || row.diagnosis?.status || '-1'
  const textMap = {
    '-1': '待诊断',
    '1': '已诊断',
    '2': '已审核',
    '9': '院内诊断'
  }
  return textMap[status] || '待诊断'
}

// 获取诊断按钮文本 - 只保留系统实际使用的4个状态
const getDiagnoseButtonText = (row) => {
  // 优先使用 diagnosisStatus 字段，如果没有则使用 diagnosis.status
  const status = row.diagnosisStatus || row.diagnosis?.status || '-1'
  const buttonTextMap = {
    '-1': '诊断',   // 待诊断
    '1': '查看',    // 已诊断
    '2': '查看',    // 已审核
    '9': '查看'     // 院内诊断
  }
  return buttonTextMap[status] || '诊断'
}

// 获取行类名
const getRowClassName = ({ row }) => {
  if (selectedPatientId.value === row.id) {
    return 'selected-row'
  }
  return ''
}

// 处理行点击
const handleRowClick = (row) => {
  emit('patient-select', row)
}

// 处理诊断按钮点击
const handleDiagnose = (row) => {
  emit('patient-select', row)
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  emit('page-change', page)
}

// 处理页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  emit('size-change', size)
}
</script>

<style scoped>
.patient-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafbfc;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.total-badge {
  margin-left: 8px;
}

.refresh-btn {
  padding: 8px;
  border-radius: 6px;
}

.table-container {
  flex: 1;
  overflow: hidden;
}

.patient-table {
  height: 100%;
}

.patient-table :deep(.el-table__row) {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.patient-table :deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

.patient-table :deep(.selected-row) {
  background-color: #e6f7ff !important;
}

.patient-table :deep(.selected-row:hover) {
  background-color: #d6f3ff !important;
}

.patient-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.avatar {
  background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
}

.patient-details {
  flex: 1;
  min-width: 0;
}

.patient-name-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.patient-name {
  font-weight: 600;
  color: #303133;
  flex: 1;
}

.diagnosis-status-tag {
  font-size: 10px;
  height: 18px;
  line-height: 16px;
  padding: 0 6px;
  border-radius: 2px;
}

.patient-meta {
  display: flex;
  gap: 8px;
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
}

.patient-id-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.patient-id {
  font-size: 11px;
  color: #c0c4cc;
  flex: 1;
}

.dicom-status-tag {
  font-size: 10px;
  height: 16px;
  line-height: 14px;
  padding: 0 5px;
  border-radius: 2px;
}

.exam-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.exam-code {
  font-weight: 500;
  color: #303133;
}

.modality-tag {
  align-self: flex-end;
}

.exam-part {
  font-size: 12px;
  color: #909399;
}

.time-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.exam-date {
  font-weight: 500;
  color: #303133;
}

.exam-time {
  font-size: 12px;
  color: #909399;
}

.hospital-info {
  font-size: 12px;
  color: #606266;
}



.diagnose-btn {
  padding: 4px 12px;
  font-size: 12px;
  border-radius: 4px;
}

/* 修复按钮样式 - 临时解决方案 */
.patient-table :deep(.el-button--primary) {
  color: #ffffff !important;
  background-color: #0066cc !important;
  border-color: #0066cc !important;
}

.patient-table :deep(.el-button--primary:hover),
.patient-table :deep(.el-button--primary:focus) {
  color: #ffffff !important;
  background-color: #4d94ff !important;
  border-color: #4d94ff !important;
}

.patient-table :deep(.el-button--primary:active) {
  color: #ffffff !important;
  background-color: #004499 !important;
  border-color: #004499 !important;
}

.pagination-container {
  padding: 16px 20px;
  border-top: 1px solid #e4e7ed;
  background: #fafbfc;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .list-header {
    padding: 12px 16px;
  }
  
  .pagination-container {
    padding: 12px 16px;
  }
  
  .patient-info {
    gap: 8px;
  }
  
  .avatar {
    width: 32px !important;
    height: 32px !important;
  }
}
</style>
