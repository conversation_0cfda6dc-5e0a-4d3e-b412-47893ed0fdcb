<template>
  <div class="cost-details">
    <div class="info-container">
      <!-- 费用汇总卡片 -->
      <el-card class="info-card summary-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><Money /></el-icon>
            <span class="header-title">费用汇总</span>
          </div>
        </template>
        
        <div class="summary-grid">
          <div class="summary-item">
            <div class="summary-label">检查费用</div>
            <div class="summary-value primary">¥{{ formatCurrency(patientData.examFee) }}</div>
          </div>
          <div class="summary-item">
            <div class="summary-label">材料费用</div>
            <div class="summary-value">¥{{ formatCurrency(patientData.materialFee) }}</div>
          </div>
          <div class="summary-item">
            <div class="summary-label">药品费用</div>
            <div class="summary-value">¥{{ formatCurrency(patientData.drugFee) }}</div>
          </div>
          <div class="summary-item">
            <div class="summary-label">总费用</div>
            <div class="summary-value total">¥{{ formatCurrency(calculateTotalFee()) }}</div>
          </div>
        </div>
      </el-card>

      <!-- 费用明细卡片 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><List /></el-icon>
            <span class="header-title">费用明细</span>
          </div>
        </template>
        
        <div class="table-container">
          <el-table 
            :data="costItems" 
            stripe 
            class="cost-table"
            :show-header="true"
          >
            <el-table-column prop="itemName" label="项目名称" min-width="150">
              <template #default="scope">
                <div class="item-name">{{ scope.row.itemName }}</div>
                <div class="item-code">{{ scope.row.itemCode }}</div>
              </template>
            </el-table-column>
            
            <el-table-column prop="itemType" label="项目类型" width="100" align="center">
              <template #default="scope">
                <el-tag 
                  :type="getItemTypeTag(scope.row.itemType)" 
                  size="small"
                >
                  {{ getItemTypeText(scope.row.itemType) }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column prop="quantity" label="数量" width="80" align="center">
              <template #default="scope">
                {{ scope.row.quantity || 1 }}
              </template>
            </el-table-column>
            
            <el-table-column prop="unitPrice" label="单价" width="100" align="right">
              <template #default="scope">
                ¥{{ formatCurrency(scope.row.unitPrice) }}
              </template>
            </el-table-column>
            
            <el-table-column prop="totalPrice" label="小计" width="100" align="right">
              <template #default="scope">
                <span class="total-price">¥{{ formatCurrency(scope.row.totalPrice) }}</span>
              </template>
            </el-table-column>
            
            <el-table-column prop="status" label="状态" width="80" align="center">
              <template #default="scope">
                <el-tag 
                  :type="getStatusTag(scope.row.status)" 
                  size="small"
                >
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 空状态 -->
          <div v-if="!costItems || costItems.length === 0" class="empty-state">
            <el-empty description="暂无费用明细数据" />
          </div>
        </div>
      </el-card>

      <!-- 支付信息卡片 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><CreditCard /></el-icon>
            <span class="header-title">支付信息</span>
          </div>
        </template>
        
        <div class="info-grid">
          <div class="info-row">
            <div class="info-item">
              <label class="info-label">支付方式</label>
              <span class="info-value">{{ getPaymentMethodText(patientData.paymentMethod) }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">支付状态</label>
              <el-tag 
                :type="getPaymentStatusTag(patientData.paymentStatus)" 
                size="small"
              >
                {{ getPaymentStatusText(patientData.paymentStatus) }}
              </el-tag>
            </div>
            <div class="info-item">
              <label class="info-label">支付时间</label>
              <span class="info-value">{{ formatDateTime(patientData.paymentTime) }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">支付单号</label>
              <span class="info-value">{{ patientData.paymentNo || '-' }}</span>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-item">
              <label class="info-label">医保类型</label>
              <span class="info-value">{{ getInsuranceTypeText(patientData.insuranceType) }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">医保报销</label>
              <span class="info-value">¥{{ formatCurrency(patientData.insuranceAmount) }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">自付金额</label>
              <span class="info-value">¥{{ formatCurrency(patientData.selfPayAmount) }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">发票号</label>
              <span class="info-value">{{ patientData.invoiceNo || '-' }}</span>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Money, List, CreditCard } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  patientData: {
    type: Object,
    required: true
  }
})

// 计算费用明细数据
const costItems = computed(() => {
  // 模拟费用明细数据，实际应该从API获取
  const items = []
  
  if (props.patientData.examFee > 0) {
    items.push({
      itemName: `${props.patientData.modality}检查`,
      itemCode: 'EXAM_001',
      itemType: 'exam',
      quantity: 1,
      unitPrice: props.patientData.examFee,
      totalPrice: props.patientData.examFee,
      status: 'completed'
    })
  }
  
  if (props.patientData.materialFee > 0) {
    items.push({
      itemName: '检查材料费',
      itemCode: 'MAT_001',
      itemType: 'material',
      quantity: 1,
      unitPrice: props.patientData.materialFee,
      totalPrice: props.patientData.materialFee,
      status: 'completed'
    })
  }
  
  if (props.patientData.drugFee > 0) {
    items.push({
      itemName: '造影剂费用',
      itemCode: 'DRUG_001',
      itemType: 'drug',
      quantity: 1,
      unitPrice: props.patientData.drugFee,
      totalPrice: props.patientData.drugFee,
      status: 'completed'
    })
  }
  
  return items
})

// 格式化货币
const formatCurrency = (amount) => {
  if (!amount || amount === 0) return '0.00'
  return Number(amount).toFixed(2)
}

// 计算总费用
const calculateTotalFee = () => {
  const examFee = Number(props.patientData.examFee) || 0
  const materialFee = Number(props.patientData.materialFee) || 0
  const drugFee = Number(props.patientData.drugFee) || 0
  return examFee + materialFee + drugFee
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  try {
    const date = new Date(dateTime)
    return date.toLocaleString('zh-CN')
  } catch (error) {
    return '-'
  }
}

// 获取项目类型标签
const getItemTypeTag = (type) => {
  const tagMap = {
    'exam': 'primary',
    'material': 'success',
    'drug': 'warning'
  }
  return tagMap[type] || 'info'
}

// 获取项目类型文本
const getItemTypeText = (type) => {
  const textMap = {
    'exam': '检查',
    'material': '材料',
    'drug': '药品'
  }
  return textMap[type] || '其他'
}

// 获取状态标签
const getStatusTag = (status) => {
  const tagMap = {
    'completed': 'success',
    'pending': 'warning',
    'cancelled': 'danger'
  }
  return tagMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const textMap = {
    'completed': '已完成',
    'pending': '待处理',
    'cancelled': '已取消'
  }
  return textMap[status] || '未知'
}

// 获取支付方式文本
const getPaymentMethodText = (method) => {
  const textMap = {
    'cash': '现金',
    'card': '银行卡',
    'alipay': '支付宝',
    'wechat': '微信支付',
    'insurance': '医保'
  }
  return textMap[method] || '未知'
}

// 获取支付状态标签
const getPaymentStatusTag = (status) => {
  const tagMap = {
    'paid': 'success',
    'unpaid': 'warning',
    'refunded': 'info'
  }
  return tagMap[status] || 'default'
}

// 获取支付状态文本
const getPaymentStatusText = (status) => {
  const textMap = {
    'paid': '已支付',
    'unpaid': '未支付',
    'refunded': '已退款'
  }
  return textMap[status] || '未知'
}

// 获取医保类型文本
const getInsuranceTypeText = (type) => {
  const textMap = {
    'urban': '城镇职工',
    'rural': '新农合',
    'resident': '城镇居民',
    'commercial': '商业保险',
    'none': '自费'
  }
  return textMap[type] || '自费'
}
</script>

<style scoped>
.cost-details {
  height: 100%;
  overflow-y: auto;
  padding-bottom: 20px;
  min-height: 0;
  box-sizing: border-box;
}

.info-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-height: min-content;
}

.info-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.info-card :deep(.el-card__header) {
  padding: 16px 20px;
  background: #fafbfc;
  border-bottom: 1px solid #e4e7ed;
}

.info-card :deep(.el-card__body) {
  padding: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  color: #409eff;
  font-size: 16px;
}

.header-title {
  font-weight: 600;
  color: #303133;
  font-size: 16px;
}

/* 费用汇总样式 */
.summary-card :deep(.el-card__body) {
  padding: 24px 20px;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.summary-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.summary-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.summary-value {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.summary-value.primary {
  color: #409eff;
}

.summary-value.total {
  color: #f56c6c;
  font-size: 24px;
}

/* 表格样式 */
.table-container {
  min-height: 200px;
}

.cost-table {
  width: 100%;
}

.item-name {
  font-weight: 500;
  color: #303133;
}

.item-code {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.total-price {
  font-weight: 600;
  color: #303133;
}

.empty-state {
  padding: 40px 0;
}

/* 信息网格样式 */
.info-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
  margin-bottom: 4px;
}

.info-value {
  font-size: 14px;
  color: #303133;
  font-weight: 400;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .info-container {
    gap: 16px;
  }
  
  .info-card :deep(.el-card__header) {
    padding: 12px 16px;
  }
  
  .info-card :deep(.el-card__body) {
    padding: 16px;
  }
  
  .summary-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .summary-item {
    padding: 12px;
  }
  
  .summary-value {
    font-size: 18px;
  }
  
  .summary-value.total {
    font-size: 20px;
  }
  
  .info-row {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .summary-grid {
    grid-template-columns: 1fr;
  }
  
  .info-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }
}
</style>
