<template>
  <div class="test-workspace">
    <h2>诊断工作台测试页面</h2>
    
    <!-- 测试按钮 -->
    <div class="test-controls">
      <el-button type="primary" @click="loadTestData">加载测试数据</el-button>
      <el-button @click="clearData">清空数据</el-button>
      <el-button type="success" @click="showWorkspace = !showWorkspace">
        {{ showWorkspace ? '隐藏' : '显示' }}工作台
      </el-button>
    </div>

    <!-- 诊断工作台 -->
    <div v-if="showWorkspace" class="workspace-container">
      <DiagnosisWorkspace ref="workspaceRef" />
    </div>

    <!-- 测试信息 -->
    <div class="test-info">
      <el-card>
        <template #header>
          <span>测试信息</span>
        </template>
        <div class="info-grid">
          <div class="info-item">
            <label>组件状态：</label>
            <el-tag :type="showWorkspace ? 'success' : 'info'">
              {{ showWorkspace ? '已显示' : '已隐藏' }}
            </el-tag>
          </div>
          <div class="info-item">
            <label>测试数据：</label>
            <el-tag :type="hasTestData ? 'success' : 'warning'">
              {{ hasTestData ? '已加载' : '未加载' }}
            </el-tag>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import DiagnosisWorkspace from './DiagnosisWorkspace.vue'

// 响应式数据
const showWorkspace = ref(true)
const hasTestData = ref(false)
const workspaceRef = ref()

// 模拟测试数据
const testPatientData = {
  id: 1,
  patientName: '张三',
  patientSex: 'Male',
  patientBirthday: '1980-05-15',
  originalPatientId: 'P001',
  inPatientId: 'IP001',
  bedNo: '101',
  mobile: '13800138000',
  examCode: 'E001',
  modality: 'CT',
  organ: '胸部',
  examItem: 'CT胸部平扫',
  checkFinishTime: new Date().toISOString(),
  examDepartment: '内科',
  examDoctorName: '李医生',
  deviceName: 'CT-001',
  hospitalName: '鄂托克旗人民医院',
  hospitalId: 'H001',
  dicomSyncFlag: 1,
  hasImages: true,
  diagnosis: {
    id: 1,
    status: '-1',
    diagnose: '',
    recommendation: '',
    doctor: '王医生',
    createTime: new Date().toISOString()
  },
  // 附加信息
  clinicalSymptoms: '胸闷、咳嗽',
  clinicalDiagnosis: '疑似肺炎',
  examPurpose: '排除肺部疾病',
  examApplyTime: new Date().toISOString(),
  examApplyNo: 'A001',
  examTechnician: '技师张',
  examSequence: 'SEQ001',
  examProtocol: 'PROTOCOL001',
  checkStartTime: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30分钟前
  imageCount: 120,
  seriesCount: 5,
  dataSize: 1024 * 1024 * 50, // 50MB
  compressionType: 'JPEG',
  transferStatus: 'completed',
  examRemark: '患者配合良好',
  technicianRemark: '图像质量良好',
  systemRemark: '自动同步完成',
  // 费用信息
  examFee: 300.00,
  materialFee: 50.00,
  drugFee: 0.00,
  paymentMethod: 'insurance',
  paymentStatus: 'paid',
  paymentTime: new Date().toISOString(),
  paymentNo: 'PAY001',
  insuranceType: 'urban',
  insuranceAmount: 280.00,
  selfPayAmount: 70.00,
  invoiceNo: 'INV001'
}

// 加载测试数据
const loadTestData = () => {
  try {
    // 模拟选择患者
    if (workspaceRef.value) {
      workspaceRef.value.selectPatient(testPatientData)
    }
    
    hasTestData.value = true
    ElMessage.success('测试数据加载成功')
  } catch (error) {
    console.error('加载测试数据失败:', error)
    ElMessage.error('加载测试数据失败')
  }
}

// 清空数据
const clearData = () => {
  hasTestData.value = false
  ElMessage.info('数据已清空')
}
</script>

<style scoped>
.test-workspace {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.test-workspace h2 {
  margin: 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.test-controls {
  display: flex;
  gap: 12px;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.workspace-container {
  flex: 1;
  border: 2px solid #409eff;
  border-radius: 8px;
  overflow: hidden;
}

.test-info {
  flex-shrink: 0;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .test-workspace {
    padding: 12px;
  }
  
  .test-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .test-controls .el-button {
    width: 100%;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
}
</style>
