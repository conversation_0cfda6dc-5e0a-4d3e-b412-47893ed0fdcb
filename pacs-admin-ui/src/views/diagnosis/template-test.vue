<template>
  <div class="template-test">
    <el-card>
      <template #header>
        <span>模板功能测试</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <h3>模板列表</h3>
          <el-button @click="loadTemplates" type="primary">加载模板</el-button>
          <div v-if="templates.length > 0">
            <div v-for="template in templates" :key="template.id" class="template-item">
              <h4>{{ template.name }}</h4>
              <p><strong>类型:</strong> {{ template.modalityType }}</p>
              <p><strong>部位:</strong> {{ template.bodyPart }}</p>
              <p><strong>公开:</strong> {{ template.isPublic === '1' ? '公共模板' : '私有模板' }}</p>
              <p><strong>所见:</strong> {{ template.findings }}</p>
              <p><strong>意见:</strong> {{ template.opinion }}</p>
              <el-button @click="applyTemplate(template)" size="small" type="primary">应用</el-button>
              <el-button @click="testSaveTemplate" size="small" type="success">测试保存</el-button>
            </div>
          </div>
        </el-col>
        
        <el-col :span="12">
          <h3>诊断表单</h3>
          <el-form :model="diagnosisForm" label-width="80px">
            <el-form-item label="影像所见">
              <el-input v-model="diagnosisForm.diagnose" type="textarea" :rows="4" />
            </el-form-item>
            <el-form-item label="影像意见">
              <el-input v-model="diagnosisForm.recommendation" type="textarea" :rows="3" />
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { listTemplate, addTemplate } from '@/api/diagnosis/template'

const templates = ref([])
const diagnosisForm = reactive({
  diagnose: '',
  recommendation: ''
})

const loadTemplates = async () => {
  try {
    const res = await listTemplate({
      pageNum: 1,
      pageSize: 10
    })
    
    console.log('API响应:', res)
    
    if (res.code === 200) {
      templates.value = res.rows || []
      ElMessage.success(`加载了 ${templates.value.length} 个模板`)
    } else {
      ElMessage.error(res.msg || '加载模板失败')
    }
  } catch (error) {
    console.error('加载模板出错:', error)
    ElMessage.error('系统错误')
  }
}

const applyTemplate = (template) => {
  console.log('应用模板:', template)

  if (template.findings) {
    diagnosisForm.diagnose = template.findings
  }

  if (template.opinion) {
    diagnosisForm.recommendation = template.opinion
  }

  ElMessage.success(`已应用模板：${template.name}`)
}

const testSaveTemplate = async () => {
  try {
    const testTemplate = {
      name: '测试模板_' + Date.now(),
      modalityType: 'CT',
      bodyPart: '胸部',
      title: '测试诊断标题',
      findings: '测试影像所见内容',
      opinion: '测试影像意见内容',
      keywords: '测试,模板',
      isPublic: '0',
      isDefault: '0',
      sortOrder: 0
    }

    const res = await addTemplate(testTemplate)

    if (res.code === 200) {
      ElMessage.success('测试模板保存成功')
      loadTemplates() // 重新加载模板列表
    } else {
      ElMessage.error(res.msg || '保存失败')
    }
  } catch (error) {
    console.error('保存测试模板出错:', error)
    ElMessage.error('系统错误')
  }
}
</script>

<style scoped>
.template-test {
  padding: 20px;
}

.template-item {
  border: 1px solid #ddd;
  padding: 10px;
  margin: 10px 0;
  border-radius: 4px;
}

.template-item h4 {
  margin: 0 0 10px 0;
  color: #409eff;
}

.template-item p {
  margin: 5px 0;
  font-size: 14px;
}
</style>
