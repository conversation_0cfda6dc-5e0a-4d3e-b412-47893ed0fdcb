<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>诊断模板管理</span>
        </div>
      </template>
      
      <!-- 紧凑搜索区域 -->
      <div v-show="showSearch" class="compact-search-bar">
        <el-form :model="queryParams" ref="queryRef" :inline="true" size="small" class="compact-form">
          <el-form-item label="模板名称" prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入模板名称"
              clearable
              style="width: 160px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="检查类型" prop="modalityType">
            <el-select v-model="queryParams.modalityType" placeholder="请选择检查类型" clearable style="width: 140px">
              <el-option v-for="dict in modalityTypeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            <el-button type="text" icon="More" @click="showAdvancedSearch = true">更多筛选</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 高级搜索弹窗 -->
      <el-dialog
        v-model="showAdvancedSearch"
        title="高级搜索"
        width="600px"
        :before-close="handleAdvancedClose"
      >
        <el-form :model="advancedParams" label-width="100px">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="模板名称">
                <el-input
                  v-model="advancedParams.name"
                  placeholder="请输入模板名称"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检查类型">
                <el-select v-model="advancedParams.modalityType" placeholder="请选择检查类型" clearable style="width: 100%">
                  <el-option v-for="dict in modalityTypeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="是否公开">
                <el-select v-model="advancedParams.isPublic" placeholder="请选择" clearable style="width: 100%">
                  <el-option
                    v-for="dict in isPublicOptions"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="诊断标题">
                <el-input
                  v-model="advancedParams.title"
                  placeholder="请输入诊断标题"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="handleAdvancedClose">取消</el-button>
            <el-button @click="resetAdvancedSearch">重置</el-button>
            <el-button type="primary" @click="handleAdvancedSearch">搜索</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 操作工具栏 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['diagnosis:template:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['diagnosis:template:remove']"
          >删除</el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!-- 表格区域 - 医疗主题 -->
      <MedicalTable v-loading="loading" :data="templateList" @selection-change="handleSelectionChange" style="width: 100%">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="模板名称" align="center" prop="name" :show-overflow-tooltip="true" min-width="180" />
        <el-table-column label="检查类型" align="center" prop="modalityType" width="100" />
<!--        <el-table-column label="身体部位" align="center" prop="bodyPart" width="100" />-->
        <el-table-column label="诊断标题" align="center" prop="title" :show-overflow-tooltip="true" min-width="150" />
        <el-table-column label="使用次数" align="center" prop="usageCount" width="100">
          <template #default="scope">
            <el-tag type="info" size="small">{{ scope.row.usageCount || 0 }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="left" width="140">
          <template #default="scope">
            <el-tag :type="scope.row.isPublic === '1' ? 'success' : 'info'" size="small">
              {{ scope.row.isPublic === '1' ? '公开' : '私有' }}
            </el-tag>
            <el-tag v-if="scope.row.isDefault === '1'" type="warning" size="small" style="margin-left: 5px">
              默认
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建者" align="center" prop="createBy" width="100" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="160">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template #default="scope">
<!--            <el-button
              type="text"
              icon="View"
              @click="handlePreview(scope.row)"
              size="small"
            >预览</el-button>-->
            <el-button
              type="text"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['diagnosis:template:edit']"
              size="small"
            >修改</el-button>
            <el-button
              type="text"
              icon="Delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['diagnosis:template:remove']"
              size="small"
            >删除</el-button>
          </template>
        </el-table-column>
      </MedicalTable>
      
      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改模板对话框 -->
    <el-dialog :title="title" v-model="open" width="900px" append-to-body>
      <el-form ref="templateFormRef" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="模板名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入模板名称" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="检查类型" prop="modalityType">
              <el-select v-model="form.modalityType" placeholder="请选择检查类型">
                <el-option
                  v-for="dict in modalityTypeOptions"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
<!--          <el-col :span="12">
            <el-form-item label="身体部位" prop="bodyPart">
              <el-input v-model="form.bodyPart" placeholder="请输入身体部位" />
            </el-form-item>
          </el-col>-->
          <el-col :span="24">
            <el-form-item label="诊断标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入诊断标题" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="影像所见" prop="findings">
              <el-input v-model="form.findings" type="textarea" :rows="6" placeholder="请输入影像所见内容" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="影像意见" prop="opinion">
              <el-input v-model="form.opinion" type="textarea" :rows="4" placeholder="请输入影像意见内容" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="关键词" prop="keywords">
              <el-input v-model="form.keywords" placeholder="请输入关键词，用逗号分隔" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否公开" prop="isPublic">
              <el-radio-group v-model="form.isPublic">
                <el-radio
                  v-for="dict in isPublicOptions"
                  :key="dict.value"
                  :label="dict.value"
                >{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否默认" prop="isDefault">
              <el-radio-group v-model="form.isDefault">
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="排序序号" prop="sortOrder">
              <el-input-number v-model="form.sortOrder" :min="0" :max="9999" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" :rows="2" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 模板预览对话框 -->
    <el-dialog v-model="previewVisible" title="模板预览" width="700px">
      <div class="template-preview">
        <div class="preview-header">
          <h3>{{ previewTemplate.name }}</h3>
          <div class="preview-meta">
            <el-tag :type="getCategoryTagType(previewTemplate.category)" size="small">
              {{ getCategoryLabel(previewTemplate.category) }}
            </el-tag>
            <el-tag type="info" size="small">{{ previewTemplate.modalityType }}</el-tag>
            <el-tag type="info" size="small">{{ previewTemplate.bodyPart }}</el-tag>
          </div>
        </div>
        <div class="preview-content">
          <div v-if="previewTemplate.title" class="preview-section">
            <h4>诊断标题</h4>
            <p>{{ previewTemplate.title }}</p>
          </div>
          <div v-if="previewTemplate.findings" class="preview-section">
            <h4>影像所见</h4>
            <p>{{ previewTemplate.findings }}</p>
          </div>
          <div v-if="previewTemplate.opinion" class="preview-section">
            <h4>影像意见</h4>
            <p>{{ previewTemplate.opinion }}</p>
          </div>
          <div v-if="previewTemplate.keywords" class="preview-section">
            <h4>关键词</h4>
            <p>{{ previewTemplate.keywords }}</p>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { listTemplate, getTemplate, addTemplate, updateTemplate, delTemplate } from "@/api/diagnosis/template";
import { parseTime } from '@/utils/ruoyi';
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

// 遮罩层
const loading = ref(false);
// 选中数组
const ids = ref([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 显示高级搜索弹窗
const showAdvancedSearch = ref(false);
// 总条数
const total = ref(0);
// 模板表格数据
const templateList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);

const templateFormRef = ref(null);



// 检查类型选项
const modalityTypeOptions = [
  { value: 'CT', label: 'CT' },
  { value: 'MRI', label: 'MRI' },
  { value: 'DR', label: 'DR' },
  { value: 'US', label: 'US' },
  { value: 'OTHER', label: '其他' }
];

// 是否公开选项
const isPublicOptions = [
  { value: '0', label: '私有' },
  { value: '1', label: '公开' }
];

// 表单参数
const form = reactive({
  id: undefined,
  name: undefined,
  modalityType: undefined,
  bodyPart: undefined,
  title: undefined,
  findings: undefined,
  opinion: undefined,
  keywords: undefined,
  isPublic: '0',
  isDefault: '0',
  sortOrder: 0,
  remark: undefined
});

// 表单校验
const rules = reactive({
  name: [{ required: true, message: '模板名称不能为空', trigger: 'blur' }]
});

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  name: undefined,
  modalityType: undefined,
  bodyPart: undefined,
  isPublic: undefined,
  title: undefined
});

// 高级搜索参数
const advancedParams = reactive({
  name: '',
  modalityType: '',
  isPublic: '',
  title: ''
});

// 预览相关
const previewVisible = ref(false);
const previewTemplate = ref({});

/** 查询模板列表 */
const getList = async () => {
  loading.value = true;
  try {
    const res = await listTemplate(queryParams);
    if (res.code === 200) {
      templateList.value = res.rows;
      total.value = res.total;
    } else {
      ElMessage.error(res.msg || '获取模板列表失败');
    }
  } catch (error) {
    console.error('获取模板列表出错', error);
    ElMessage.error('系统错误，请联系管理员');
  } finally {
    loading.value = false;
  }
};

/** 重置按钮操作 */
const resetQuery = () => {
  Object.keys(queryParams).forEach(key => {
    if (key !== 'pageNum' && key !== 'pageSize') {
      queryParams[key] = undefined;
    }
  });
  queryParams.pageNum = 1;
  handleQuery();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

/** 高级搜索弹窗关闭处理 */
const handleAdvancedClose = () => {
  showAdvancedSearch.value = false;
};

/** 重置高级搜索 */
const resetAdvancedSearch = () => {
  advancedParams.name = '';
  advancedParams.modalityType = '';
  advancedParams.isPublic = '';
  advancedParams.title = '';
};

/** 高级搜索 */
const handleAdvancedSearch = () => {
  // 将高级搜索参数同步到查询参数
  queryParams.name = advancedParams.name;
  queryParams.modalityType = advancedParams.modalityType;
  queryParams.isPublic = advancedParams.isPublic;
  queryParams.title = advancedParams.title;

  // 关闭弹窗并执行搜索
  showAdvancedSearch.value = false;
  handleQuery();
};

/** 重置表单 */
const reset = () => {
  form.id = undefined;
  form.name = undefined;
  form.modalityType = undefined;
  form.bodyPart = undefined;
  form.title = undefined;
  form.findings = undefined;
  form.opinion = undefined;
  form.keywords = undefined;
  form.isPublic = '0';
  form.isDefault = '0';
  form.sortOrder = 0;
  form.remark = undefined;

  if (templateFormRef.value) {
    templateFormRef.value.resetFields();
  }
};

/** 取消按钮 */
const cancel = () => {
  open.value = false;
  reset();
};

/** 选择条目触发事件 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  open.value = true;
  title.value = "添加模板";
};

/** 修改按钮操作 */
const handleUpdate = async (row) => {
  try {
    reset();
    const id = row.id || ids.value[0];
    const res = await getTemplate(id);
    if (res.code === 200) {
      Object.assign(form, res.data);
      title.value = "修改模板";
      open.value = true;
    } else {
      ElMessage.error(res.msg || '获取模板详情失败');
    }
  } catch (error) {
    console.error('获取模板详情出错', error);
    ElMessage.error('系统错误，请联系管理员');
  }
};

/** 提交按钮 */
const submitForm = async () => {
  templateFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const api = form.id ? updateTemplate : addTemplate;
        const res = await api(form);
        if (res.code === 200) {
          ElMessage.success('保存成功');
          open.value = false;
          getList();
        } else {
          ElMessage.error(res.msg || '保存失败');
        }
      } catch (error) {
        console.error('保存模板出错', error);
        ElMessage.error('系统错误，请联系管理员');
      }
    }
  });
};

/** 预览按钮操作 */
const handlePreview = (row) => {
  previewTemplate.value = { ...row };
  previewVisible.value = true;
};

/** 删除按钮操作 */
const handleDelete = (row) => {
  const templateIds = row.id || ids.value;
  if (!templateIds.length && !row.id) {
    return;
  }

  ElMessageBox.confirm(
    '是否确认删除所选模板?',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        const res = await delTemplate(templateIds);
        if (res.code === 200) {
          getList();
          ElMessage.success('删除成功');
        } else {
          ElMessage.error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除模板出错', error);
        ElMessage.error('系统错误，请联系管理员');
      }
    })
    .catch(() => {});
};



// 页面加载时获取数据
onMounted(() => {
  getList();
});
</script>

<style scoped>
.template-preview {
  max-height: 500px;
  overflow-y: auto;
}

.preview-header {
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
}

.preview-header h3 {
  margin: 0 0 10px 0;
  color: #303133;
}

.preview-meta {
  display: flex;
  gap: 8px;
}

.preview-section {
  margin-bottom: 20px;
}

.preview-section h4 {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 14px;
  font-weight: 600;
}

.preview-section p {
  margin: 0;
  color: #303133;
  line-height: 1.6;
  white-space: pre-wrap;
}

/* 紧凑搜索栏样式 */
.compact-search-bar {
  background: #fafafa;
  padding: 16px 20px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.compact-form {
  margin: 0;
}

.compact-form :deep(.el-form-item) {
  margin-right: 16px;
  margin-bottom: 0;
}

.compact-form :deep(.el-form-item__label) {
  color: #606266;
  font-weight: 500;
  font-size: 14px;
}

.compact-form :deep(.el-button) {
  margin-left: 8px;
}

.compact-form :deep(.el-button--text) {
  color: #409eff;
  padding: 8px 12px;
}

/* 搜索表单样式优化 */
.search-form {
  width: 100%;
  margin-bottom: 20px;
}

.search-form .el-form-item {
  margin-bottom: 16px;
}

.search-form .el-input,
.search-form .el-select {
  width: 100%;
}

/* 表格容器样式 */
.el-table {
  width: 100% !important;
}

/* 确保页面容器占满宽度 */
.app-container {
  width: 100%;
  padding: 10px;
}

/* 卡片样式优化 */
.box-card {
  width: 100%;
  margin: 0;
}

.box-card .el-card__body {
  padding: 20px;
}

/* 表格行样式优化 */
.el-table .el-table__row {
  cursor: pointer;
}

.el-table .el-table__row:hover {
  background-color: #f5f7fa;
}
</style>