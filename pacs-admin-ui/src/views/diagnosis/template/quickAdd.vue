<template>
  <div>
    <el-dialog
      :title="title"
      v-model="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form ref="templateFormRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入模板名称" />
        </el-form-item>
        <el-form-item label="检查类型" prop="modalityType">
          <el-select v-model="form.modalityType" placeholder="请选择检查类型">
            <el-option
              v-for="dict in modalityTypeOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="身体部位" prop="bodyPart">
          <el-input v-model="form.bodyPart" placeholder="请输入身体部位" />
        </el-form-item>
        <el-form-item label="是否公开" prop="isPublic">
          <el-radio-group v-model="form.isPublic">
            <el-radio label="0">私有</el-radio>
            <el-radio label="1">公开</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="诊断标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入诊断标题" />
        </el-form-item>
        <el-form-item label="诊断内容" prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="6"
            placeholder="请输入诊断内容"
          />
        </el-form-item>
        <el-form-item label="诊断结论" prop="conclusion">
          <el-input
            v-model="form.conclusion"
            type="textarea"
            :rows="3"
            placeholder="请输入诊断结论"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, defineExpose, defineEmits } from 'vue';
import { ElMessage } from 'element-plus';
import { addTemplate } from '@/api/diagnosis/template';

const emit = defineEmits(['success']);

// 弹窗状态
const dialogVisible = ref(false);
const title = ref('添加模板');
const submitLoading = ref(false);
const templateFormRef = ref(null);

// 检查类型选项
const modalityTypeOptions = [
  { value: 'CT', label: 'CT' },
  { value: 'MRI', label: 'MRI' },
  { value: 'DR', label: 'DR' },
  { value: 'US', label: 'US' },
  { value: 'OTHER', label: '其他' }
];

// 表单数据
const form = reactive({
  name: '',
  title: '',
  content: '',
  conclusion: '',
  modalityType: '',
  bodyPart: '',
  isPublic: '0'
});

// 表单校验规则
const rules = {
  name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  title: [{ required: true, message: '请输入诊断标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入诊断内容', trigger: 'blur' }],
  conclusion: [{ required: true, message: '请输入诊断结论', trigger: 'blur' }],
  modalityType: [{ required: true, message: '请选择检查类型', trigger: 'change' }]
};

// 打开弹窗
const showDialog = (data = {}) => {
  // 重置表单
  form.name = data.name || '';
  form.title = data.title || '';
  form.content = data.content || '';
  form.conclusion = data.conclusion || '';
  form.modalityType = data.modalityType || '';
  form.bodyPart = data.bodyPart || '';
  form.isPublic = data.isPublic || '0';
  
  dialogVisible.value = true;
  
  // 延迟重置表单校验状态
  if (templateFormRef.value) {
    setTimeout(() => {
      templateFormRef.value.resetFields();
    }, 0);
  }
};

// 提交表单
const handleSubmit = () => {
  templateFormRef.value.validate(async (valid) => {
    if (!valid) return;
    
    submitLoading.value = true;
    try {
      const res = await addTemplate(form);
      if (res.code === 200) {
        ElMessage.success('保存成功');
        dialogVisible.value = false;
        emit('success');
      } else {
        ElMessage.error(res.msg || '保存失败');
      }
    } catch (error) {
      console.error('保存模板出错', error);
      ElMessage.error('系统错误，请联系管理员');
    } finally {
      submitLoading.value = false;
    }
  });
};

// 对外暴露方法
defineExpose({
  showDialog
});
</script> 