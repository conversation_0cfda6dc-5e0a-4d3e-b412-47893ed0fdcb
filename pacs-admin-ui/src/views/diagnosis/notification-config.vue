<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>新患者提醒配置</span>
          <el-button type="primary" @click="saveConfig" :loading="saving">
            保存配置
          </el-button>
        </div>
      </template>

      <el-form :model="config" :rules="rules" ref="configForm" label-width="150px">
        <!-- 主开关 -->
        <el-form-item label="启用新患者提醒" prop="enabled">
          <el-switch
            v-model="config.enabled"
            active-text="启用"
            inactive-text="禁用"
            @change="handleMainSwitchChange"
          />
          <div class="form-tip">
            开启后，系统会自动检测新的待诊断患者并发送提醒
          </div>
        </el-form-item>

        <!-- 检查间隔 -->
        <el-form-item label="检查间隔" prop="checkInterval" v-if="config.enabled">
          <el-input-number
            v-model="config.checkInterval"
            :min="10000"
            :max="300000"
            :step="5000"
            controls-position="right"
            style="width: 200px"
          />
          <span class="unit">毫秒</span>
          <div class="form-tip">
            系统检查新患者的时间间隔，建议设置为30秒以上
          </div>
        </el-form-item>

        <!-- 提示音配置 -->
        <el-form-item label="提示音设置" v-if="config.enabled">
          <div class="sound-config">
            <el-switch
              v-model="config.soundEnabled"
              active-text="启用提示音"
              inactive-text="禁用提示音"
            />
            
            <div v-if="config.soundEnabled" class="sound-details">
              <el-form-item label="音量" style="margin-top: 15px;">
                <el-slider
                  v-model="config.volume"
                  :min="0"
                  :max="1"
                  :step="0.1"
                  :format-tooltip="formatVolumeTooltip"
                  style="width: 200px"
                />
              </el-form-item>
              
              <el-form-item label="音频文件">
                <el-input
                  v-model="config.soundFile"
                  placeholder="请输入音频文件路径"
                  style="width: 300px"
                />
                <el-button @click="testSound" style="margin-left: 10px;">
                  测试播放
                </el-button>
              </el-form-item>
            </div>
          </div>
        </el-form-item>

        <!-- 弹窗配置 -->
        <el-form-item label="弹窗设置" v-if="config.enabled">
          <div class="popup-config">
            <el-switch
              v-model="config.popupEnabled"
              active-text="启用弹窗提醒"
              inactive-text="禁用弹窗提醒"
            />
            
            <div v-if="config.popupEnabled" class="popup-details">
              <el-form-item label="显示位置" style="margin-top: 15px;">
                <el-select v-model="config.position" style="width: 200px">
                  <el-option label="右下角" value="bottom-right" />
                  <el-option label="右上角" value="top-right" />
                  <el-option label="左下角" value="bottom-left" />
                  <el-option label="左上角" value="top-left" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="显示时长">
                <el-input-number
                  v-model="config.duration"
                  :min="3000"
                  :max="30000"
                  :step="1000"
                  controls-position="right"
                  style="width: 200px"
                />
                <span class="unit">毫秒</span>
              </el-form-item>
            </div>
          </div>
        </el-form-item>

        <!-- 提醒内容配置 -->
        <el-form-item label="提醒内容" v-if="config.enabled">
          <div class="content-config">
            <el-form-item label="标题">
              <el-input
                v-model="config.title"
                placeholder="请输入提醒标题"
                style="width: 300px"
              />
            </el-form-item>
            
            <el-form-item label="内容模板">
              <el-input
                v-model="config.message"
                type="textarea"
                :rows="3"
                placeholder="请输入提醒内容，{count}会被替换为新患者数量"
                style="width: 400px"
              />
              <div class="form-tip">
                使用 {count} 作为占位符，会被替换为实际的新患者数量
              </div>
            </el-form-item>
          </div>
        </el-form-item>

        <!-- 高级设置 -->
        <el-form-item label="高级设置" v-if="config.enabled">
          <div class="advanced-config">
            <el-form-item label="最大提醒次数">
              <el-input-number
                v-model="config.maxCount"
                :min="1"
                :max="10"
                controls-position="right"
                style="width: 200px"
              />
              <div class="form-tip">
                同一批新患者的最大提醒次数，避免重复打扰
              </div>
            </el-form-item>
          </div>
        </el-form-item>

        <!-- 测试功能 -->
        <el-form-item label="功能测试" v-if="config.enabled">
          <el-button @click="testNotification" type="success">
            测试提醒功能
          </el-button>
          <div class="form-tip">
            点击测试按钮可以预览提醒效果
          </div>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 音频元素 -->
    <audio ref="testAudio" preload="auto"></audio>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { getNotificationConfig, updateNotificationConfig } from '@/api/diagnosis/diagnosis'

// 响应式数据
const config = ref({
  enabled: false,
  soundEnabled: true,
  popupEnabled: true,
  checkInterval: 30000,
  soundFile: '/admin/sounds/new-patient-notification.mp3',
  volume: 0.8,
  position: 'bottom-right',
  duration: 5000,
  title: '新影像待诊断',
  message: '您有{count}个新影像待诊断，请注意查看',
  maxCount: 3
})

const saving = ref(false)
const configForm = ref(null)
const testAudio = ref(null)

// 表单验证规则
const rules = {
  checkInterval: [
    { required: true, message: '请设置检查间隔', trigger: 'blur' },
    { type: 'number', min: 10000, message: '检查间隔不能少于10秒', trigger: 'blur' }
  ],
  title: [
    { required: true, message: '请输入提醒标题', trigger: 'blur' }
  ],
  message: [
    { required: true, message: '请输入提醒内容', trigger: 'blur' }
  ]
}

// 初始化
onMounted(() => {
  loadConfig()
})

// 加载配置
const loadConfig = async () => {
  try {
    const response = await getNotificationConfig()
    if (response.code === 200) {
      Object.assign(config.value, response.data)
      
      // 转换数据类型
      config.value.checkInterval = parseInt(config.value.checkInterval) || 30000
      config.value.duration = parseInt(config.value.duration) || 5000
      config.value.volume = parseFloat(config.value.volume) || 0.8
      config.value.maxCount = parseInt(config.value.maxCount) || 3
    }
  } catch (error) {
    ElMessage.error('加载配置失败')
    console.error('加载配置失败:', error)
  }
}

// 保存配置
const saveConfig = async () => {
  if (!configForm.value) return
  
  try {
    await configForm.value.validate()
    saving.value = true
    
    const response = await updateNotificationConfig(config.value)
    if (response.code === 200) {
      ElMessage.success('配置保存成功')
    } else {
      ElMessage.error(response.msg || '配置保存失败')
    }
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error('配置保存失败')
      console.error('保存配置失败:', error)
    }
  } finally {
    saving.value = false
  }
}

// 主开关变化处理
const handleMainSwitchChange = (value) => {
  if (!value) {
    ElMessage.info('新患者提醒功能已禁用')
  }
}

// 格式化音量提示
const formatVolumeTooltip = (value) => {
  return `${Math.round(value * 100)}%`
}

// 测试音频播放
const testSound = () => {
  if (testAudio.value && config.value.soundFile) {
    testAudio.value.src = config.value.soundFile
    testAudio.value.volume = config.value.volume
    testAudio.value.play().catch(error => {
      ElMessage.error('音频播放失败，请检查文件路径')
      console.error('音频播放失败:', error)
    })
  }
}

// 测试提醒功能
const testNotification = () => {
  // 测试提示音
  if (config.value.soundEnabled) {
    testSound()
  }
  
  // 测试弹窗
  if (config.value.popupEnabled) {
    const message = config.value.message.replace('{count}', '2')
    ElNotification({
      title: config.value.title,
      message: message + '\n（这是测试提醒）',
      type: 'warning',
      position: config.value.position.replace('-', ''),
      duration: config.value.duration
    })
  }
  
  ElMessage.success('测试提醒已发送')
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.unit {
  margin-left: 10px;
  color: #909399;
}

.sound-config,
.popup-config,
.content-config,
.advanced-config {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.sound-details,
.popup-details {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e4e7ed;
}

.el-form-item {
  margin-bottom: 20px;
}

.sound-details .el-form-item,
.popup-details .el-form-item {
  margin-bottom: 15px;
}
</style>
