<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="header-title">诊断报告详情</span>
          <div class="header-actions">
            <el-button @click="handleBack">返回</el-button>
          </div>
        </div>
      </template>
      
      <div v-loading="loading">
        <!-- 报告样式容器 -->
        <div class="report-container" v-if="diagnosis">
          <!-- 报告头部 -->
          <div class="report-header">
            <div class="hospital-info">
              <div class="hospital-name">鄂托克旗人民医院</div>
              <div class="report-title">{{ study?.modality || 'CT' }}检查报告单</div>
              <div style="display: flex;justify-content: flex-start;width: 100%;font-size: 13px;margin-top: 5px;">
                检查号：{{ study?.originalPatientId || '' }}
              </div>
            </div>
            <div class="report-divider"></div>
          </div>
          
          <!-- 患者信息 -->
          <div class="patient-info">
            <div class="info-row">
              <div class="info-item">
                <span class="info-label">姓名：</span>
                <span class="info-value">{{ study?.patientName || '' }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">性别：</span>
                <span class="info-value">{{ study?.patientSex || '' }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">年龄：</span>
                <span class="info-value">{{ study?.patientAge || '' }}</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <span class="info-label">检查项目：</span>
                <span class="info-value">{{ study?.studyDescription || '' }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">检查日期：</span>
                <span class="info-value">{{ parseTime(study?.studyDate) || '' }}</span>
              </div>
            </div>
          </div>
          
          <!-- 诊断内容 -->
          <div class="report-content">
            <div class="report-section">
              <div class="section-title">诊断结论：</div>
              <div class="section-content">{{ diagnosis.diagnose }}</div>
            </div>
            
            <div class="report-section" v-if="diagnosis.recommendation">
              <div class="section-title">建议：</div>
              <div class="section-content">{{ diagnosis.recommendation }}</div>
            </div>
            
            <div class="report-section" v-if="diagnosis.remark">
              <div class="section-title">备注：</div>
              <div class="section-content">{{ diagnosis.remark }}</div>
            </div>
          </div>
          
          <!-- 报告底部 -->
          <div class="report-footer">
            <div class="signature">
              <div class="signature-item">
                <span class="signature-label">诊断医生：</span>
                <span class="signature-value">{{ diagnosis.doctor }}</span>
              </div>
              <div class="signature-item">
                <span class="signature-label">诊断日期：</span>
                <span class="signature-value">{{ parseTime(diagnosis.createTime) }}</span>
              </div>
            </div>
            <div class="signature" v-if="diagnosis.auditBy">
              <div class="signature-item">
                <span class="signature-label">审核医生：</span>
                <span class="signature-value">{{ diagnosis.auditBy }}</span>
              </div>
              <div class="signature-item">
                <span class="signature-label">审核日期：</span>
                <span class="signature-value">{{ parseTime(diagnosis.auditTime) }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 审核操作区域 -->
        <div class="audit-actions" v-if="diagnosis && diagnosis.status === '1'">
          <el-divider content-position="center">审核操作</el-divider>
          <el-form ref="auditFormRef" :model="auditForm" :rules="auditRules" label-width="100px">
            <el-form-item label="审核意见" prop="auditComment">
              <el-input v-model="auditForm.auditComment" type="textarea" rows="4" placeholder="请输入审核意见"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="success" @click="handleApprove" :loading="approveLoading">通过审核</el-button>
              <el-button type="danger" @click="handleReject" :loading="rejectLoading">驳回</el-button>
              <el-button @click="handleBack">返回</el-button>
            </el-form-item>
          </el-form>
        </div>
        
        <!-- 已审核状态显示 -->
        <div class="audit-status" v-else-if="diagnosis && diagnosis.status === '2'">
          <el-alert
            title="该诊断报告已审核通过"
            type="success"
            :closable="false"
            show-icon
          >
            <template #default>
              <div>审核人: {{ diagnosis.auditBy }}</div>
              <div>审核时间: {{ parseTime(diagnosis.auditTime) }}</div>
              <div v-if="diagnosis.auditComment">审核意见: {{ diagnosis.auditComment }}</div>
            </template>
          </el-alert>
          <div class="mt20">
            <el-button @click="handleBack">返回</el-button>
            <el-button type="warning" @click="handleCancel" v-if="hasAuditPermission">撤销审核</el-button>
          </div>
        </div>
        
        <!-- 已驳回状态显示 -->
        <div class="audit-status" v-else-if="diagnosis && diagnosis.status === '3'">
          <el-alert
            title="该诊断报告已被驳回"
            type="error"
            :closable="false"
            show-icon
          >
            <template #default>
              <div>审核人: {{ diagnosis.auditBy }}</div>
              <div>审核时间: {{ parseTime(diagnosis.auditTime) }}</div>
              <div v-if="diagnosis.auditComment">驳回原因: {{ diagnosis.auditComment }}</div>
            </template>
          </el-alert>
          <div class="mt20">
            <el-button @click="handleBack">返回</el-button>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getDiagnosis, auditDiagnosis, rejectDiagnosis, cancelAudit } from '@/api/diagnosis/diagnosis';
import { getStudy } from '@/api/pacs/study';
import { parseTime } from '@/utils/ruoyi';
import useUserStore from '@/store/modules/user';

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
const diagnosisId = ref(route.query.diagnosisId);

const loading = ref(false);
const diagnosis = ref(null);
const study = ref(null);
const approveLoading = ref(false);
const rejectLoading = ref(false);
const cancelLoading = ref(false);

// 审核表单
const auditForm = reactive({
  id: null,
  auditComment: ''
});

// 审核表单校验规则
const auditRules = {
  auditComment: [
    { required: true, message: '请输入审核意见', trigger: 'blur' }
  ]
};

// 是否有审核权限
const hasAuditPermission = computed(() => {
  return userStore.permissions.includes('diagnosis:diagnosis:audit');
});

// 加载诊断报告详情
const loadDiagnosis = async () => {
  if (!diagnosisId.value) {
    ElMessage.error('诊断ID不能为空');
    return;
  }
  
  loading.value = true;
  try {
    const res = await getDiagnosis(diagnosisId.value);
    if (res.code === 200) {
      diagnosis.value = res.data;
      auditForm.id = res.data.id;
      
      // 加载检查信息
      if (res.data.studyId) {
        loadStudy(res.data.studyId);
      }
    } else {
      ElMessage.error(res.msg || '获取诊断报告失败');
    }
  } catch (error) {
    console.error('加载诊断报告出错', error);
    ElMessage.error('系统错误，请联系管理员');
  } finally {
    loading.value = false;
  }
};

// 加载检查信息
const loadStudy = async (studyId) => {
  try {
    const res = await getStudy(studyId);
    if (res.code === 200) {
      study.value = res.data;
    }
  } catch (error) {
    console.error('加载检查信息出错', error);
  }
};

// 返回按钮操作
const handleBack = () => {
  router.go(-1);
};

// 通过审核
const handleApprove = async () => {
  if (!diagnosis.value || !diagnosis.value.id) {
    ElMessage.error('诊断报告不存在');
    return;
  }
  
  approveLoading.value = true;
  try {
    const res = await auditDiagnosis(diagnosis.value.id);
    if (res.code === 200) {
      ElMessage.success('审核通过成功');
      // 重新加载诊断报告
      loadDiagnosis();
    } else {
      ElMessage.error(res.msg || '审核失败');
    }
  } catch (error) {
    console.error('审核出错', error);
    ElMessage.error('系统错误，请联系管理员');
  } finally {
    approveLoading.value = false;
  }
};

// 驳回审核
const handleReject = async () => {
  if (!diagnosis.value || !diagnosis.value.id) {
    ElMessage.error('诊断报告不存在');
    return;
  }
  
  // 表单验证
  const auditFormRef = ref(null);
  if (!auditForm.auditComment) {
    ElMessage.warning('请输入驳回原因');
    return;
  }
  
  rejectLoading.value = true;
  try {
    const data = {
      id: diagnosis.value.id,
      auditComment: auditForm.auditComment
    };
    
    const res = await rejectDiagnosis(data);
    if (res.code === 200) {
      ElMessage.success('驳回成功');
      // 重新加载诊断报告
      loadDiagnosis();
    } else {
      ElMessage.error(res.msg || '驳回失败');
    }
  } catch (error) {
    console.error('驳回出错', error);
    ElMessage.error('系统错误，请联系管理员');
  } finally {
    rejectLoading.value = false;
  }
};

// 撤销审核
const handleCancel = async () => {
  if (!diagnosis.value || !diagnosis.value.id) {
    ElMessage.error('诊断报告不存在');
    return;
  }
  
  ElMessageBox.confirm(
    '确定要撤销该诊断报告的审核吗？',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    cancelLoading.value = true;
    try {
      const res = await cancelAudit(diagnosis.value.id);
      if (res.code === 200) {
        ElMessage.success('撤销审核成功');
        // 重新加载诊断报告
        loadDiagnosis();
      } else {
        ElMessage.error(res.msg || '撤销审核失败');
      }
    } catch (error) {
      console.error('撤销审核出错', error);
      ElMessage.error('系统错误，请联系管理员');
    } finally {
      cancelLoading.value = false;
    }
  }).catch(() => {});
};

onMounted(() => {
  loadDiagnosis();
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  font-size: 18px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.report-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  border: 1px solid #e0e0e0;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.report-header {
  margin-bottom: 20px;
}

.hospital-info {
  text-align: center;
  margin-bottom: 10px;
}

.hospital-name {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 5px;
}

.report-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}

.report-divider {
  height: 2px;
  background-color: #000;
  margin: 10px 0;
}

.patient-info {
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.info-item {
  flex: 1;
  min-width: 200px;
  margin-bottom: 5px;
}

.info-label {
  font-weight: bold;
}

.report-content {
  margin-bottom: 30px;
}

.report-section {
  margin-bottom: 20px;
}

.section-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.section-content {
  white-space: pre-wrap;
  line-height: 1.6;
}

.report-footer {
  margin-top: 30px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.signature {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-bottom: 10px;
}

.signature-item {
  margin-bottom: 5px;
}

.signature-label {
  font-weight: bold;
}

.audit-actions {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
  border: 1px solid #e0e0e0;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.audit-status {
  max-width: 800px;
  margin: 20px auto;
}

.mt20 {
  margin-top: 20px;
}
</style>
