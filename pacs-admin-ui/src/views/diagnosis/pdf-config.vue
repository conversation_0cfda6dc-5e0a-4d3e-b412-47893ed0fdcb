<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>诊断PDF生成配置</span>
          <div class="header-buttons">
            <el-button type="primary" @click="saveConfig" :loading="saveLoading">
              <el-icon><Check /></el-icon>
              保存配置
            </el-button>
            <el-button type="warning" @click="resetConfig" :loading="resetLoading">
              <el-icon><RefreshLeft /></el-icon>
              重置默认
            </el-button>
            <el-button type="info" @click="testConfig" :loading="testLoading">
              <el-icon><Connection /></el-icon>
              测试配置
            </el-button>
          </div>
        </div>
      </template>

      <el-form :model="configForm" :rules="rules" ref="configFormRef" label-width="180px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="医院名称" prop="hospitalName">
              <el-input v-model="configForm.hospitalName" placeholder="请输入医院名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报告标题" prop="reportTitle">
              <el-input v-model="configForm.reportTitle" placeholder="请输入报告标题" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="报告生成页面URL" prop="reportGenerateUrl">
              <el-input v-model="configForm.reportGenerateUrl" placeholder="请输入报告生成页面URL" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="自动生成开关" prop="autoGenerate">
              <el-switch 
                v-model="configForm.autoGenerate" 
                active-value="true" 
                inactive-value="false"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="生成超时时间(秒)" prop="timeout">
              <el-input-number 
                v-model="configForm.timeout" 
                :min="60" 
                :max="1800" 
                placeholder="超时时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最大并发数" prop="maxConcurrent">
              <el-input-number 
                v-model="configForm.maxConcurrent" 
                :min="1" 
                :max="10" 
                placeholder="并发数"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="存储路径" prop="storagePath">
              <el-input v-model="configForm.storagePath" placeholder="OSS存储路径前缀" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报告作者" prop="author">
              <el-input v-model="configForm.author" placeholder="PDF元数据作者信息" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="模板文件路径" prop="templatePath">
              <el-input v-model="configForm.templatePath" placeholder="支持HTTP/HTTPS网络URL或本地路径" />
              <div class="form-tip">支持网络URL（如：http://example.com/template.json）或本地路径</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="字体配置路径" prop="fontConfigPath">
              <el-input v-model="configForm.fontConfigPath" placeholder="字体配置文件路径" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 测试结果显示 -->
      <el-card v-if="testResult" class="test-result-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><DataAnalysis /></el-icon>
            <span>配置测试结果</span>
          </div>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="报告生成URL">
            <el-tag :type="testResult.urlAccessible ? 'success' : 'danger'">
              {{ testResult.urlAccessible ? '可访问' : '不可访问' }}
            </el-tag>
            <span style="margin-left: 10px;">{{ testResult.reportGenerateUrl }}</span>
          </el-descriptions-item>
          
          <el-descriptions-item label="模板文件">
            <el-tag :type="testResult.templateExists ? 'success' : 'danger'">
              {{ testResult.templateExists ? '存在' : '不存在' }}
            </el-tag>
            <span style="margin-left: 10px;">{{ testResult.templatePath }}</span>
          </el-descriptions-item>
          
          <el-descriptions-item label="自动生成">
            <el-tag :type="testResult.autoGenerateEnabled ? 'success' : 'warning'">
              {{ testResult.autoGenerateEnabled ? '已启用' : '已禁用' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Check, 
  RefreshLeft, 
  Connection, 
  DataAnalysis 
} from '@element-plus/icons-vue'
import request from '@/utils/request'

// 响应式数据
const configFormRef = ref()
const saveLoading = ref(false)
const resetLoading = ref(false)
const testLoading = ref(false)
const testResult = ref(null)

const configForm = ref({
  hospitalName: '',
  reportTitle: '',
  reportGenerateUrl: '',
  autoGenerate: 'true',
  timeout: 300,
  maxConcurrent: 3,
  storagePath: '',
  templatePath: '',
  fontConfigPath: '',
  author: ''
})

// 表单验证规则
const rules = {
  hospitalName: [
    { required: true, message: '请输入医院名称', trigger: 'blur' }
  ],
  reportTitle: [
    { required: true, message: '请输入报告标题', trigger: 'blur' }
  ],
  reportGenerateUrl: [
    { required: true, message: '请输入报告生成页面URL', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
  ],
  timeout: [
    { required: true, message: '请输入超时时间', trigger: 'blur' }
  ],
  maxConcurrent: [
    { required: true, message: '请输入最大并发数', trigger: 'blur' }
  ],
  storagePath: [
    { required: true, message: '请输入存储路径', trigger: 'blur' }
  ],
  author: [
    { required: true, message: '请输入报告作者', trigger: 'blur' }
  ],
  templatePath: [
    { required: true, message: '请输入模板文件路径', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error('请输入模板文件路径'))
        } else if (value.startsWith('http://') || value.startsWith('https://')) {
          // 网络URL验证
          try {
            new URL(value)
            callback()
          } catch {
            callback(new Error('请输入正确的URL格式'))
          }
        } else {
          // 本地路径验证
          if (value.includes('..') || value.startsWith('/')) {
            callback(new Error('本地路径不能包含..或以/开头'))
          } else {
            callback()
          }
        }
      },
      trigger: 'blur'
    }
  ]
}

// 加载配置
const loadConfig = async () => {
  try {
    const response = await request({
      url: '/diagnosis/pdf/config/list',
      method: 'get'
    })
    
    if (response.code === 200) {
      Object.assign(configForm.value, response.data)
      // 确保数值类型正确
      configForm.value.timeout = parseInt(configForm.value.timeout) || 300
      configForm.value.maxConcurrent = parseInt(configForm.value.maxConcurrent) || 3
    }
  } catch (error) {
    ElMessage.error('加载配置失败: ' + (error.message || error))
  }
}

// 保存配置
const saveConfig = async () => {
  try {
    await configFormRef.value.validate()
    
    saveLoading.value = true
    
    const response = await request({
      url: '/diagnosis/pdf/config/update',
      method: 'put',
      data: configForm.value
    })
    
    if (response.code === 200) {
      ElMessage.success('配置保存成功')
      testResult.value = null // 清除测试结果
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('保存配置失败: ' + (error.message || error))
    }
  } finally {
    saveLoading.value = false
  }
}

// 重置配置
const resetConfig = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置所有配置为默认值吗？此操作不可撤销。',
      '确认重置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    resetLoading.value = true
    
    const response = await request({
      url: '/diagnosis/pdf/config/reset',
      method: 'post'
    })
    
    if (response.code === 200) {
      ElMessage.success('配置已重置为默认值')
      await loadConfig() // 重新加载配置
      testResult.value = null // 清除测试结果
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重置配置失败: ' + (error.message || error))
    }
  } finally {
    resetLoading.value = false
  }
}

// 测试配置
const testConfig = async () => {
  try {
    testLoading.value = true
    
    const response = await request({
      url: '/diagnosis/pdf/config/test',
      method: 'post'
    })
    
    if (response.code === 200) {
      testResult.value = response.data
      ElMessage.success('配置测试完成')
    }
  } catch (error) {
    ElMessage.error('测试配置失败: ' + (error.message || error))
  } finally {
    testLoading.value = false
  }
}

// 组件挂载时加载配置
onMounted(() => {
  loadConfig()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.header-buttons {
  display: flex;
  gap: 10px;
}

.test-result-card {
  margin-top: 20px;
}

.test-result-card .card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}
</style>
