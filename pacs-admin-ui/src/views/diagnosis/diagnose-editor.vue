<template>
  <div class="app-container diagnosis-editor">

    <div class="viewer-header">
      <h4>诊断报告编辑</h4>
      <div class="action-buttons">
        <el-button type="primary" size="small" @click="syncDicomData" :loading="syncLoading">
          <el-icon><RefreshRight/></el-icon>
          刷新影像
        </el-button>
        <el-button type="primary" size="small" @click="openFullscreenViewer" :disabled="study && study.hasImages === false">
          <el-icon><full-screen/></el-icon>
          影像阅读
        </el-button>
        <el-button type="primary" size="small" @click="openPatientInfoDrawer">
          <el-icon><InfoFilled/></el-icon>
          患者详情
        </el-button>
        <el-button type="primary" size="small" @click="printReport" v-if="study">
          <el-icon><Printer/></el-icon>
          打印报告
        </el-button>
        <el-button type="primary" size="small" @click="previewReport" v-if="study">
          <el-icon><View/></el-icon>
          预览报告
        </el-button>
      </div>
    </div>


    <!-- 报告样式容器 -->
    <div class="report-wrapper" v-if="study">
      <div class="report-container" id="printableReport">
      <!-- 报告头部 -->
      <div class="report-header">
        <div class="header-content">
          <div class="hospital-info">
            <!--              <div class="hospital-logo">
                            <img src="/logo.png" alt="医院标志" onerror="this.style.display='none'"/>
                          </div>-->
            <div class="hospital-name">{{ diagnosisConfig.hospitalName || '鄂托克旗人民医院' }}</div>
            <div class="report-title">{{ study.modality || 'CT' }}检查报告单</div>
            <div class="exam-number">
              检查号：{{ study.originalPatientId || '' }}
            </div>
          </div>
          <!-- 二维码区域 -->
          <div class="qr-code-container" v-if="study && study.originalPatientId && qrCodeGenerated">
            <div class="qr-code-wrapper">
              <canvas ref="qrCodeCanvas" class="qr-code-canvas"></canvas>
              <div class="qr-code-label">扫码查看影像</div>
            </div>
          </div>
        </div>
        <div class="report-divider"></div>
      </div>

      <!-- 患者基本信息 -->
      <div class="patient-info-grid">
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">姓名</span>
            <span class="info-value">{{ study.patientName || '' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">性别</span>
            <span class="info-value">{{ study.patientSex === 'Male' || study.patientSex === 'M' ? '男' : '女' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">年龄</span>
            <span class="info-value">{{ calculateAge(study.patientBirthday) }}岁</span>
          </div>
          <div class="info-item">
            <span class="info-label">床号</span>
            <span class="info-value">{{ study.bedNo || '' }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item info-item-wide">
            <span class="info-label">检查时间</span>
            <span class="info-value">{{ formatDateTime(study.checkFinishTime) || '' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">住院号</span>
            <span class="info-value">{{ study.inPatientId || '' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">影像号</span>
            <span class="info-value">{{ study.originalPatientId || '' }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item info-item-wide">
            <span class="info-label">检查部位</span>
            <span class="info-value">{{ study.organ || '' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">送检医生</span>
            <span class="info-value">{{ study.examDoctorName || '' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">申请科室</span>
            <span class="info-value">{{ study.examDepartment || '' }}</span>
          </div>
        </div>
      </div>
      <div class="report-divider"></div>
      <div class="diagnosis-content">
        <!-- 诊断内容编辑 -->
        <div class="editor-container">
          <div v-if="isAudited" class="audit-notice">
            <el-alert
                title="该诊断已经审核，不允许修改"
                type="warning"
                :closable="false"
                show-icon
            >
              <template #default>
                <div>审核人: {{ diagnosisForm.auditBy || '未知' }}</div>
                <div>审核时间: {{ parseTime(diagnosisForm.auditTime) || '未知' }}</div>
              </template>
            </el-alert>
          </div>
          <div v-else-if="!isCreator" class="creator-notice">
            <el-alert
                title="您不是该诊断的创建人，不允许修改"
                type="info"
                :closable="false"
                show-icon
            >
              <template #default>
                <div>创建人: {{ diagnosisForm.doctor || '未知' }}</div>
                <div>创建时间: {{ parseTime(diagnosisForm.createTime) || '未知' }}</div>
              </template>
            </el-alert>
          </div>
          <el-form ref="diagnosisFormRef" :model="diagnosisForm" :rules="rules" label-width="0">
            <div class="report-section">
              <div class="section-header">
                <span>影像所见：</span>
                <template-selector
                  v-if="canEdit"
                  :modality-type="study?.modality"
                  :body-part="study?.organ"
                  @template-selected="handleTemplateSelected"
                  class="template-selector-btn"
                />
              </div>
              <el-form-item prop="diagnose" class="report-content-item">
                <el-input
                    v-model="diagnosisForm.diagnose"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入影像所见内容"
                    @input="triggerAutoSave"
                    :disabled="!canEdit"
                    class="report-textarea"
                ></el-input>
              </el-form-item>
            </div>

            <div class="report-section">
              <div class="section-header">
                <span>影像意见：</span>
              </div>
              <el-form-item prop="recommendation" class="report-content-item">
                <el-input
                    v-model="diagnosisForm.recommendation"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入影像意见"
                    @input="triggerAutoSave"
                    :disabled="!canEdit"
                    class="report-textarea"
                ></el-input>
              </el-form-item>
            </div>

            <!-- 阴阳性结果选择 -->
            <div class="report-section">
              <div class="section-header">阴阳性结果：</div>
              <el-form-item prop="positiveNegative" class="report-content-item">
                <el-radio-group
                    v-model="diagnosisForm.positiveNegative"
                    @change="triggerAutoSave"
                    :disabled="!canEdit"
                    class="positive-negative-group"
                >
                  <el-radio value="positive" class="positive-radio">
                    阳性
                  </el-radio>
                  <el-radio value="negative" class="negative-radio">
                    阴性
                  </el-radio>
<!--                  <el-radio value="uncertain" class="uncertain-radio">
                    <el-icon class="uncertain-icon"><QuestionFilled /></el-icon>
                    待定
                  </el-radio>-->
                </el-radio-group>
              </el-form-item>
            </div>

            <!-- 签名区域 -->
            <div class="report-signature">
              <div class="signature-row">
                <div class="signature-item">
                  <span class="signature-label">报告医生：</span>
                  <div class="signature-content">
                    <div class="signature-image-wrapper" v-if="reportDoctorInfo.signatureUrl">
                      <img :src="reportDoctorInfo.signatureUrl" alt="医生签名" class="signature-image" @error="handleSignatureError('doctor')" />
                    </div>
                    <span class="signature-name" v-else>{{ reportDoctorInfo.name || diagnosisForm.doctor || userStore.name }}</span>
                  </div>
                </div>
                <div class="signature-item">
                  <span class="signature-label">审核医生：</span>
                  <div class="signature-content">
                    <div class="signature-image-wrapper" v-if="auditDoctorInfo.signatureUrl && auditDoctorInfo.name">
                      <img :src="auditDoctorInfo.signatureUrl" alt="审核医生签名" class="signature-image" @error="handleSignatureError('audit')" />
                    </div>
                    <span class="signature-name" v-else>{{ auditDoctorInfo.name || diagnosisForm.auditBy || '' }}</span>
                  </div>
                </div>
                <div class="signature-item">
                  <span class="signature-label">报告时间：</span>
                  <span class="signature-value">{{ diagnosisForm.createTime || '' }}</span>
                </div>
              </div>
            </div>
          </el-form>
        </div>
      </div>
      </div>
    </div>

    <!-- 自动保存提示 -->
    <div class="auto-save-container" v-if="autoSaveStatus">
      <div class="auto-save-indicator">
        <el-tag size="small" type="info">{{ autoSaveStatus }}</el-tag>
      </div>
    </div>

    <!-- 底部按钮区域 -->
    <div class="bottom-actions">
      <div class="action-buttons">
        <el-button type="info" @click="openHistoryDiagnosis">
          <el-icon>
            <document/>
          </el-icon>
          诊断记录
        </el-button>
        <el-button
            @click="saveDiagnosis"
            :loading="saveLoading"
            :disabled="saveLoading || !canEdit"
            v-if="!isAudited && isCreator"
        >暂存
        </el-button>
        <el-button
            type="primary"
            @click="submitDiagnosis"
            :loading="submitLoading"
            :disabled="submitLoading || !canEdit"
            v-if="!isAudited && isCreator"
        >提交
        </el-button>
        <el-button
            type="warning"
            @click="auditDiagnosis"
            :loading="auditLoading"
            :disabled="auditLoading || !canEdit"
            v-if="!isAudited && isCreator"
            v-hasPermi="['diagnosis:diagnosis:audit']"
        >保存并审核
        </el-button>
        <el-button
            type="danger"
            @click="unauditDiagnosis"
            :loading="unauditLoading"
            :disabled="unauditLoading"
            v-if="isAudited && canUnaudit"
            v-hasPermi="['diagnosis:diagnosis:audit']"
        >反审核
        </el-button>
        <el-tag v-if="isAudited" type="success">已审核</el-tag>
      </div>
    </div>

    <!-- 历史诊断记录 -->
    <el-drawer
        v-model="historyDrawer"
        title="诊断记录"
        direction="rtl"
        size="50%"
    >
      <div v-loading="historyLoading">
        <div v-if="diagnosisHistory.length > 0">
          <div v-for="(item, index) in diagnosisHistory" :key="index" class="history-item">
            <div class="history-header">
              <span class="history-date">{{ parseTime(item.createTime) }}</span>
              <span class="history-doctor">医生: {{ item.doctor }}</span>
              <el-tag size="small" :type="getStatusTagType(item.status)">
                {{ getStatusLabel(item.status) }}
              </el-tag>
              <span class="history-audit" v-if="item.auditBy && item.status === '2'">
                    审核人: {{ item.auditBy }} (审核时间: {{ parseTime(item.auditTime) }})
                  </span>
            </div>
            <div class="history-content">
              <div class="history-section">
                <div class="section-title">影像所见:</div>
                <div class="section-content">{{ item.diagnose }}</div>
              </div>
              <div class="history-section">
                <div class="section-title">影像意见:</div>
                <div class="section-content">{{ item.recommendation }}</div>
              </div>
            </div>
            <!--                <div class="history-actions">
                              <el-button type="primary" link @click="copyFromHistory(item)">使用此诊断</el-button>
                            </div>-->
          </div>
        </div>
        <el-empty v-else description="没有诊断记录"></el-empty>
      </div>
    </el-drawer>


    <!-- 全屏影像阅读器弹窗 -->
    <el-dialog
        v-model="fullscreenViewerVisible"
        title="影像阅读器"
        fullscreen
        :destroy-on-close="false"
        :before-close="handleCloseViewer"
        :show-close="true"
    >
      <div class="fullscreen-viewer-container">
        <div class="fullscreen-viewer-content">
          <i-frame v-if="fullscreenViewerUrl" :src="fullscreenViewerUrl"></i-frame>
        </div>
      </div>
    </el-dialog>

    <!-- 患者检查信息抽屉 -->
    <patient-study-drawer
        v-model:visible="patientInfoDrawer"
        :study-data="study"
        :loading="patientInfoLoading"
        @close="handlePatientInfoDrawerClose"
    />

    <!-- 报告预览组件 -->
    <Report ref="reportRef" />
  </div>
</template>

<script setup>
import {ref, reactive, onMounted, computed, onBeforeUnmount, nextTick} from 'vue';
import {useRoute, useRouter} from 'vue-router';
import {ElMessage, ElMessageBox} from 'element-plus';
import {getStudy, getStudyInstanceUid, syncDicomData as apiSyncDicomData} from '@/api/pacs/study';
import {
  getDiagnosis,
  addDiagnosis,
  updateDiagnosis,
  listDiagnosis,
  auditDiagnosis as apiAuditDiagnosis,
  unauditDiagnosis as apiUnauditDiagnosis,
  formatReportData as apiFormatReportData
} from '@/api/diagnosis/diagnosis';
import { getUserProfile, getUserByName } from '@/api/system/user';
import { getDiagnosisConfig } from '@/api/diagnosis/config';
import {listRecordByDiagnosisId} from '@/api/diagnosis/record';
import useUserStore from '@/store/modules/user';

import {parseTime} from '@/utils/ruoyi';
import iFrame from "@/components/iFrame/index.vue";
import {ArrowDown, FullScreen, Document, InfoFilled, RefreshRight, Refresh, Check, Close, QuestionFilled, Printer, View} from '@element-plus/icons-vue';
import hasPermi from '@/directive/permission/hasPermi';
import QRCode from 'qrcode';

import DiagnosisDictSelector from "@/components/DiagnosisDictSelector";
import PatientStudyDrawer from "@/components/PatientStudyDrawer/index.vue";
import Report from "@/components/Report/Report.vue";
import TemplateSelector from "@/components/TemplateSelector/index.vue";

const emit = defineEmits(['switchToStudyList']);

// 格式化日期时间函数（简化版）
const formatDateTime = (dateTime) => {
  if (!dateTime) return '';
  try {
    const date = new Date(dateTime);
    if (isNaN(date.getTime())) return '';

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  } catch (error) {
    return '';
  }
};

// 计算年龄函数
const calculateAge = (birthDate) => {
  if (!birthDate) {
    return '0';
  }

  try {
    // 将出生日期转换为Date对象
    let birth;
    if (typeof birthDate === 'string') {
      // 如果是字符串，尝试解析
      birth = new Date(birthDate.replace(/-/g, '/'));

      // 如果日期无效，尝试只使用年月日部分
      if (isNaN(birth.getTime()) && birthDate.length >= 10) {
        birth = new Date(birthDate.substring(0, 10).replace(/-/g, '/'));
      }
    } else if (birthDate instanceof Date) {
      // 如果已经是Date对象
      birth = birthDate;
    } else {
      console.warn('无法识别的出生日期格式:', birthDate);
      return '0';
    }

    // 检查日期是否有效
    if (isNaN(birth.getTime())) {
      console.warn('无效的出生日期:', birthDate);
      return '0';
    }

    // 计算年龄
    const today = new Date();
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    // 如果当前月份小于出生月份，或者月份相同但当前日期小于出生日期，则年龄减1
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }

    return age.toString();
  } catch (error) {
    console.error('计算年龄时出错:', error);
    return '0';
  }
};

const route = useRoute();
const router = useRouter();
const studyId = ref(route.query.studyId);
const diagnosisId = ref(route.query.diagnosisId);

const study = ref(null);
const viewerUrl = ref('');
const viewerLoading = ref(false); // 添加影像加载状态
const saveLoading = ref(false);
const submitLoading = ref(false);
const auditLoading = ref(false);
const unauditLoading = ref(false);
const autoSaveStatus = ref('');
const autoSaveTimer = ref(null);
const lastSavedContent = ref('');
const historyDrawer = ref(false);
const historyLoading = ref(false);
const diagnosisHistory = ref([]);
const diagnosisFormRef = ref(null);
const userStore = useUserStore();

// 患者信息抽屉相关
const patientInfoDrawer = ref(false);
const patientInfoLoading = ref(false);

// 报告预览相关
const reportRef = ref();

// 二维码相关
const qrCodeCanvas = ref(null);
const qrCodeGenerated = ref(false);

// 诊断配置相关
const diagnosisConfig = ref({
  qrCodeUrl: '',
  hospitalName: '',
  reportTitle: '医学影像诊断报告',
  viewerBaseUrl: '',
  viewerDesktopPath: '/desktop/index.html',
  viewerMobilePath: '/mobile/dicomViewer.html'
});

// 签名图片相关
const doctorSignatureUrl = ref('');
const auditSignatureUrl = ref('');

// 医生信息对象（与后端 prepareReportData 保持一致）
const reportDoctorInfo = ref({
  name: '',
  signatureUrl: ''
});

const auditDoctorInfo = ref({
  name: '',
  signatureUrl: ''
});

// 影像同步相关
const syncLoading = ref(false);
const lastSyncTime = ref('');

// 全屏查看器相关
const fullscreenViewerVisible = ref(false);
const fullscreenViewerUrl = computed(() => {
  if (!studyId.value) return '';

  // 如果明确没有影像数据，则返回空字符串
  if (study.value?.hasImages === false) {
    return '';
  }

  // 如果study.value为空，使用studyId
  if (!study.value) {
    console.warn('study.value is undefined in fullscreenViewerUrl computed property');
    return buildViewerUrl(studyId.value, {isFullscreen: true});
  }

  // 优先使用studyInstanceUid，如果没有则使用studyId
  const studyUid = study.value.studyInstanceUid || studyId.value;
  return buildViewerUrl(studyUid, {isFullscreen: true});
});

// 判断是否已审核
const isAudited = computed(() => {
  return diagnosisForm.status === '2';
});

// 判断当前用户是否是创建人
const isCreator = computed(() => {
  // 如果没有诊断 ID，说明是新创建的诊断，则允许编辑
  if (!diagnosisForm.id) {
    return true;
  }
  // 输出调试信息
  console.log('检查创建者权限:', {
    'diagnosisForm.createBy': diagnosisForm.createBy,
    'userStore.id': userStore.id,
    'userStore.name': userStore.name,
    'diagnosisForm.doctor': diagnosisForm.doctor
  });

  // 特殊情况处理：如果创建者是 admin 但医生字段被设置为若依，则允许编辑
  if (diagnosisForm.createBy === 'admin') {
    return true;
  }

  // 检查createBy字段，如果当前用户的用户名与创建者用户名相同，则允许编辑
  if (diagnosisForm.createBy) {
    // 如果当前用户是创建者，则允许编辑
    if (diagnosisForm.createBy === userStore.id || diagnosisForm.createBy === userStore.name) {
      return true;
    }
  }

  // 如果没有createBy字段或者检查失败，则回退到检查doctor字段
  // 如果没有设置 doctor 字段，也允许编辑（可能是旧数据）
  if (!diagnosisForm.doctor) {
    return true;
  }

  // 判断当前用户是否是诊断医生
  return diagnosisForm.doctor === userStore.name;
});

// 判断是否可以编辑
const canEdit = computed(() => {
  // 如果已经审核，不允许编辑
  if (isAudited.value) {
    return false;
  }
  // 如果不是创建人，不允许编辑
  if (!isCreator.value) {
    return false;
  }
  return true;
});

// 判断是否可以反审核
const canUnaudit = computed(() => {
  // 只有已审核状态才能反审核
  if (!isAudited.value) {
    return false;
  }
  // 只有审核人或者管理员才能反审核
  return diagnosisForm.auditBy === userStore.name || userStore.roles.includes('admin');
});

// 处理从检查列表传递过来的检查数据
const handleStudyData = async (row) => {
  if (!row || !row.id) {
    ElMessage.error('检查数据无效');
    return;
  }

  let diagnosis = row.diagnosis || {};

  // 设置检查ID和诊断ID
  studyId.value = row.id; // 修复：使用检查记录的ID而不是诊断ID
  diagnosisId.value = diagnosis.id || undefined;

  // 重置诊断表单
  diagnosisForm.id = diagnosis?.id || undefined;
  diagnosisForm.studyId = row.id;
  diagnosisForm.checkId = row.id; // 设置检查记录的ID
  diagnosisForm.diagnose = diagnosis.diagnose;
  diagnosisForm.recommendation = diagnosis.recommendation || '';
  diagnosisForm.remark = diagnosis.remark || '';
  diagnosisForm.status = diagnosis.status || '0';
  diagnosisForm.doctor = diagnosis.doctor || userStore.name;
  diagnosisForm.auditBy = diagnosis.auditBy || undefined;
  diagnosisForm.auditTime = diagnosis.auditTime || undefined;

  // 加载检查数据
  loadStudy();

  // 立即生成二维码
  generateQRCode();

  // 加载签名图片
  await loadDoctorSignature();
  if (diagnosisForm.auditBy) {
    await loadAuditSignature();
  }
};

// 打开全屏影像阅读器
const openFullscreenViewer = () => {
  // 检查study.value是否存在
  if (!study.value) {
    console.warn('study.value is undefined in openFullscreenViewer');
    fullscreenViewerVisible.value = true;
    return;
  }

  // 如果没有影像数据，则不打开全屏查看器
  if (study.value.hasImages === false) {
    ElMessage.warning('该患者没有影像数据');
    return;
  }
  fullscreenViewerVisible.value = true;
};

// 关闭全屏影像阅读器
const handleCloseViewer = () => {
  fullscreenViewerVisible.value = false;
};


// 打开历史诊断抽屉
const openHistoryDiagnosis = () => {
  historyDrawer.value = true;
  loadDiagnosisHistory(true); // 传入true表示是通过按钮打开，不需要询问
};

// 诊断表单
const diagnosisForm = reactive({
  id: undefined,
  studyId: studyId.value,
  checkId: studyId.value, // 检查记录的ID
  diagnose: '',
  recommendation: '',
  remark: '',
  status: '0',// 0: 草稿, 1: 待审核, 2: 已完成, 3: 已驳回, 4: 已撤销, 5: 已修订, 6: 已归档
  doctor: userStore.name || '', // 默认设置为当前用户
  auditBy: undefined, // 审核人
  auditTime: undefined, // 审核时间
  positiveNegative: '' // 阴阳性结果
});

// 表单验证规则
const rules = {
  diagnose: [{required: true, message: '请输入诊断结论', trigger: 'blur'}]
};


// 检测设备类型
const detectDevice = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  const screenWidth = window.screen.width;
  const mobileKeywords = ['mobile', 'android', 'iphone', 'ipod', 'windows phone'];
  const isTablet = /ipad|android|tablet/i.test(userAgent) && screenWidth >= 768;

  if (isTablet) return 'tablet';
  if (mobileKeywords.some(k => userAgent.includes(k)) || screenWidth < 768) return 'mobile';
  return 'desktop';
};

// 构建查看器URL
const buildViewerUrl = (studyId, options = {}) => {
  const {isFullscreen = false} = options;

  // 构建参数对象
  const params = {};

  // 判断是否是UID格式（包含点和数字）
  const isUidFormat = /^[0-9.]+$/.test(studyId);

  // 从配置中获取baseUrl和路径
  const baseUrl = diagnosisConfig.value.viewerBaseUrl;

  // 全屏模式使用桌面端查看器，嵌入模式使用移动端查看器
  if (isFullscreen) {
    // 全屏模式使用桌面端查看器
    const desktopPath = diagnosisConfig.value.viewerDesktopPath || '/desktop/index.html';
    const fullUrl = baseUrl + desktopPath;

    // 如果是UID格式，使用studyInstanceUid参数
    // 否则使用studyId参数
    if (isUidFormat) {
      params.studyInstanceUid = studyId;
    } else {
      params.studyId = studyId;
    }

    // 添加全屏参数
    params.fullscreen = 'true';

    // 添加模式参数（使用高级模式）
    params.mode = 'advanced';

    // 构建完整URL
    return `${fullUrl}?${new URLSearchParams(params).toString()}`;
  } else {
    // 嵌入模式使用移动端查看器（更简洁）
    const mobilePath = diagnosisConfig.value.viewerMobilePath || '/mobile/dicomViewer.html';
    const fullUrl = baseUrl + mobilePath;

    // 如果是UID格式，直接使用studyInstanceUid参数
    // 否则使用studyId参数（移动端会自动处理）
    if (isUidFormat) {
      params.studyInstanceUid = studyId;
    } else {
      params.studyId = studyId;
    }

    // 构建完整URL
    return `${fullUrl}?${new URLSearchParams(params).toString()}`;
  }
};

// 打开患者信息抽屉
const openPatientInfoDrawer = () => {
  patientInfoDrawer.value = true;
  patientInfoLoading.value = true;

  // 如果已经有study数据，直接显示
  if (study.value) {
    patientInfoLoading.value = false;
  }
};

// 处理患者信息抽屉关闭事件
const handlePatientInfoDrawerClose = () => {
  console.log('患者信息抽屉已关闭');
};

// 打印报告
const printReport = () => {
  // 获取诊断单内容
  const reportContainer = document.getElementById('printableReport');
  if (!reportContainer) {
    ElMessage.error('找不到可打印的报告内容');
    return;
  }

  // 创建新窗口进行打印
  const printWindow = window.open('', '_blank');
  if (!printWindow) {
    ElMessage.error('无法打开打印窗口，请检查浏览器设置');
    return;
  }

  // 获取当前页面的样式
  const styles = Array.from(document.styleSheets)
    .map(styleSheet => {
      try {
        return Array.from(styleSheet.cssRules)
          .map(rule => rule.cssText)
          .join('\n');
      } catch (e) {
        return '';
      }
    })
    .join('\n');

  // 构建打印页面HTML
  const printHTML = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>诊断报告</title>
      <style>
        ${styles}

        /* 打印专用样式 */
        body {
          margin: 0;
          padding: 20px;
          font-family: 'SimSun', '宋体', serif;
        }

        .report-container {
          width: 100% !important;
          min-height: auto !important;
          box-shadow: none !important;
          border: none !important;
          margin: 0 !important;
          padding: 15mm !important;
        }

        @media print {
          body { margin: 0; padding: 0; }
          .report-container { padding: 10mm !important; }
        }
      </style>
    </head>
    <body>
      ${reportContainer.outerHTML}
    </body>
    </html>
  `;

  // 写入打印窗口并打印
  printWindow.document.write(printHTML);
  printWindow.document.close();

  // 等待内容加载完成后打印
  printWindow.onload = () => {
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 500);
  };
};

// 预览报告
const previewReport = async () => {
  if (!study.value) {
    ElMessage.error('检查数据不存在');
    return;
  }

  if (!diagnosisForm.diagnose) {
    ElMessage.error('请先填写影像所见内容');
    return;
  }

  try {
    // 调用后台接口格式化报告数据
    const reportData = await formatReportData();

    // 调用Report组件的open方法
    const options = {
      filename: `${study.value.patientName || '未知患者'}_${study.value.modality || 'CT'}检查报告_${formatDateTime(new Date())}`,
      rptPath: '/report.rdlx-json', // 使用后端静态资源路径
      data: reportData
    };

    reportRef.value.open(options);
  } catch (error) {
    console.error('预览报告失败:', error);
    ElMessage.error('预览报告失败，请稍后重试');
  }
};

// 格式化报告数据
const formatReportData = async () => {
  if (!diagnosisForm.id) {
    // 如果没有诊断ID，使用前端格式化
    return formatReportDataLocal();
  }

  try {
    // 调用后台接口格式化报告数据
    const res = await apiFormatReportData(diagnosisForm.id);
    if (res.code === 200) {
      return res.data;
    } else {
      console.warn('后台格式化报告数据失败，使用前端格式化:', res.msg);
      return formatReportDataLocal();
    }
  } catch (error) {
    console.error('调用后台格式化报告数据接口失败，使用前端格式化:', error);
    return formatReportDataLocal();
  }
};

// 前端格式化报告数据（备用方案）
const formatReportDataLocal = () => {
  if (!study.value) return {};

  // 构建诊断对象（模拟后台的 diagnosis 对象）
  const diagnosis = {
    id: diagnosisForm.id,
    diagnose: diagnosisForm.diagnose || '',
    recommendation: diagnosisForm.recommendation || '',
    positiveNegative: diagnosisForm.positiveNegative || '',
    doctor: diagnosisForm.doctor || userStore.name || '',
    auditBy: diagnosisForm.auditBy || '',
    createTime: diagnosisForm.createTime,
    auditTime: diagnosisForm.auditTime,
    status: diagnosisForm.status
  };

  // 构建检查信息对象（模拟后台的 study 对象）
  const studyData = {
    ...study.value,
    // 确保年龄字段存在
    age: study.value.age || parseInt(calculateAge(study.value.patientBirthday)) || 0
  };

  // 按照后台数据结构返回
  return {
    // 基本信息（使用配置中的值）
    hospitalName: diagnosisConfig.value.hospitalName,
    reportTitle: `${study.value.modality || ''}检查报告单`,
    // 诊断对象
    diagnosis: diagnosis,
    // 检查信息对象
    study: studyData,
    qrCode: diagnosisConfig.value.qrCodeUrl .replace('{examCode}', study.value.originalPatientId) ,
    generateTime: formatDateTime(new Date())
  };
};

// 加载诊断配置
const loadDiagnosisConfig = async () => {
  try {
    const res = await getDiagnosisConfig();
    if (res.code === 200 && res.data) {
      diagnosisConfig.value = {
        ...diagnosisConfig.value,
        ...res.data
      };
      console.log('诊断配置加载成功:', diagnosisConfig.value);
    }
  } catch (error) {
    console.error('加载诊断配置失败:', error);
    // 使用默认配置，不显示错误提示
  }
};

// 生成二维码（优化版本）
const generateQRCode = async () => {
  if (!study.value || !study.value.originalPatientId) {
    return;
  }

  try {
    const originalPatientId = study.value.originalPatientId;
    // 使用配置中的二维码URL模板
    const qrCodeUrlTemplate = diagnosisConfig.value.qrCodeUrl;
    const qrCodeUrl = qrCodeUrlTemplate.replace('{examCode}', originalPatientId);

    // 先显示二维码容器
    qrCodeGenerated.value = true;

    // 等待DOM更新
    await nextTick();

    if (!qrCodeCanvas.value) {
      return;
    }

    // 使用最优化的设置快速生成
    await QRCode.toCanvas(qrCodeCanvas.value, qrCodeUrl, {
      width: 60,
      height: 60,
      margin: 0,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      },
      errorCorrectionLevel: 'L', // 最低错误纠正级别
      type: 'image/png',
      quality: 0.3, // 降低质量以提高速度
      rendererOpts: {
        quality: 0.3
      }
    });
  } catch (error) {
    console.error('生成二维码失败:', error);
    qrCodeGenerated.value = false;
  }
};

// 获取用户签名图片（返回URL字符串，保持向后兼容）
const getUserSignature = async (userName) => {
  if (!userName) {
    return '';
  }

  try {
    // 如果是当前用户，直接使用store中的头像
    if (userName === userStore.name) {
      return userStore.avatar || '';
    }

    // 对于其他用户，调用API获取用户信息
    const res = await getUserByName(userName);
    if (res.code === 200 && res.data && res.data.avatar) {
      let avatar = res.data.avatar;
      // 如果是相对路径，转换为完整URL
      if (avatar && !avatar.startsWith('http')) {
        avatar = import.meta.env.VITE_APP_BASE_API + avatar;
      }
      return avatar;
    }

    return '';
  } catch (error) {
    console.error('获取用户签名失败:', error);
    return '';
  }
};

// 获取医生详细信息（姓名和签名URL）
const getDoctorInfo = async (userName) => {
  const doctorInfo = {
    name: '',
    signatureUrl: ''
  };

  if (!userName) {
    return doctorInfo;
  }

  try {
    // 如果是当前用户，直接使用store中的信息
    if (userName === userStore.name) {
      doctorInfo.name = userStore.nickName || userStore.name || userName;
      doctorInfo.signatureUrl = userStore.avatar || '';
      return doctorInfo;
    }

    // 对于其他用户，调用API获取用户信息
    const res = await getUserByName(userName);
    if (res.code === 200 && res.data) {
      const user = res.data;
      // 优先使用昵称，其次使用用户名
      doctorInfo.name = user.nickName || user.userName || userName;

      // 处理签名图片URL
      if (user.avatar) {
        let avatar = user.avatar;
        // 如果是相对路径，转换为完整URL
        if (!avatar.startsWith('http')) {
          avatar = import.meta.env.VITE_APP_BASE_API + avatar;
        }
        doctorInfo.signatureUrl = avatar;
      }
    } else {
      // 如果找不到用户，只设置姓名
      doctorInfo.name = userName;
    }

    return doctorInfo;
  } catch (error) {
    console.error('获取医生信息失败:', error);
    // 异常情况下只设置姓名
    doctorInfo.name = userName;
    return doctorInfo;
  }
};

// 加载医生签名图片
const loadDoctorSignature = async () => {
  const doctorName = diagnosisForm.doctor || userStore.name;
  doctorSignatureUrl.value = await getUserSignature(doctorName);
  // 同时更新医生信息对象
  reportDoctorInfo.value = await getDoctorInfo(doctorName);
};

// 加载审核医生签名图片
const loadAuditSignature = async () => {
  if (diagnosisForm.auditBy) {
    auditSignatureUrl.value = await getUserSignature(diagnosisForm.auditBy);
    // 同时更新审核医生信息对象
    auditDoctorInfo.value = await getDoctorInfo(diagnosisForm.auditBy);
  } else {
    auditSignatureUrl.value = '';
    auditDoctorInfo.value = { name: '', signatureUrl: '' };
  }
};

// 处理签名图片加载错误
const handleSignatureError = (type) => {
  console.warn(`${type === 'doctor' ? '报告医生' : '审核医生'}签名图片加载失败`);
  if (type === 'doctor') {
    doctorSignatureUrl.value = '';
    reportDoctorInfo.value.signatureUrl = '';
  } else {
    auditSignatureUrl.value = '';
    auditDoctorInfo.value.signatureUrl = '';
  }
};

// 申请影像数据
const syncDicomData = async () => {
  if (!study.value || !study.value.originalPatientId) {
    ElMessage.error('无法获取患者ID，无法申请影像');
    return;
  }

  syncLoading.value = true;
  try {
    const patientId = study.value.originalPatientId;
    const res = await apiSyncDicomData(patientId, '0');
    if (res.code === 200) {
      ElMessage.success('影像申请成功，正在同步数据，请稍后刷新页面查看');

      // 更新最后同步时间
      lastSyncTime.value = parseTime(new Date());

      // 延时几秒后重新加载检查信息
      setTimeout(() => {
        loadStudy();
      }, 5000);
    } else {
      ElMessage.error(res.msg || '影像申请失败');
    }
  } catch (error) {
    console.error('申请影像出错', error);
    ElMessage.error('系统错误，请联系管理员');
  } finally {
    syncLoading.value = false;
  }
};

// 手动刷新检查数据
const refreshStudyData = () => {
  // 更新最后同步时间
  lastSyncTime.value = parseTime(new Date());

  // 重新加载检查信息
  loadStudy();

  ElMessage.success('正在刷新数据...');
};

// 加载检查信息
const loadStudy = async () => {
  if (!studyId.value) {
    // 如果没有检查ID，静默返回，不显示错误提示
    return;
  }

  // 设置加载状态
  viewerLoading.value = true;
  patientInfoLoading.value = true;

  try {
    const res = await getStudy(studyId.value);
    if (res.code === 200) {
      study.value = res.data;

      // 确保study.value已正确初始化
      if (!study.value) {
        console.error('study.value is undefined after API call');
        study.value = {}; // 初始化为空对象以防止后续错误
      }

      // 获取studyInstanceUid
      try {
        // 使用患者ID获取studyInstanceUid
        const patientId = study.value.originalPatientId || study.value.patientId;
        if (patientId) {
          console.log('正在获取患者影像数据，患者ID:', patientId);
          const dicomRes = await getStudyInstanceUid(patientId);

          // 检查代理响应
          if (dicomRes && dicomRes.code === 200 && dicomRes.data) {
            // 代理返回的数据在data属性中
            const dicomData = dicomRes.data;
            console.log('代理返回的DICOM数据:', dicomData);

            // 检查是否是数组并且有数据
            if (Array.isArray(dicomData) && dicomData.length > 0) {
              // 如果有多个结果，选择第一个
              const studyData = dicomData[0];
              console.log('获取到DICOM数据:', studyData);

              // 检查是否包含StudyInstanceUID (0020,000D)
              if (studyData && studyData['0020000D'] && studyData['0020000D'].Value && studyData['0020000D'].Value.length > 0) {
                // 保存studyInstanceUid到study对象中
                study.value.studyInstanceUid = studyData['0020000D'].Value[0];
                study.value.hasImages = true;
                console.log('成功获取studyInstanceUid:', study.value.studyInstanceUid);
              } else {
                // 没有找到有效的studyInstanceUid
                study.value.hasImages = false;
                console.log('未找到有效的studyInstanceUid，DICOM数据结构:', JSON.stringify(studyData));
              }
            } else {
              // 没有找到影像数据
              study.value.hasImages = false;
              console.log('患者没有影像数据，没有返回数组数据');
            }
          } else {
            // 代理请求失败
            study.value.hasImages = false;
            console.log('代理请求失败或返回的数据格式不正确:', dicomRes);
          }
        } else {
          // 没有患者ID
          study.value.hasImages = false;
          console.log('没有患者ID，无法获取影像数据');
        }
      } catch (dicomError) {
        console.error('获取studyInstanceUid失败:', dicomError);
        // 设置没有影像标记
        if (study.value) {
          study.value.hasImages = false;
        } else {
          console.error('study.value is undefined in catch block');
        }
      }

      // 构建影像阅读器URL
      if (study.value && study.value.hasImages === false) {
        // 如果明确没有影像数据，则不设置URL
        viewerUrl.value = '';
      } else if (study.value) {
        // 如果有影像数据或者不确定，则尝试加载
        // 使用移动端查看器（非全屏模式）
        viewerUrl.value = buildViewerUrl(study.value.studyInstanceUid || studyId.value, {isFullscreen: false});
      } else {
        console.error('study.value is undefined when building viewer URL');
        viewerUrl.value = '';
      }

      // 立即生成二维码
      generateQRCode();
    } else {
      ElMessage.error(res.msg || '加载检查记录失败');
    }
  } catch (error) {
    console.error('加载检查记录出错', error);
    ElMessage.error('系统错误，请联系管理员');
  } finally {
    // 关闭加载状态
    viewerLoading.value = false;
  }
};

// 加载历史诊断记录
const loadDiagnosisHistory = async (fromButton = false) => {
  // 如果有诊断ID，则使用诊断ID查询记录历史
  if (diagnosisId.value) {
    historyLoading.value = true;
    try {
      const res = await listRecordByDiagnosisId(diagnosisId.value);
      if (res.code === 200 && res.rows) {
        diagnosisHistory.value = res.rows;
      }
    } catch (error) {
      console.error('根据诊断ID加载历史记录出错', error);
    } finally {
      historyLoading.value = false;
    }
    return;
  }

  // 如果没有诊断ID但有检查ID，则使用原来的方式查询
  if (!studyId.value) return;

  historyLoading.value = true;
  try {
    // 查询同一检查记录的历史诊断
    const res = await listDiagnosis({
      studyId: studyId.value,
      pageSize: 10,
      pageNum: 1
    });

    if (res.code === 200 && res.rows) {
      diagnosisHistory.value = res.rows;

      // 如果有历史记录且当前是新建，并且不是通过按钮打开的，才弹出提示
      if (diagnosisHistory.value.length > 0 && !diagnosisId.value && !fromButton) {
        ElMessageBox.confirm(
            '检测到该检查记录有历史诊断，是否查看？',
            '提示',
            {
              confirmButtonText: '查看',
              cancelButtonText: '忽略',
              type: 'info',
            }
        )
            .then(() => {
              historyDrawer.value = true;
            })
            .catch(() => {
            });
      }
    }
  } catch (error) {
    console.error('加载历史诊断记录出错', error);
  } finally {
    historyLoading.value = false;
  }
};

// 从历史记录复制诊断内容
const copyFromHistory = (historyItem) => {
  ElMessageBox.confirm(
      '将使用此历史诊断覆盖当前内容，是否继续？',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
  )
      .then(() => {
        diagnosisForm.doctor = historyItem.doctor;
        diagnosisForm.diagnose = historyItem.diagnose;
        diagnosisForm.recommendation = historyItem.recommendation || '';
        diagnosisForm.remark = historyItem.remark || '';
        historyDrawer.value = false;
        ElMessage.success('已应用历史诊断');
      })
      .catch(() => {
      });
};

// 处理诊断字典选择
const handleDiagnosisSelect = (selected) => {
  if (!selected || !selected.data) return;

  // 如果是诊断项目（叶子节点），添加到诊断内容中
  if (selected.type === 'diagnosis') {
    const diagnosisText = selected.data.content || selected.data.title;

    // 如果诊断内容为空，直接设置
    if (!diagnosisForm.diagnose || diagnosisForm.diagnose.trim() === '') {
      diagnosisForm.diagnose = diagnosisText;
    } else {
      // 如果已有内容，则追加新行
      diagnosisForm.diagnose += '\n' + diagnosisText;
    }

    // 触发自动保存
    triggerAutoSave();
  }
  // 分类节点的展开/折叠由DiagnosisDictSelector组件内部处理
};

// 触发自动保存
const triggerAutoSave = () => {
  // 如果诊断已经审核或者不是创建人，不触发自动保存
  if (!canEdit.value) {
    return;
  }

  if (autoSaveTimer.value) {
    clearTimeout(autoSaveTimer.value);
  }

  autoSaveStatus.value = '编辑中...';

  autoSaveTimer.value = setTimeout(async () => {
    // 检查内容是否有变化
    const currentContent = JSON.stringify(diagnosisForm);
    if (currentContent !== lastSavedContent.value) {
      await autoSaveDiagnosis();
    } else {
      autoSaveStatus.value = '';
    }
  }, 5000); // 5秒后自动保存
};

// 自动保存诊断
const autoSaveDiagnosis = async () => {
  // 只有在表单基本填写完整时才自动保存
  if (!diagnosisForm.diagnose) {
    autoSaveStatus.value = '';
    return;
  }

  // 如果诊断已经审核或者不是创建人，不允许自动保存
  if (!canEdit.value) {
    autoSaveStatus.value = '';
    return;
  }

  autoSaveStatus.value = '正在保存...';

  try {
    // 保留原来的状态，除非是新诊断
    if (!diagnosisForm.id) {
      diagnosisForm.status = '0'; // 新诊断设置为草稿状态 (0=草稿)
    }

    const api = diagnosisForm.id ? updateDiagnosis : addDiagnosis;
    const res = await api(diagnosisForm);

    if (res.code === 200) {
      autoSaveStatus.value = '自动保存成功 ' + parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}');

      // 将响应数据合并到表单中
      Object.assign(diagnosisForm, res.data);

      // 如果是新创建的诊断，更新诊断ID
      if (res.data.id && !diagnosisId.value) {
        diagnosisId.value = res.data.id;
      }

      // 输出调试信息
      console.log('自动保存后的诊断数据:', diagnosisForm);

      // 更新最后保存的内容
      lastSavedContent.value = JSON.stringify(diagnosisForm);

      // 3秒后清除状态提示
      setTimeout(() => {
        autoSaveStatus.value = '';
      }, 3000);
    } else {
      autoSaveStatus.value = '自动保存失败';
      console.error('自动保存失败', res.msg);
    }
  } catch (error) {
    autoSaveStatus.value = '自动保存出错';
    console.error('自动保存出错', error);
  }
};

// 切换回检查列表标签页
const switchToStudyList = () => {
  // 发送事件通知父组件切换到检查列表标签页
  emit('switchToStudyList');
};

// 处理模板选择（整体应用）
const handleTemplateSelected = (template) => {
  // 检查是否有现有内容
  const hasFindings = diagnosisForm.diagnose && diagnosisForm.diagnose.trim();
  const hasOpinion = diagnosisForm.recommendation && diagnosisForm.recommendation.trim();

  if (hasFindings || hasOpinion) {
    ElMessageBox.confirm(
      '当前已有诊断内容，是否替换为模板内容？',
      '确认应用模板',
      {
        confirmButtonText: '替换',
        cancelButtonText: '追加',
        distinguishCancelAndClose: true,
        type: 'warning'
      }
    ).then(() => {
      // 用户确认替换内容
      applyTemplate(template, 'replace');
    }).catch((action) => {
      if (action === 'cancel') {
        // 用户选择追加内容
        applyTemplate(template, 'append');
      }
      // 如果是close（点击X或按ESC），不执行任何操作，也不显示消息
    });
  } else {
    // 没有现有内容，直接应用模板
    applyTemplate(template, 'replace');
  }
};

// 应用模板内容
const applyTemplate = (template, mode = 'replace') => {
  let appliedContent = [];

  if (mode === 'replace') {
    // 替换模式
    if (template.findings) {
      diagnosisForm.diagnose = template.findings;
      appliedContent.push('影像所见');
    }
    if (template.opinion) {
      diagnosisForm.recommendation = template.opinion;
      appliedContent.push('影像意见');
    }
  } else if (mode === 'append') {
    // 追加模式
    if (template.findings) {
      diagnosisForm.diagnose = (diagnosisForm.diagnose || '') +
        (diagnosisForm.diagnose ? '\n' : '') + template.findings;
      appliedContent.push('影像所见');
    }
    if (template.opinion) {
      diagnosisForm.recommendation = (diagnosisForm.recommendation || '') +
        (diagnosisForm.recommendation ? '\n' : '') + template.opinion;
      appliedContent.push('影像意见');
    }
  }

  // 触发自动保存
  triggerAutoSave();

  // 只有在真正应用了内容后才显示成功消息
  if (appliedContent.length > 0) {
    const modeText = mode === 'replace' ? '替换' : '追加';
    ElMessage.success(`模板已${modeText}应用到${appliedContent.join('和')}`);
  } else {
    ElMessage.warning('模板内容为空，未应用任何内容');
  }
};

/** 获取状态标签类型 */
const getStatusTagType = (status) => {
  switch (status) {
    case '0':
      return 'warning'; // 草稿
    case '1':
      return 'primary'; // 待审核
    case '2':
      return 'success'; // 已完成
    case '3':
      return 'danger';  // 已驳回
    case '4':
      return 'info';    // 已撤销
    case '5':
      return 'warning'; // 已修订
    case '6':
      return 'success'; // 已归档
    default:
      return 'info';
  }
};

/** 获取状态标签文本 */
const getStatusLabel = (status) => {
  switch (status) {
    case '0':
      return '草稿';
    case '1':
      return '待审核';
    case '2':
      return '已完成';
    case '3':
      return '已驳回';
    case '4':
      return '已撤销';
    case '5':
      return '已修订';
    case '6':
      return '已归档';
    default:
      return '未知状态';
  }
};

// 保存诊断（草稿）
const saveDiagnosis = async () => {
  await diagnosisFormRef.value.validate(async (valid) => {
    if (!valid) return;

    saveLoading.value = true;
    try {
      diagnosisForm.status = '0'; // 草稿状态 (0=草稿)
      diagnosisForm.studyId = studyId.value;
      diagnosisForm.checkId = studyId.value; // 确保checkId被设置

      const api = diagnosisForm.id ? updateDiagnosis : addDiagnosis;
      const res = await api(diagnosisForm);

      if (res.code === 200) {
        ElMessage.success('保存成功');
        // 检查响应数据是否存在
        if (res.data) {
          // 将响应数据合并到表单中
          Object.assign(diagnosisForm, res.data);
          if (res.data.id) {
            diagnosisId.value = res.data.id;
          }
        }

        // 输出调试信息
        console.log('保存后的诊断数据:', diagnosisForm);
        console.log('响应数据:', res);

        // 更新最后保存的内容
        lastSavedContent.value = JSON.stringify(diagnosisForm);
        autoSaveStatus.value = '';

        // 保存成功后切换回检查列表标签页
        switchToStudyList();
      } else {
        ElMessage.error(res.msg || '保存失败');
      }
    } catch (error) {
      console.error('保存诊断出错', error);
      ElMessage.error('系统错误，请联系管理员');
    } finally {
      saveLoading.value = false;
    }
  });
};

// 提交诊断
const submitDiagnosis = async () => {
  await diagnosisFormRef.value.validate(async (valid) => {
    if (!valid) return;

    submitLoading.value = true;
    try {
      diagnosisForm.status = '1'; // 待审核状态 (1=待审核)
      diagnosisForm.studyId = studyId.value;

      const api = diagnosisForm.id ? updateDiagnosis : addDiagnosis;
      const res = await api(diagnosisForm);

      if (res.code === 200) {
        ElMessage.success('提交成功');
        // 检查响应数据是否存在
        if (res.data) {
          // 将响应数据合并到表单中
          Object.assign(diagnosisForm, res.data);
          if (res.data.id) {
            diagnosisId.value = res.data.id;
          }
        }

        // 输出调试信息
        console.log('提交后的诊断数据:', diagnosisForm);
        console.log('响应数据:', res);

        // 更新最后保存的内容
        lastSavedContent.value = JSON.stringify(diagnosisForm);
        autoSaveStatus.value = '';

        // 提交后切换回检查列表标签页
        switchToStudyList();
      } else {
        ElMessage.error(res.msg || '提交失败');
      }
    } catch (error) {
      console.error('提交诊断出错', error);
      ElMessage.error('系统错误，请联系管理员');
    } finally {
      submitLoading.value = false;
    }
  });
};

// 保存并审核诊断
const auditDiagnosis = async () => {
  await diagnosisFormRef.value.validate(async (valid) => {
    if (!valid) return;

    auditLoading.value = true;
    try {
      // 如果已经有ID，则直接调用审核接口
      if (diagnosisForm.id) {
        const res = await apiAuditDiagnosis(diagnosisForm.id);
        if (res.code === 200) {
          ElMessage.success('审核成功');
          // 更新本地表单状态
          diagnosisForm.status = '2';
          diagnosisForm.auditBy = userStore.name;
          diagnosisForm.auditTime = new Date();

          // 加载审核医生签名
          await loadAuditSignature();

          // 输出调试信息
          console.log('审核后的诊断数据:', diagnosisForm);

          // 更新最后保存的内容
          lastSavedContent.value = JSON.stringify(diagnosisForm);
          autoSaveStatus.value = '';
          // 审核后切换回检查列表标签页
          switchToStudyList();
        } else {
          ElMessage.error(res.msg || '审核失败');
        }
      } else {
        // 如果是新诊断，先保存再审核
        diagnosisForm.status = '2'; // 已完成状态 (2=已完成/已审核)
        diagnosisForm.studyId = studyId.value;
        diagnosisForm.auditBy = userStore.name; // 记录审核人
        diagnosisForm.auditTime = new Date(); // 记录审核时间

        const res = await addDiagnosis(diagnosisForm);
        if (res.code === 200) {
          ElMessage.success('保存并审核成功');
          // 检查响应数据是否存在
          if (res.data) {
            Object.assign(diagnosisForm, res.data);
            if (res.data.id) {
              diagnosisId.value = res.data.id;
            }
          }

          // 加载审核医生签名
          await loadAuditSignature();

          // 输出调试信息
          console.log('保存并审核后的诊断数据:', diagnosisForm);
          console.log('响应数据:', res);

          // 更新最后保存的内容
          lastSavedContent.value = JSON.stringify(diagnosisForm);
          autoSaveStatus.value = '';
          // 审核后切换回检查列表标签页
          switchToStudyList();
        } else {
          ElMessage.error(res.msg || '保存并审核失败');
        }
      }
    } catch (error) {
      console.error('审核诊断出错', error);
      ElMessage.error('系统错误，请联系管理员');
    } finally {
      auditLoading.value = false;
    }
  });
};

// 反审核诊断
const unauditDiagnosis = async () => {
  if (!diagnosisForm.id) {
    ElMessage.error('诊断ID不存在');
    return;
  }

  ElMessageBox.confirm(
      '确定要反审核此诊断吗？反审核后诊断将回到待审核状态。',
      '反审核确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
  ).then(async () => {
    unauditLoading.value = true;
    try {
      const res = await apiUnauditDiagnosis(diagnosisForm.id);
      if (res.code === 200) {
        ElMessage.success('反审核成功');
        // 更新本地表单状态
        diagnosisForm.status = '1';
        diagnosisForm.auditBy = null;
        diagnosisForm.auditTime = null;

        // 清除审核医生签名
        auditSignatureUrl.value = '';

        // 输出调试信息
        console.log('反审核后的诊断数据:', diagnosisForm);

        // 更新最后保存的内容
        lastSavedContent.value = JSON.stringify(diagnosisForm);
        autoSaveStatus.value = '';
        // 反审核后切换回检查列表标签页
        switchToStudyList();
      } else {
        ElMessage.error(res.msg || '反审核失败');
      }
    } catch (error) {
      console.error('反审核诊断出错', error);
      ElMessage.error('系统错误，请联系管理员');
    } finally {
      unauditLoading.value = false;
    }
  }).catch(() => {
    // 用户取消操作
  });
};


// 离开前提示保存
const beforeUnloadHandler = (e) => {
  const currentContent = JSON.stringify(diagnosisForm);
  if (currentContent !== lastSavedContent.value) {
    e.preventDefault();
    e.returnValue = ''; // Chrome需要设置returnValue
    return ''; // 返回一个空字符串
  }
};

onMounted(async () => {
  // 输出当前用户信息，用于调试
  console.log('当前用户信息:', {
    id: userStore.id,
    name: userStore.name,
    username: userStore.username
  });

  // 首先加载诊断配置
  await loadDiagnosisConfig();

  // 只有当有studyId时才加载检查数据
  if (studyId.value) {
    loadStudy();
  }

  // 加载医生签名
  await loadDoctorSignature();

  // 如果有审核医生，加载审核医生签名
  if (diagnosisForm.auditBy) {
    await loadAuditSignature();
  }

  // 添加页面离开前提示
  window.addEventListener('beforeunload', beforeUnloadHandler);
});

onBeforeUnmount(() => {
  // 清除自动保存定时器
  if (autoSaveTimer.value) {
    clearTimeout(autoSaveTimer.value);
  }

  // 移除页面离开前提示
  window.removeEventListener('beforeunload', beforeUnloadHandler);
});

// 暴露方法给父组件调用
defineExpose({
  handleStudyData
});
</script>

<style scoped>
.diagnosis-editor {
  height: calc(100vh - 80px);
  overflow: visible;
}

.viewer-container, .diagnosis-form-container {
  height: 98vh;
  display: flex;
  flex-direction: column;
  overflow: visible;
}

.viewer-header, .diagnosis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3px;
  padding: 4px 8px;
  background-color: #f5f7fa;
  border-radius: 3px;
  flex-shrink: 0;
  min-height: 36px;
}

.viewer-header {
  flex-direction: row;
}

.viewer-header h4, .diagnosis-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.action-buttons {
  display: flex;
  gap: 6px;
  align-items: center;
}

.action-buttons .el-button {
  padding: 4px 8px;
  font-size: 12px;
}

.viewer-content {
  flex: 1;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  overflow: hidden;
  min-height: 600px;
}

.empty-viewer {
  height: 100%;
  min-height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-viewer-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
  justify-content: center;
}

.full-height-row {
  height: 100%;
}

.sync-time-info {
  font-size: 12px;
  color: #909399;
  margin-top: 10px;
  text-align: center;
}

/* 为底部按钮留出空间 */
.diagnosis-editor {
  padding-bottom: 80px;
  height: calc(100vh - 80px);
  overflow-y: auto;
  overflow-x: hidden;
}

/* 报告外层容器 */
.report-wrapper {
  display: flex;
  justify-content: center;
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 120px);
}

/* 报告样式容器 - 模拟A4纸张 */
.report-container {
  width: 210mm; /* A4纸宽度 */
  min-height: auto; /* 自动高度，不限制最小高度 */
  background-color: #fff;
  padding: 15mm; /* 减少页边距 */
  margin: 0 auto 20px auto; /* 底部留出空间 */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  font-family: 'SimSun', '宋体', serif; /* 使用宋体字体 */
  font-size: 12pt;
  line-height: 1.5;
}

/* 报告头部样式 */
.report-header {
  margin-bottom: 15px;
  position: relative;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
  min-height: 60px;
}

.hospital-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.exam-number {
  font-size: 11pt;
  color: #000;
  margin-top: 6px;
  font-family: 'SimSun', '宋体', serif;
}

.hospital-logo {
  width: 50px;
  height: 50px;
  margin-bottom: 5px;
}

.hospital-logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.hospital-name {
  font-size: 18pt;
  font-weight: bold;
  color: #000;
  margin-bottom: 3px;
  letter-spacing: 1px;
  line-height: 1.2;
}

.report-title {
  font-size: 16pt;
  font-weight: bold;
  color: #000;
  margin-bottom: 6px;
  letter-spacing: 0.5px;
  line-height: 1.2;
}

.report-divider {
  height: 1px;
  background-color: #000;
  margin: 8px 0;
}

/* 二维码样式 */
.qr-code-container {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 20px;
}

.qr-code-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 6px;
  border: 1px solid #000;
  border-radius: 2px;
  background-color: #fff;
}

.qr-code-canvas {
  border: none;
  margin-bottom: 2px;
  display: block;
}

.qr-code-label {
  font-size: 8pt;
  color: #000;
  text-align: center;
  white-space: nowrap;
  font-family: 'SimSun', '宋体', serif;
  line-height: 1;
}

/* 患者信息网格样式 */
.patient-info-grid {
  margin-bottom: 15px;
  border: 1px solid #000;
  width: 100%;
}

.info-row {
  display: flex;
  border-bottom: 1px solid #000;
}

.info-row:last-child {
  border-bottom: none;
}

.info-item {
  flex: 1;
  width: 25%;
  padding: 6px 8px;
  display: flex;
  border-right: 1px solid #000;
  font-size: 10pt;
  min-height: 28px;
  align-items: center;
}

.info-item:last-child {
  border-right: none;
}

.info-item-wide {
  flex: 1.5;
  width: 37.5%;
}

.info-label {
  font-size: 10pt;
  font-weight: bold;
  color: #000;
  white-space: nowrap;
  font-family: 'SimSun', '宋体', serif;
  min-width: 50px;
  margin-right: 6px;
  flex-shrink: 0;
}

.info-label::after {
  content: '：';
}

.info-value {
  color: #000;
  font-family: 'SimSun', '宋体', serif;
  flex: 1;
  word-break: break-all;
  font-size: 10pt;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.patient-info-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 5px;
  margin-bottom: 10px;
}

/* 诊断内容区域样式 */
.diagnosis-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
  margin-bottom: 5px;
}

.tree-container {
  height: 30vh;
  overflow: auto;
  padding: 5px;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  background-color: #f9f9f9;
  margin-bottom: 5px;
}

.tree-container h4 {
  margin-top: 0;
  margin-bottom: 5px;
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.editor-container {
  flex: 1;
  overflow: auto;
  padding: 5px 0;
  display: flex;
  flex-direction: column;
}

.editor-container :deep(.el-form-item) {
  margin-bottom: 10px;
}

.editor-container :deep(.el-textarea__inner) {
  min-height: 12vh;
}

/* 报告内容区域样式 */
.report-section {
  margin-bottom: 20px;
  flex-shrink: 0;
}

.section-header {
  font-size: 14pt;
  font-weight: bold;
  color: #000;
  margin-bottom: 10px;
  padding-left: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.template-selector-btn {
  margin-left: 10px;
  border-left: none;
  font-family: 'SimSun', '宋体', serif;
}

.report-content-item {
  margin-bottom: 15px;
}

.report-textarea :deep(.el-textarea__inner) {
  background-color: #fff;
  border: 1px solid #000;
  padding: 10px;
  font-size: 12pt;
  line-height: 1.8;
  max-height: none;
  font-family: 'SimSun', '宋体', serif;
  color: #000;
  min-height: 80px;
}

/* 签名区域样式 */
.report-signature {
  margin-top: 40px;
  border-top: 1px solid #000;
  padding-top: 20px;
}

.signature-row {
  display: flex;
  margin-bottom: 15px;
  justify-content: space-between;
}

.signature-item {
  flex: 1;
  display: flex;
  align-items: center;
  font-size: 12pt;
}

.signature-label {
  font-weight: bold;
  color: #000;
  margin-right: 10px;
  font-family: 'SimSun', '宋体', serif;
}

.signature-value {
  color: #000;
  font-family: 'SimSun', '宋体', serif;
  border-bottom: 1px solid #000;
  min-width: 100px;
  padding-bottom: 2px;
}

.signature-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.signature-name {
  color: #000;
  font-size: 10pt;
  line-height: 1.2;
  font-family: 'SimSun', '宋体', serif;
}

.signature-image-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 30px;
  max-height: 40px;
}

.signature-image {
  max-width: 80px;
  max-height: 40px;
  height: auto;
  width: auto;
  object-fit: contain;
  border: 1px solid #ddd;
  border-radius: 2px;
  background-color: #fff;
}

.audit-notice, .creator-notice {
  margin-bottom: 15px;
}

/* 自动保存提示样式 */
.auto-save-container {
  padding: 2px 10px;
  margin-bottom: 5px;
  flex-shrink: 0;
  position: fixed;
  top: 50px;
  right: 20px;
  z-index: 999;
}

.auto-save-indicator {
  font-size: 12px;
  text-align: right;
}

/* 底部按钮区域样式 */
.bottom-actions {
  display: flex;
  justify-content: flex-end;
  padding: 8px 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.content-editor-wrapper {
  position: relative;
}

.quick-text-panel {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.quick-text-item {
  cursor: pointer;
  user-select: none;
}

.quick-text-item:hover {
  background-color: #ecf5ff;
}


.history-item {
  margin-bottom: 16px;
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fff;
}

.history-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.history-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  flex-wrap: wrap;
  gap: 8px;
}

.history-title {
  font-weight: bold;
  margin-right: 12px;
}

.history-date, .history-doctor, .history-audit {
  font-size: 12px;
  color: #909399;
  margin-right: 12px;
}

.history-audit {
  color: #67c23a;
  font-weight: 500;
  display: block;
  margin-top: 4px;
}

.history-content {
  margin-top: 8px;
}

.history-section {
  margin-bottom: 8px;
}

.section-title {
  font-weight: bold;
  margin-bottom: 4px;
  color: #606266;
}

.section-content {
  white-space: pre-wrap;
  line-height: 1.5;
  color: #303133;
  background-color: #f8f8f8;
  padding: 8px;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
}

.history-actions {
  margin-top: 8px;
  display: flex;
  justify-content: flex-end;
}

/* 全屏查看器样式 */
.fullscreen-viewer-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.fullscreen-viewer-content {
  flex: 1;
  overflow: hidden;
}

.fullscreen-viewer-content iframe {
  width: 100%;
  height: 100%;
  border: none;
}

/* 调整全屏对话框样式 */
.el-dialog.is-fullscreen {
  margin: 0 !important;
  padding: 0 !important;
}

.el-dialog.is-fullscreen .el-dialog__header {
  padding: 10px 20px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e6e6e6;
}

.el-dialog.is-fullscreen .el-dialog__body {
  padding: 0;
  height: calc(100vh - 55px);
}

/* 阴阳性结果样式 */
.positive-negative-group {
  display: flex;
  gap: 20px;
  align-items: center;
  padding: 10px 0;
}

.positive-negative-group .el-radio {
  margin-right: 0;
  padding: 8px 16px;
  border-radius: 6px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  background-color: #f8f9fa;
}

.positive-negative-group .el-radio:hover {
  background-color: #e9ecef;
}

.positive-negative-group .el-radio.is-checked {
  font-weight: 600;
}

.positive-radio.is-checked {
  background-color: #fff2f0;
  border-color: #ff4d4f;
  color: #ff4d4f;
}

.negative-radio.is-checked {
  background-color: #f6ffed;
  border-color: #52c41a;
  color: #52c41a;
}

.uncertain-radio.is-checked {
  background-color: #fff7e6;
  border-color: #fa8c16;
  color: #fa8c16;
}

.positive-icon {
  color: #ff4d4f;
  margin-right: 4px;
}

.negative-icon {
  color: #52c41a;
  margin-right: 4px;
}

.uncertain-icon {
  color: #fa8c16;
  margin-right: 4px;
}

/* 打印样式优化 */
@media print {
  body {
    margin: 0;
    padding: 0;
    font-family: 'SimSun', '宋体', serif;
  }

  .report-container {
    width: 100% !important;
    min-height: auto !important;
    box-shadow: none !important;
    border: none !important;
    margin: 0 !important;
    padding: 10mm !important;
  }

  .qr-code-wrapper {
    border: 1px solid #000 !important;
    background: white !important;
  }

  /* 确保文字清晰 */
  * {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  /* 分页控制 */
  .report-container {
    page-break-inside: avoid;
  }

  .report-section {
    page-break-inside: avoid;
  }
}
</style>