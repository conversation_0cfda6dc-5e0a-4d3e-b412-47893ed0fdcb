<template>
  <div class="simple-test">
    <h1>诊断工作台测试页面</h1>
    <p>如果您能看到这个页面，说明路由配置正常。</p>
    
    <el-card>
      <template #header>
        <span>路由测试</span>
      </template>
      <div>
        <p>当前路由: {{ $route.path }}</p>
        <p>当前时间: {{ currentTime }}</p>
        <el-button type="primary" @click="testApi">测试API</el-button>
        <el-button @click="goToWorkspace">前往工作台</el-button>
      </div>
    </el-card>

    <el-card style="margin-top: 20px;">
      <template #header>
        <span>API测试结果</span>
      </template>
      <div>
        <pre>{{ apiResult }}</pre>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()
const currentTime = ref('')
const apiResult = ref('点击"测试API"按钮查看结果')

// 更新时间
const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

// 测试API
const testApi = async () => {
  try {
    apiResult.value = '正在测试API...'
    
    // 测试一个简单的API
    const response = await fetch('/dev-api/getInfo', {
      headers: {
        'Authorization': 'Bearer ' + localStorage.getItem('token')
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      apiResult.value = JSON.stringify(data, null, 2)
      ElMessage.success('API测试成功')
    } else {
      apiResult.value = `API测试失败: ${response.status} ${response.statusText}`
      ElMessage.error('API测试失败')
    }
  } catch (error) {
    apiResult.value = `API测试错误: ${error.message}`
    ElMessage.error('API测试错误')
  }
}

// 前往工作台
const goToWorkspace = () => {
  router.push('/diagnosis/workspace')
}

onMounted(() => {
  updateTime()
  setInterval(updateTime, 1000)
})
</script>

<style scoped>
.simple-test {
  padding: 20px;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}
</style>
