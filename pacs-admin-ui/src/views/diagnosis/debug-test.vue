<template>
  <div class="debug-test">
    <h2>诊断状态参数调试测试</h2>
    
    <el-card>
      <template #header>
        <span>当前参数状态</span>
      </template>
      
      <div class="debug-info">
        <h3>queryParams:</h3>
        <pre>{{ JSON.stringify(queryParams, null, 2) }}</pre>
        
        <h3>diagnosisStatus值:</h3>
        <p>值: {{ queryParams.diagnosisStatus }}</p>
        <p>类型: {{ typeof queryParams.diagnosisStatus }}</p>
        <p>是否为undefined: {{ queryParams.diagnosisStatus === undefined }}</p>
        <p>是否为空字符串: {{ queryParams.diagnosisStatus === '' }}</p>
      </div>
    </el-card>

    <el-card style="margin-top: 20px;">
      <template #header>
        <span>直接测试诊断状态选择器</span>
      </template>
      
      <div class="direct-test">
        <el-form-item label="诊断状态:">
          <el-select 
            v-model="queryParams.diagnosisStatus" 
            placeholder="请选择诊断状态"
            @change="handleDirectChange"
            style="width: 200px"
          >
            <el-option label="全部" value="" />
            <el-option label="待诊断" value="-1" />
            <el-option label="已诊断" value="1" />
            <el-option label="已审核" value="2" />
            <el-option label="院内诊断" value="9" />
          </el-select>
        </el-form-item>
        
        <el-button type="primary" @click="testApiCall">测试API调用</el-button>
      </div>
    </el-card>

    <el-card style="margin-top: 20px;">
      <template #header>
        <span>API调用结果</span>
      </template>
      
      <div class="api-result">
        <pre>{{ apiResult }}</pre>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { listWithDiagnose } from '@/api/pacs/study'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  diagnosisStatus: undefined
})

const apiResult = ref('点击"测试API调用"查看结果')

// 监听diagnosisStatus变化
watch(() => queryParams.diagnosisStatus, (newVal, oldVal) => {
  console.log('🎯 直接测试 - diagnosisStatus变化:', { newVal, oldVal, type: typeof newVal })
})

// 处理直接变化
const handleDirectChange = (value) => {
  console.log('🔥 直接选择器变化:', value, '类型:', typeof value)
  console.log('🔥 当前queryParams:', JSON.stringify(queryParams, null, 2))
}

// 测试API调用
const testApiCall = async () => {
  try {
    apiResult.value = '正在调用API...'
    
    // 构建请求参数
    const params = {}
    Object.keys(queryParams).forEach(key => {
      const value = queryParams[key]
      if (key === 'diagnosisStatus') {
        if (value !== undefined && value !== null) {
          params[key] = value
        }
      } else {
        if (value !== undefined && value !== null && value !== '') {
          params[key] = value
        }
      }
    })
    
    console.log('🚀 测试API调用参数:', params)
    
    const response = await listWithDiagnose(params)
    
    apiResult.value = JSON.stringify({
      requestParams: params,
      responseCode: response.code,
      responseMsg: response.msg,
      total: response.total,
      hasData: !!response.rows?.length
    }, null, 2)
    
  } catch (error) {
    apiResult.value = `API调用错误: ${error.message}`
    console.error('API调用错误:', error)
  }
}
</script>

<style scoped>
.debug-test {
  padding: 20px;
}

.debug-info pre,
.api-result pre {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
  max-height: 300px;
  overflow-y: auto;
}

.direct-test {
  display: flex;
  align-items: center;
  gap: 20px;
}
</style>
