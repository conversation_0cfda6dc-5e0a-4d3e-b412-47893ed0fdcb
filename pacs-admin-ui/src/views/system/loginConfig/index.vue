<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          size="small"
          @click="handleBatchEdit"
          v-hasPermi="['system:config:edit']"
        >批量编辑</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          size="small"
          @click="handleExport"
          v-hasPermi="['system:config:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="configList">
      <el-table-column label="配置项" align="center" prop="configName" />
      <el-table-column label="配置键" align="center" prop="configKey" :show-overflow-tooltip="true" />
      <el-table-column label="配置值" align="center" prop="configValue" :show-overflow-tooltip="true" />
      <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            type="text"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:config:edit']"
          >修改</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="configRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="参数名称" prop="configName">
          <el-input v-model="form.configName" placeholder="请输入参数名称" :disabled="true" />
        </el-form-item>
        <el-form-item label="参数键名" prop="configKey">
          <el-input v-model="form.configKey" placeholder="请输入参数键名" :disabled="true" />
        </el-form-item>
        <el-form-item label="参数键值" prop="configValue">
          <el-input v-model="form.configValue" type="textarea" :rows="3" placeholder="请输入参数键值" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量编辑对话框 -->
    <el-dialog title="批量编辑登录配置" v-model="batchEditOpen" width="800px" append-to-body>
      <el-form ref="batchFormRef" :model="batchForm" label-width="120px">
        <el-form-item label="医院名称">
          <el-input v-model="batchForm.hospitalName" placeholder="请输入医院名称" />
        </el-form-item>
        <el-form-item label="系统名称">
          <el-input v-model="batchForm.systemName" placeholder="请输入系统名称" />
        </el-form-item>
        <el-form-item label="欢迎文字">
          <el-input v-model="batchForm.welcomeText" placeholder="请输入欢迎文字" />
        </el-form-item>
        <el-form-item label="副标题">
          <el-input v-model="batchForm.subTitle" type="textarea" :rows="2" placeholder="请输入副标题" />
        </el-form-item>
        <el-form-item label="底部说明">
          <el-input v-model="batchForm.footerText" type="textarea" :rows="2" placeholder="请输入底部说明文字" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitBatchForm">确 定</el-button>
          <el-button @click="cancelBatch">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="LoginConfig">
import { listConfig, getConfig, updateConfig } from "@/api/system/config";

const { proxy } = getCurrentInstance();
const { parseTime } = proxy.useParseTime;

const configList = ref([]);
const open = ref(false);
const batchEditOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref("");

const data = reactive({
  form: {},
  batchForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    configName: undefined,
    configKey: undefined,
    configType: undefined
  },
  rules: {
    configName: [
      { required: true, message: "参数名称不能为空", trigger: "blur" }
    ],
    configKey: [
      { required: true, message: "参数键名不能为空", trigger: "blur" }
    ],
    configValue: [
      { required: true, message: "参数键值不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, batchForm, rules } = toRefs(data);

/** 查询参数列表 */
function getList() {
  loading.value = true;
  // 只查询登录相关配置
  const params = {
    ...queryParams.value,
    configKey: 'mobile.login'
  };
  listConfig(params).then(response => {
    // 过滤只显示登录相关配置
    configList.value = response.rows.filter(item => 
      item.configKey && item.configKey.startsWith('mobile.login.')
    );
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 取消批量编辑 */
function cancelBatch() {
  batchEditOpen.value = false;
  resetBatchForm();
}

/** 表单重置 */
function reset() {
  form.value = {
    configId: undefined,
    configName: undefined,
    configKey: undefined,
    configValue: undefined,
    configType: "N",
    remark: undefined
  };
  proxy.resetForm("configRef");
}

/** 批量表单重置 */
function resetBatchForm() {
  batchForm.value = {
    hospitalName: '',
    systemName: '',
    welcomeText: '',
    subTitle: '',
    footerText: ''
  };
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const configId = row.configId || row.configId;
  getConfig(configId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改参数";
  });
}

/** 批量编辑按钮操作 */
function handleBatchEdit() {
  resetBatchForm();
  // 加载当前配置值
  configList.value.forEach(item => {
    if (item.configKey === 'mobile.login.hospitalName') {
      batchForm.value.hospitalName = item.configValue;
    } else if (item.configKey === 'mobile.login.systemName') {
      batchForm.value.systemName = item.configValue;
    } else if (item.configKey === 'mobile.login.welcomeText') {
      batchForm.value.welcomeText = item.configValue;
    } else if (item.configKey === 'mobile.login.subTitle') {
      batchForm.value.subTitle = item.configValue;
    } else if (item.configKey === 'mobile.login.footerText') {
      batchForm.value.footerText = item.configValue;
    }
  });
  batchEditOpen.value = true;
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["configRef"].validate(valid => {
    if (valid) {
      if (form.value.configId != undefined) {
        updateConfig(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 批量提交 */
function submitBatchForm() {
  const updates = [];
  
  configList.value.forEach(item => {
    let newValue = '';
    if (item.configKey === 'mobile.login.hospitalName') {
      newValue = batchForm.value.hospitalName;
    } else if (item.configKey === 'mobile.login.systemName') {
      newValue = batchForm.value.systemName;
    } else if (item.configKey === 'mobile.login.welcomeText') {
      newValue = batchForm.value.welcomeText;
    } else if (item.configKey === 'mobile.login.subTitle') {
      newValue = batchForm.value.subTitle;
    } else if (item.configKey === 'mobile.login.footerText') {
      newValue = batchForm.value.footerText;
    }
    
    if (newValue !== item.configValue) {
      updates.push(updateConfig({
        ...item,
        configValue: newValue
      }));
    }
  });
  
  if (updates.length > 0) {
    Promise.all(updates).then(() => {
      proxy.$modal.msgSuccess("批量更新成功");
      batchEditOpen.value = false;
      getList();
    }).catch(() => {
      proxy.$modal.msgError("批量更新失败");
    });
  } else {
    proxy.$modal.msgInfo("没有需要更新的配置");
    batchEditOpen.value = false;
  }
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/config/export', {
    ...queryParams.value
  }, `config_${new Date().getTime()}.xlsx`)
}

getList();
</script>
