<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>MinIO存储配置</span>
          <div class="header-buttons">
            <el-button type="primary" @click="saveConfig" :loading="saveLoading">
              <el-icon><Check /></el-icon>
              保存配置
            </el-button>
            <el-button type="success" @click="testConnection" :loading="testLoading">
              <el-icon><Connection /></el-icon>
              测试连接
            </el-button>
            <el-button type="warning" @click="resetConfig" :loading="resetLoading">
              <el-icon><RefreshLeft /></el-icon>
              重置默认
            </el-button>
          </div>
        </div>
      </template>

      <el-form :model="configForm" :rules="rules" ref="configFormRef" label-width="150px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="服务器地址" prop="endpoint">
              <el-input v-model="configForm.endpoint" placeholder="请输入MinIO服务器地址" />
              <div class="form-tip">例如：http://localhost:9000</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="存储桶名称" prop="bucketName">
              <el-input v-model="configForm.bucketName" placeholder="请输入存储桶名称" />
              <div class="form-tip">用于存储PDF报告的存储桶</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="访问密钥" prop="accessKey">
              <el-input v-model="configForm.accessKey" placeholder="请输入访问密钥" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="秘密密钥" prop="secretKey">
              <el-input 
                v-model="configForm.secretKey" 
                type="password" 
                placeholder="请输入秘密密钥"
                show-password
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="文件访问URL前缀" prop="urlPrefix">
              <el-input v-model="configForm.urlPrefix" placeholder="请输入文件访问URL前缀" />
              <div class="form-tip">例如：http://localhost:9000/pacs-reports/</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 连接测试结果 -->
      <el-card v-if="testResult" class="test-result-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><DataAnalysis /></el-icon>
            <span>连接测试结果</span>
          </div>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="服务器地址">
            {{ testResult.endpoint }}
          </el-descriptions-item>
          
          <el-descriptions-item label="存储桶名称">
            {{ testResult.bucketName }}
          </el-descriptions-item>
          
          <el-descriptions-item label="连接状态" :span="2">
            <el-tag :type="testResult.connectionSuccess ? 'success' : 'danger'">
              {{ testResult.connectionSuccess ? '连接成功' : '连接失败' }}
            </el-tag>
            <span style="margin-left: 10px;">{{ testResult.message }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 存储统计信息 -->
      <el-card class="statistics-card" shadow="hover" style="margin-top: 20px;">
        <template #header>
          <div class="card-header">
            <el-icon><PieChart /></el-icon>
            <span>存储统计</span>
            <el-button 
              type="text" 
              @click="loadStatistics"
              :loading="statisticsLoading"
              style="float: right;"
            >
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number">{{ statistics.status || '未知' }}</div>
              <div class="stat-label">服务状态</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number">{{ statistics.bucketName || '-' }}</div>
              <div class="stat-label">当前存储桶</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number">{{ statistics.endpoint || '-' }}</div>
              <div class="stat-label">服务器地址</div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Check, 
  Connection, 
  RefreshLeft, 
  DataAnalysis, 
  PieChart, 
  Refresh 
} from '@element-plus/icons-vue'
import request from '@/utils/request'

// 响应式数据
const configFormRef = ref()
const saveLoading = ref(false)
const testLoading = ref(false)
const resetLoading = ref(false)
const statisticsLoading = ref(false)
const testResult = ref(null)
const statistics = ref({})

const configForm = ref({
  endpoint: '',
  accessKey: '',
  secretKey: '',
  bucketName: '',
  urlPrefix: ''
})

// 表单验证规则
const rules = {
  endpoint: [
    { required: true, message: '请输入服务器地址', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
  ],
  accessKey: [
    { required: true, message: '请输入访问密钥', trigger: 'blur' }
  ],
  secretKey: [
    { required: true, message: '请输入秘密密钥', trigger: 'blur' }
  ],
  bucketName: [
    { required: true, message: '请输入存储桶名称', trigger: 'blur' },
    { pattern: /^[a-z0-9][a-z0-9.-]*[a-z0-9]$/, message: '存储桶名称格式不正确', trigger: 'blur' }
  ],
  urlPrefix: [
    { required: true, message: '请输入文件访问URL前缀', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
  ]
}

// 加载配置
const loadConfig = async () => {
  try {
    const response = await request({
      url: '/minio/config/list',
      method: 'get'
    })
    
    if (response.code === 200) {
      Object.assign(configForm.value, response.data)
    }
  } catch (error) {
    ElMessage.error('加载配置失败: ' + (error.message || error))
  }
}

// 保存配置
const saveConfig = async () => {
  try {
    await configFormRef.value.validate()
    
    saveLoading.value = true
    
    const response = await request({
      url: '/minio/config/update',
      method: 'put',
      data: configForm.value
    })
    
    if (response.code === 200) {
      ElMessage.success('配置保存成功')
      testResult.value = null // 清除测试结果
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('保存配置失败: ' + (error.message || error))
    }
  } finally {
    saveLoading.value = false
  }
}

// 测试连接
const testConnection = async () => {
  try {
    testLoading.value = true
    
    const response = await request({
      url: '/minio/config/test',
      method: 'post'
    })
    
    if (response.code === 200) {
      testResult.value = response.data
      if (response.data.connectionSuccess) {
        ElMessage.success('连接测试成功')
      } else {
        ElMessage.warning('连接测试失败')
      }
    }
  } catch (error) {
    ElMessage.error('测试连接失败: ' + (error.message || error))
  } finally {
    testLoading.value = false
  }
}

// 重置配置
const resetConfig = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置所有配置为默认值吗？此操作不可撤销。',
      '确认重置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    resetLoading.value = true
    
    const response = await request({
      url: '/minio/config/reset',
      method: 'post'
    })
    
    if (response.code === 200) {
      ElMessage.success('配置已重置为默认值')
      await loadConfig() // 重新加载配置
      testResult.value = null // 清除测试结果
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重置配置失败: ' + (error.message || error))
    }
  } finally {
    resetLoading.value = false
  }
}

// 加载统计信息
const loadStatistics = async () => {
  try {
    statisticsLoading.value = true
    
    const response = await request({
      url: '/minio/config/statistics',
      method: 'get'
    })
    
    if (response.code === 200) {
      statistics.value = response.data
    }
  } catch (error) {
    ElMessage.error('获取统计信息失败: ' + (error.message || error))
  } finally {
    statisticsLoading.value = false
  }
}

// 组件挂载时加载配置和统计信息
onMounted(() => {
  loadConfig()
  loadStatistics()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.header-buttons {
  display: flex;
  gap: 10px;
}

.test-result-card, .statistics-card {
  margin-top: 20px;
}

.test-result-card .card-header,
.statistics-card .card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.stat-item {
  text-align: center;
  padding: 20px 0;
}

.stat-number {
  font-size: 18px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
  word-break: break-all;
}

.stat-label {
  font-size: 14px;
  color: #666;
}
</style>
