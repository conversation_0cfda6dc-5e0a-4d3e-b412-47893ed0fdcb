<template>
  <div class=" home-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="breadcrumb">
            <span class="breadcrumb-item">工作台</span>
          </div>
        </div>
        <div class="header-right">
          <div class="header-actions">
            <el-button type="primary" size="small">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
          <div class="date-info">{{ currentDate }}</div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧内容区域 -->
      <div class="left-content">
        <!-- 会诊消息列表 -->
        <div class="message-section">
          <!-- 融合的标题和标签页 -->
          <div class="integrated-header">
            <div class="header-left">
              <el-icon class="title-icon"><ChatDotRound /></el-icon>
              <div class="message-tabs">
                <div class="message-tab" :class="{ active: activeMessageTab === 'invited' }" @click="activeMessageTab = 'invited'">
                  <span>受邀</span>
                  <el-badge :value="invitedMessages.length" :hidden="invitedMessages.length === 0" class="tab-badge" />
                </div>
                <div class="message-tab" :class="{ active: activeMessageTab === 'myRequests' }" @click="activeMessageTab = 'myRequests'">
                  <span>我的申请</span>
                  <el-badge :value="myRequestMessages.length" :hidden="myRequestMessages.length === 0" class="tab-badge" />
                </div>
              </div>
            </div>
            <div class="header-right">
<!--              <el-badge :value="totalUnreadCount" :hidden="totalUnreadCount === 0" class="unread-badge">
                <span class="total-count">({{ totalMessageCount }})</span>
              </el-badge>-->
              <el-button text type="primary" size="small" @click="refreshMessages">
                <el-icon><Refresh /></el-icon>
              </el-button>
              <el-button text type="primary" size="small" @click="viewAllMessages">
                <el-icon><More /></el-icon>
              </el-button>
            </div>
          </div>

          <!-- 受邀的会诊消息 -->
          <div v-show="activeMessageTab === 'invited'" class="message-list" v-loading="messageLoading">
            <div v-for="message in invitedMessages" :key="message.id"
                 class="message-item compact" @click="handleMessageClick(message)">
              <div class="message-icon">
                <el-icon :class="getMessageIconClass(message.status)">
                  <component :is="getMessageIcon(message.status)" />
                </el-icon>
              </div>
              <div class="message-content">
                <div class="message-title">{{ getInvitedMessageTitle(message) }}</div>
                <div class="message-meta">
                  <span class="message-from">{{ message.requesterName }}</span>
                  <span class="message-time">{{ formatTime(message.requestTime) }}</span>
                  <el-tag :type="getMessageStatusType(message.status)" size="small" class="status-tag">
                    {{ getMessageStatusText(message.status) }}
                  </el-tag>
                </div>
              </div>
              <div class="message-unread" v-if="isUnreadMessage(message)">
                <el-badge is-dot />
              </div>
            </div>

            <div v-if="invitedMessages.length === 0 && !messageLoading" class="empty-state compact">
              <el-empty description="暂无受邀消息" :image-size="40" />
            </div>
          </div>

          <!-- 我的申请消息 -->
          <div v-show="activeMessageTab === 'myRequests'" class="message-list" v-loading="messageLoading">
            <div v-for="message in myRequestMessages" :key="message.id"
                 class="message-item compact" @click="handleMessageClick(message)">
              <div class="message-icon">
                <el-icon :class="getMessageIconClass(message.status)">
                  <component :is="getMessageIcon(message.status)" />
                </el-icon>
              </div>
              <div class="message-content">
                <div class="message-title">{{ getMyRequestMessageTitle(message) }}</div>
                <div class="message-meta">
                  <span class="message-from">{{ message.consultantName }}</span>
                  <span class="message-time">{{ formatTime(message.responseTime || message.requestTime) }}</span>
                  <el-tag :type="getMessageStatusType(message.status)" size="small" class="status-tag">
                    {{ getMessageStatusText(message.status) }}
                  </el-tag>
                </div>
              </div>
              <div class="message-unread" v-if="isUnreadMessage(message)">
                <el-badge is-dot />
              </div>
            </div>

            <div v-if="myRequestMessages.length === 0 && !messageLoading" class="empty-state compact">
              <el-empty description="暂无申请消息" :image-size="40" />
            </div>
          </div>
        </div>

        <!-- 诊断统计卡片 -->
        <div class="diagnosis-section">
          <div class="section-card" v-loading="loading">
            <div class="card-header">
              <div class="card-title">
                <el-icon class="title-icon"><Monitor /></el-icon>
                <span>诊断统计</span>
              </div>
              <a class="card-link" @click="router.push('/diagnosis/workspace')">查看更多</a>
            </div>
            <div class="diagnosis-stats">
              <div class="diagnosis-item" @click="handleStatClick('diagnosis-pending')">
                <div class="diagnosis-number pending">{{ diagnosisStats.pendingCount || 0 }}</div>
                <div class="diagnosis-label">待诊断</div>
              </div>
              <div class="diagnosis-item" @click="handleStatClick('diagnosis-diagnosed')">
                <div class="diagnosis-number diagnosed">{{ diagnosisStats.diagnosedCount || 0 }}</div>
                <div class="diagnosis-label">已诊断</div>
              </div>
              <div class="diagnosis-item" @click="handleStatClick('diagnosis-audited')">
                <div class="diagnosis-number audited">{{ diagnosisStats.auditedCount || 0 }}</div>
                <div class="diagnosis-label">已审核</div>
              </div>
              <div class="diagnosis-item" @click="handleStatClick('diagnosis-archived')">
                <div class="diagnosis-number archived">{{ diagnosisStats.archivedCount || 0 }}</div>
                <div class="diagnosis-label">院内诊断</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 会诊统计卡片 -->
        <div class="consultation-section">
          <div class="section-card" v-loading="loading">
            <div class="card-header">
              <div class="card-title">
                <el-icon class="title-icon"><ChatDotRound /></el-icon>
                <span>会诊统计</span>
              </div>
              <a class="card-link" @click="router.push('/consultation/my-consultation')">查看更多</a>
            </div>
            <div class="consultation-stats">
              <div class="consultation-item" @click="handleStatClick('consultation-pending')">
                <div class="consultation-number pending">{{ consultationStats.pendingCount || 0 }}</div>
                <div class="consultation-label">待处理</div>
              </div>
              <div class="consultation-item" @click="handleStatClick('consultation-accepted')">
                <div class="consultation-number accepted">{{ consultationStats.acceptedCount || 0 }}</div>
                <div class="consultation-label">已接受</div>
              </div>
              <div class="consultation-item" @click="handleStatClick('consultation-completed')">
                <div class="consultation-number completed">{{ consultationStats.completedCount || 0 }}</div>
                <div class="consultation-label">已完成</div>
              </div>
              <div class="consultation-item" @click="handleStatClick('consultation-total')">
                <div class="consultation-number total">{{ consultationStats.totalCount || 0 }}</div>
                <div class="consultation-label">总数</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 影像申请统计卡片 -->
        <div class="apply-section">
          <div class="section-card" v-loading="loading">
            <div class="card-header">
              <div class="card-title">
                <el-icon class="title-icon"><DataAnalysis /></el-icon>
                <span>影像申请统计</span>
              </div>
              <a class="card-link" @click="router.push('/dicomApply')">查看更多</a>
            </div>
            <div class="apply-stats">
              <div class="apply-item" @click="handleStatClick('total-apply')">
                <div class="apply-number">{{ applyCount || 0 }}</div>
                <div class="apply-label">申请总数</div>
              </div>
              <div class="apply-item" @click="handleStatClick('synced-apply')">
                <div class="apply-number success">{{ applySyncedCount || 0 }}</div>
                <div class="apply-label">已同步</div>
              </div>
              <div class="apply-item" @click="handleStatClick('unsynced-apply')">
                <div class="apply-number warning">{{ applyUnsyncedCount || 0 }}</div>
                <div class="apply-label">待同步</div>
              </div>
              <div class="apply-item" @click="handleStatClick('sync-rate')">
                <div class="apply-number info">{{ syncRate }}%</div>
                <div class="apply-label">成功率</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧功能区域 -->
      <div class="right-content">
        <!-- 快捷功能 -->
        <div class="quick-actions">
          <div class="section-title">快速访问</div>
          <div class="action-grid">
            <div class="action-item" @click="handleQuickAccess({route: '/diagnosis/workspace'})">
              <div class="action-icon blue">
                <el-icon><Monitor /></el-icon>
              </div>
              <div class="action-text">影像诊断</div>
            </div>
            <div class="action-item" @click="handleQuickAccess({route: '/diagnosis/workspace'})">
              <div class="action-icon green">
                <el-icon><Select /></el-icon>
              </div>
              <div class="action-text">诊断审核</div>
            </div>
            <div class="action-item" @click="handleQuickAccess({route: '/consultation/my-request'})">
              <div class="action-icon purple">
                <el-icon><ChatDotRound /></el-icon>
              </div>
              <div class="action-text">会诊申请</div>
            </div>
            <div class="action-item" @click="handleQuickAccess({route: '/consultation/my-consultation'})">
              <div class="action-icon orange">
                <el-icon><User /></el-icon>
              </div>
              <div class="action-text">我的会诊</div>
            </div>
            <div class="action-item" @click="handleQuickAccess({route: '/datasync/study2'})">
              <div class="action-icon blue">
                <el-icon><Upload /></el-icon>
              </div>
              <div class="action-text">数据同步</div>
            </div>
            <div class="action-item" @click="handleQuickAccess({route: '/system/user'})">
              <div class="action-icon gray">
                <el-icon><Setting /></el-icon>
              </div>
              <div class="action-text">系统管理</div>
            </div>
          </div>
        </div>

        <!-- 影像数据同步统计 -->
        <div class="sync-overview-section">
          <div class="section-card" v-loading="loading">
            <div class="card-header">
              <div class="card-title">
                <el-icon class="title-icon"><Upload /></el-icon>
                <span>数据同步概览</span>
              </div>
              <a class="card-link" @click="router.push('/datasync/study2')">查看详情</a>
            </div>
            <div class="sync-overview-stats">
              <div class="sync-overview-item" @click="handleStatClick('total-studies')">
                <div class="sync-overview-number total">{{ studyCount || 0 }}</div>
                <div class="sync-overview-label">总检查数</div>
              </div>
              <div class="sync-overview-item" @click="handleStatClick('synced-studies')">
                <div class="sync-overview-number synced">{{ syncedStudyCount || 0 }}</div>
                <div class="sync-overview-label">已同步</div>
              </div>
            </div>
            <div class="sync-progress">
              <div class="progress-info">
                <span class="progress-text">同步进度</span>
                <span class="progress-percent">{{ Math.round((syncedStudyCount / (studyCount || 1)) * 100) }}%</span>
              </div>
              <el-progress
                :percentage="Math.round((syncedStudyCount / (studyCount || 1)) * 100)"
                :stroke-width="8"
                :show-text="false"
                color="#52c41a"
              />
            </div>
          </div>
        </div>
      </div>
    </div>





  </div>
</template>

<script setup name="Index">
import {ref, onMounted, onBeforeUnmount, computed} from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts';
import useUserStore from '@/store/modules/user';
import {
  getSyncStatistics,
  getSyncStatus,
  getStudyStatistics,
  getDiagnosisStatusCount,
  getConsultationStatistics,
  getPdfStatistics,
  getUnreadNotificationCount,
  getRecentNotifications,
  getSystemOverview
} from '@/api/dashboard/index';
import { listMyConsultations, listMyRequests } from '@/api/consultation/request';
// 导入 Element Plus 图标
import {
  DataAnalysis, Finished, Clock, Document, Refresh, Operation, Bell, Calendar, PieChart,
  ChatDotRound, Select, Monitor, User, Upload, Setting, View, More, Close
} from '@element-plus/icons-vue';

// 路由和用户信息
const router = useRouter();
const userStore = useUserStore();
const userInfo = computed(() => userStore.user || {});

// 当前日期
const currentDate = computed(() => {
  const now = new Date();
  const options = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  };
  return now.toLocaleDateString('zh-CN', options);
});

// 同步成功率计算
const syncRate = computed(() => {
  const total = applyCount.value || 0;
  const synced = applySyncedCount.value || 0;
  if (total === 0) return 0;
  return Math.round((synced / total) * 100);
});

// 快捷功能配置
const quickAccessItems = ref([
  {
    key: 'diagnosis-workspace',
    title: '诊断工作台',
    description: '影像诊断与报告编写',
    icon: Monitor,
    iconClass: 'bg-primary',
    route: '/diagnosis/workspace',
    badge: null
  },
  {
    key: 'consultation-request',
    title: '我的申请',
    description: '会诊申请管理',
    icon: ChatDotRound,
    iconClass: 'bg-warning',
    route: '/consultation/my-request',
    badge: null
  },
  {
    key: 'consultation-consultant',
    title: '我的会诊',
    description: '待处理会诊任务',
    icon: User,
    iconClass: 'bg-success',
    route: '/consultation/my-consultation',
    badge: 3
  },
  {
    key: 'data-sync',
    title: '数据同步',
    description: 'PACS数据同步管理',
    icon: Upload,
    iconClass: 'bg-info',
    route: '/datasync',
    badge: null
  },
  {
    key: 'diagnosis-audit',
    title: '诊断审核',
    description: '诊断报告审核',
    icon: Select,
    iconClass: 'bg-purple',
    route: '/diagnosis/audit',
    badge: null
  },
  {
    key: 'system-user',
    title: '用户管理',
    description: '系统用户管理',
    icon: Setting,
    iconClass: 'bg-orange',
    route: '/system/user',
    badge: null
  }
]);

// 统计数据和图表逻辑
const dateRange = ref([]);
const chartTimeRange = ref('30d');

// 统计数据
const applyCount = ref(0);
const applySyncedCount = ref(0);
const applyUnsyncedCount = ref(0);
const studyCount = ref(0);
const syncedStudyCount = ref(0);
const applyTrend = ref([]);
const syncedTrend = ref([]);
const unsyncedTrend = ref([]);

// 会诊统计数据
const consultationStats = ref({
  pendingCount: 0,
  acceptedCount: 0,
  completedCount: 0,
  totalCount: 0
});

// 诊断统计数据
const diagnosisStats = ref({
  pendingCount: 0,    // 待诊断数量 (status = -1)
  diagnosedCount: 0,  // 已诊断数量 (status = 1)
  auditedCount: 0,    // 已审核数量 (status = 2)
  archivedCount: 0,   // 院内诊断数量 (status = 9)
  totalCount: 0       // 总诊断数量
});

// 通知相关数据
const unreadCount = ref(0);
const recentNotifications = ref([]);
const notificationLoading = ref(false);

// 会诊消息数据
const activeMessageTab = ref('invited');
const consultationMessages = ref([]);
const invitedMessages = ref([]);      // 受邀的会诊消息
const myRequestMessages = ref([]);    // 我的申请消息
const totalUnreadCount = ref(0);
const totalMessageCount = ref(0);
const messageLoading = ref(false);

// 加载状态
const loading = ref(false);

// 图表相关
const trendChartRef = ref(null);
const statusPieChartRef = ref(null);
let trendChartInstance = null;
let pieChartInstance = null;

// 快捷功能处理
const handleQuickAccess = (item) => {
  if (item.route) {
    router.push(item.route);
  }
};

// 待办任务点击处理
const handleTaskClick = (taskType) => {
  switch (taskType) {
    case 'diagnosis-audit':
      router.push('/diagnosis/audit');
      break;
    case 'consultation-pending':
      router.push('/consultation/my-consultation');
      break;
    case 'sync-failed':
      router.push('/datasync');
      break;
    case 'patient-new':
      router.push('/diagnosis/study-select');
      break;
    default:
      console.log('未知任务类型:', taskType);
  }
};

// 统计卡片点击处理
const handleStatClick = (statType) => {
  switch (statType) {
    case 'total-studies':
      router.push('/datasync/study2');
      break;
    case 'synced-studies':
      router.push('/datasync/study2?dicomSyncFlag=1');
      break;
    case 'pending-diagnosis':
      router.push('/datasync/study2');
      break;
    case 'total-apply':
      router.push('/dicomApply');
      break;
    case 'synced-apply':
      router.push('/dicomApply?status=synced');
      break;
    case 'unsynced-apply':
      router.push('/dicomApply?status=unsynced');
      break;
    case 'sync-rate':
      router.push('/dicomApply');
      break;
    case 'consultation-pending':
      router.push('/consultation/my-consultation?status=pending');
      break;
    case 'consultation-accepted':
      router.push('/consultation/my-consultation?status=accepted');
      break;
    case 'consultation-completed':
      router.push('/consultation/my-consultation?status=completed');
      break;
    case 'consultation-total':
      router.push('/consultation/my-consultation');
      break;
    case 'diagnosis-pending':
      router.push('/diagnosis/workspace?diagnosisStatus=-1');
      break;
    case 'diagnosis-diagnosed':
      router.push('/diagnosis/workspace?diagnosisStatus=1');
      break;
    case 'diagnosis-audited':
      router.push('/diagnosis/workspace?diagnosisStatus=2');
      break;
    case 'diagnosis-archived':
      router.push('/diagnosis/workspace?diagnosisStatus=9');
      break;
    default:
      console.log('未知统计类型:', statType);
  }
};

// 通知相关方法
const handleNotificationClick = (notification) => {
  // 根据通知类型跳转到相应页面
  switch (notification.type) {
    case 'CONSULTATION_REQUEST':
      router.push('/consultation/my-consultation');
      break;
    case 'CONSULTATION_ACCEPT':
      router.push('/consultation/my-request');
      break;
    case 'CONSULTATION_REJECT':
      router.push('/consultation/my-request');
      break;
    case 'CONSULTATION_COMPLETE':
      router.push('/consultation/my-consultation');
      break;
    case 'DIAGNOSIS_COMPLETE':
      router.push('/diagnosis/audit');
      break;
    case 'SYSTEM':
    case 'WARNING':
      // 系统通知和警告通知暂不跳转
      console.log('点击通知:', notification);
      break;
    default:
      console.log('未知通知类型:', notification);
  }
};

const viewAllNotifications = () => {
  router.push('/consultation/notification');
};

// 会诊消息相关函数
const handleMessageClick = (message) => {
  // 根据消息类型跳转到相应页面
  if (message.requesterId === userInfo.value.userId) {
    // 如果是申请人，跳转到我的申请页面
    router.push('/consultation/my-request');
  } else {
    // 如果是受邀人，跳转到我的会诊页面
    router.push('/consultation/my-consultation');
  }
};

const getMessageIcon = (type) => {
  const iconMap = {
    'PENDING': 'Clock',
    'ACCEPTED': 'CircleCheck',
    'REJECTED': 'CircleClose',
    'COMPLETED': 'Finished',
    'CANCELLED': 'Close'
  };
  return iconMap[type] || 'ChatDotRound';
};

const getMessageIconClass = (type) => {
  const classMap = {
    'PENDING': 'message-warning',
    'ACCEPTED': 'message-success',
    'REJECTED': 'message-danger',
    'COMPLETED': 'message-primary',
    'CANCELLED': 'message-info'
  };
  return classMap[type] || 'message-primary';
};

// 受邀消息标题
const getInvitedMessageTitle = (message) => {
  const statusMap = {
    'PENDING': '会诊申请',
    'ACCEPTED': '已接受会诊',
    'REJECTED': '已拒绝会诊',
    'COMPLETED': '已完成会诊',
    'CANCELLED': '申请已取消'
  };
  return statusMap[message.status] || '会诊消息';
};

// 我的申请消息标题
const getMyRequestMessageTitle = (message) => {
  const statusMap = {
    'PENDING': '等待处理',
    'ACCEPTED': '申请已接受',
    'REJECTED': '申请已拒绝',
    'COMPLETED': '会诊已完成',
    'CANCELLED': '申请已取消'
  };
  return statusMap[message.status] || '会诊消息';
};

// 判断是否为未读消息
const isUnreadMessage = (message) => {
  // 24小时内有状态变更的消息视为未读
  if (['ACCEPTED', 'REJECTED', 'COMPLETED'].includes(message.status) && message.responseTime) {
    const responseTime = new Date(message.responseTime);
    const now = new Date();
    return (now - responseTime) < 24 * 60 * 60 * 1000; // 24小时内
  }
  // 待处理的消息也视为未读
  return message.status === 'PENDING';
};

const getMessageStatusType = (status) => {
  const typeMap = {
    'PENDING': 'warning',
    'ACCEPTED': 'success',
    'REJECTED': 'danger',
    'COMPLETED': 'primary',
    'CANCELLED': 'info'
  };
  return typeMap[status] || 'info';
};

const getMessageStatusText = (status) => {
  const textMap = {
    'PENDING': '待处理',
    'ACCEPTED': '已接受',
    'REJECTED': '已拒绝',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消'
  };
  return textMap[status] || status;
};

const refreshMessages = async () => {
  messageLoading.value = true;
  try {
    // 并行获取两类消息
    const [invitedResponse, requestsResponse] = await Promise.allSettled([
      listMyConsultations({ pageNum: 1, pageSize: 10 }),
      listMyRequests({ pageNum: 1, pageSize: 10 })
    ]);

    // 处理受邀的会诊消息
    if (invitedResponse.status === 'fulfilled' && invitedResponse.value?.rows) {
      invitedMessages.value = invitedResponse.value.rows || [];
    }

    // 处理我的申请消息
    if (requestsResponse.status === 'fulfilled' && requestsResponse.value?.rows) {
      myRequestMessages.value = requestsResponse.value.rows || [];
    }

    // 合并所有消息并计算统计数据
    const allMessages = [...invitedMessages.value, ...myRequestMessages.value];
    consultationMessages.value = allMessages;
    totalMessageCount.value = allMessages.length;

    // 计算未读消息数量
    const invitedUnread = invitedMessages.value.filter(msg => isUnreadMessage(msg)).length;
    const requestUnread = myRequestMessages.value.filter(msg => isUnreadMessage(msg)).length;
    totalUnreadCount.value = invitedUnread + requestUnread;

    ElMessage.success('消息刷新成功');
  } catch (error) {
    console.error('刷新消息失败:', error);
    ElMessage.error('消息刷新失败');
  } finally {
    messageLoading.value = false;
  }
};

const viewAllMessages = () => {
  router.push('/consultation/my-consultation');
};

const getNotificationIcon = (type) => {
  const iconMap = {
    'CONSULTATION_REQUEST': 'ChatDotRound',
    'CONSULTATION_ACCEPT': 'Select',
    'CONSULTATION_REJECT': 'Close',
    'CONSULTATION_COMPLETE': 'Finished',
    'DIAGNOSIS_COMPLETE': 'Document',
    'SYSTEM': 'Bell',
    'WARNING': 'Warning'
  };
  return iconMap[type] || 'Bell';
};

const getNotificationIconClass = (type) => {
  const classMap = {
    'CONSULTATION_REQUEST': 'notification-primary',
    'CONSULTATION_ACCEPT': 'notification-success',
    'CONSULTATION_REJECT': 'notification-danger',
    'CONSULTATION_COMPLETE': 'notification-info',
    'DIAGNOSIS_COMPLETE': 'notification-success',
    'SYSTEM': 'notification-info',
    'WARNING': 'notification-warning'
  };
  return classMap[type] || 'notification-info';
};

const formatTime = (time) => {
  if (!time) return '';
  const date = new Date(time);
  const now = new Date();
  const diff = now - date;

  if (diff < 60000) return '刚刚';
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
  return date.toLocaleDateString('zh-CN');
};

// 设置默认日期范围
const setDefaultDateRange = () => {
  const today = new Date();
  const year = today.getFullYear();
  const month = today.getMonth();
  const firstDayOfMonth = new Date(year, month, 1);
  const lastDayOfMonth = new Date(year, month + 1, 0);

  const formatDate = (date) => {
    const y = date.getFullYear();
    const m = String(date.getMonth() + 1).padStart(2, '0');
    const d = String(date.getDate()).padStart(2, '0');
    return `${y}-${m}-${d}`;
  }

  dateRange.value = [formatDate(firstDayOfMonth), formatDate(lastDayOfMonth)];
};

// 处理日期变化
const handleDateChange = () => {
  if (dateRange.value && dateRange.value.length === 2) {
    getStatistics(dateRange.value[0], dateRange.value[1]);
  }
};

// 处理图表时间范围变化
const handleChartTimeRangeChange = (value) => {
  if (value === 'custom') {
    return;
  }

  const today = new Date();
  let startDate;

  if (value === '7d') {
    startDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
  } else if (value === '30d') {
    startDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
  }

  const formatDate = (date) => {
    const y = date.getFullYear();
    const m = String(date.getMonth() + 1).padStart(2, '0');
    const d = String(date.getDate()).padStart(2, '0');
    return `${y}-${m}-${d}`;
  };

  dateRange.value = [formatDate(startDate), formatDate(today)];
  getStatistics(dateRange.value[0], dateRange.value[1]);
};

// 获取统计数据
const getStatistics = async (start = null, end = null) => {
  loading.value = true;
  try {
    const params = {};
    if (start) params.start = start;
    if (end) params.end = end;

    // 并行获取所有统计数据
    const [
      studyStatResponse,
      syncStatisticsResponse,
      diagnosisStatusResponse,
      consultationStatsResponse,
      notificationCountResponse,
      notificationsResponse,
      invitedConsultationsResponse,
      myRequestsResponse
    ] = await Promise.allSettled([
      getStudyStatistics(params),
      getSyncStatistics(),
      getDiagnosisStatusCount(params),
      getConsultationStatistics(),
      getUnreadNotificationCount(),
      getRecentNotifications(),
      listMyConsultations({ pageNum: 1, pageSize: 10 }),
      listMyRequests({ pageNum: 1, pageSize: 10 })
    ]);

    // 处理检查统计数据
    if (studyStatResponse.status === 'fulfilled' && studyStatResponse.value?.data) {
      const data = studyStatResponse.value.data;
      applyCount.value = data.applyCount || 0;
      applySyncedCount.value = data.applySyncedCount || 0;
      applyUnsyncedCount.value = data.applyUnsyncedCount || 0;
      applyTrend.value = Array.isArray(data.applyTrend) ? data.applyTrend : [];
      syncedTrend.value = Array.isArray(data.syncedTrend) ? data.syncedTrend : [];
      unsyncedTrend.value = Array.isArray(data.unsyncedTrend) ? data.unsyncedTrend : [];
    }

    // 处理同步统计数据（数据同步概览）
    if (syncStatisticsResponse.status === 'fulfilled' && syncStatisticsResponse.value?.data) {
      const data = syncStatisticsResponse.value.data;
      // 从同步统计API获取真实的总检查数和已同步数
      if (data.studyData) {
        studyCount.value = data.studyData.totalCount || 0;
        syncedStudyCount.value = data.studyData.syncedCount || 0;
      }
    }

    // 处理诊断状态统计
    if (diagnosisStatusResponse.status === 'fulfilled' && diagnosisStatusResponse.value?.data) {
      const data = diagnosisStatusResponse.value.data;
      diagnosisStats.value = {
        pendingCount: data.pending || 0,      // 待诊断 (status = -1)
        diagnosedCount: data.diagnosed || 0,  // 已诊断 (status = 1)
        auditedCount: data.audited || 0,      // 已审核 (status = 2)
        archivedCount: data.archived || 0,    // 院内诊断 (status = 9)
        totalCount: (data.pending || 0) + (data.diagnosed || 0) + (data.audited || 0) + (data.archived || 0)
      };
    }

    // 处理会诊统计数据
    if (consultationStatsResponse.status === 'fulfilled' && consultationStatsResponse.value?.data) {
      const data = consultationStatsResponse.value.data;
      consultationStats.value = {
        pendingCount: data.pendingCount || 0,
        acceptedCount: data.acceptedCount || 0,
        completedCount: data.completedCount || 0,
        totalCount: data.totalCount || 0
      };
    }

    // 处理通知数据
    if (notificationCountResponse.status === 'fulfilled' && notificationCountResponse.value?.data) {
      unreadCount.value = notificationCountResponse.value.data || 0;
    }

    if (notificationsResponse.status === 'fulfilled' && notificationsResponse.value?.rows) {
      recentNotifications.value = notificationsResponse.value.rows || [];
    }

    // 处理受邀的会诊消息
    if (invitedConsultationsResponse.status === 'fulfilled' && invitedConsultationsResponse.value?.rows) {
      invitedMessages.value = invitedConsultationsResponse.value.rows || [];
    }

    // 处理我的申请消息
    if (myRequestsResponse.status === 'fulfilled' && myRequestsResponse.value?.rows) {
      myRequestMessages.value = myRequestsResponse.value.rows || [];
    }

    // 合并所有消息并计算统计数据
    const allMessages = [...invitedMessages.value, ...myRequestMessages.value];
    consultationMessages.value = allMessages;
    totalMessageCount.value = allMessages.length;

    // 计算未读消息数量
    const invitedUnread = invitedMessages.value.filter(msg => isUnreadMessage(msg)).length;
    const requestUnread = myRequestMessages.value.filter(msg => isUnreadMessage(msg)).length;
    totalUnreadCount.value = invitedUnread + requestUnread;

    initCharts();
  } catch (error) {
    console.error("获取统计数据失败:", error);
    ElMessage.error('获取统计数据失败');
    initCharts();
  } finally {
    loading.value = false;
  }
};

// Initialize/Update charts function
const initCharts = () => {
  const colorPalette = ['#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE', '#3BA272', '#FC8452', '#9A60B4', '#EA7CCC'];

  // Trend Chart
  if (trendChartRef.value) {
    try {
      if (trendChartInstance) {
        trendChartInstance.dispose();
      }
      trendChartInstance = echarts.init(trendChartRef.value);

      const hasTrendData = applyTrend.value.length > 0 || syncedTrend.value.length > 0 || unsyncedTrend.value.length > 0;
      const dates = hasTrendData ? (applyTrend.value.length > 0 ? applyTrend.value.map(item => item.date) : (syncedTrend.value.length > 0 ? syncedTrend.value.map(item => item.date) : unsyncedTrend.value.map(item => item.date))) : [];

      const trendOption = {
        color: colorPalette,
        tooltip: {trigger: 'axis'},
        legend: {
          data: ['总申请数', '已同步', '待同步'],
          top: 10,
          itemGap: 15,
          textStyle: {color: '#666'}
        },
        grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true},
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: dates,
          axisLine: {lineStyle: {color: '#ccc'}},
          axisLabel: {color: '#666'}
        },
        yAxis: {
          type: 'value',
          axisLine: {show: true, lineStyle: {color: '#ccc'}},
          splitLine: {lineStyle: {type: 'dashed', color: '#eee'}},
          axisLabel: {color: '#666'}
        },
        series: [
          {
            name: '总申请数',
            type: 'line',
            smooth: true,
            data: hasTrendData ? applyTrend.value.map(item => item.count) : [],
            areaStyle: {opacity: 0.1}
          },
          {
            name: '已同步',
            type: 'line',
            smooth: true,
            data: hasTrendData ? syncedTrend.value.map(item => item.count) : [],
            areaStyle: {opacity: 0.1}
          },
          {
            name: '待同步',
            type: 'line',
            smooth: true,
            data: hasTrendData ? unsyncedTrend.value.map(item => item.count) : [],
            areaStyle: {opacity: 0.1}
          }
        ]
      };

      if (!hasTrendData) {
        trendOption.title = {
          text: '暂无趋势数据',
          left: 'center',
          top: 'center',
          textStyle: {color: '#888', fontSize: 14}
        };
      }

      trendChartInstance.setOption(trendOption);
    } catch (error) {
      console.error("趋势图初始化失败:", error);
    }
  }

  // Status Pie Chart
  if (statusPieChartRef.value) {
    try {
      if (pieChartInstance) {
        pieChartInstance.dispose();
      }
      pieChartInstance = echarts.init(statusPieChartRef.value);

      const hasPieData = (applySyncedCount.value || 0) + (applyUnsyncedCount.value || 0) > 0;

      const pieOption = {
        color: colorPalette.slice(1, 3),
        tooltip: {trigger: 'item', formatter: '{a} <br/>{b}: {c} ({d}%)'},
        legend: {top: '5%', left: 'center'},
        series: [
          {
            name: '申请状态分布',
            type: 'pie',
            radius: ['50%', '75%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 8,
              borderColor: '#fff',
              borderWidth: 3,
            },
            label: {show: false, position: 'center'},
            emphasis: {
              label: {show: true, fontSize: '16', fontWeight: 'bold'},
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            labelLine: {show: false},
            data: hasPieData ? [
              {value: applySyncedCount.value || 0, name: '已同步'},
              {value: applyUnsyncedCount.value || 0, name: '待同步'},
            ] : []
          }
        ]
      };

      if (!hasPieData) {
        pieOption.title = {
          text: '暂无状态数据',
          left: 'center',
          top: 'center',
          textStyle: {color: '#888', fontSize: 14}
        };
      }

      pieChartInstance.setOption(pieOption);
    } catch (error) {
      console.error("饼图初始化失败:", error);
    }
  }

  window.addEventListener('resize', resizeCharts);
};

// Resize charts function
const resizeCharts = () => {
  if (trendChartInstance) {
    trendChartInstance.resize();
  }
  if (pieChartInstance) {
    pieChartInstance.resize();
  }
};

// Cleanup on unmount
onBeforeUnmount(() => {
  window.removeEventListener('resize', resizeCharts);
  if (trendChartInstance) {
    trendChartInstance.dispose();
    trendChartInstance = null;
  }
  if (pieChartInstance) {
    pieChartInstance.dispose();
    pieChartInstance = null;
  }
});

// 组件挂载
onMounted(async () => {
  try {
    // 获取用户信息
    await userStore.getInfo();

    // 设置默认日期范围
    setDefaultDateRange();

    // 获取统计数据
    if (dateRange.value && dateRange.value.length === 2) {
      await getStatistics(dateRange.value[0], dateRange.value[1]);
    }
  } catch (error) {
    console.error('初始化失败:', error);
  }
});

// 手动刷新数据
const refreshData = async () => {
  try {
    if (dateRange.value && dateRange.value.length === 2) {
      await getStatistics(dateRange.value[0], dateRange.value[1]);
    } else {
      setDefaultDateRange();
      if (dateRange.value && dateRange.value.length === 2) {
        await getStatistics(dateRange.value[0], dateRange.value[1]);
      }
    }
    ElMessage.success('数据刷新成功');
  } catch (error) {
    ElMessage.error('数据刷新失败');
  }
};


</script>

<style scoped lang="scss">
.home-container {
  font-family: "PingFang SC", "Helvetica Neue", Helvetica, Arial, sans-serif;
  color: #333;
  margin: -5px; // 抵消全局app-container的padding
  background: #f5f5f5;
  min-height: 100%; // 让内容自然扩展，滚动由AppMain处理
  display: flex;
  flex-direction: column;

  // 页面头部样式
  .page-header {
    background: #fff;
    padding: 16px 24px;
    border-bottom: 1px solid #e8e8e8;
    margin-bottom: 0;
    flex-shrink: 0;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header-left {
      .breadcrumb {
        margin-bottom: 8px;

        .breadcrumb-item {
          color: #1890ff;
          font-size: 14px;
        }
      }

      h1 {
        margin: 0;
        font-size: 20px;
        font-weight: 500;
        color: #333;
      }
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 16px;

      .header-actions {
        display: flex;
        gap: 8px;
      }

      .date-info {
        font-size: 14px;
        color: #666;
      }
    }
  }

  // 主要内容区域
  .main-content {
    display: flex;
    gap: 24px;
    padding: 24px;
    flex: 1;

    .left-content {
      flex: 2;
      min-width: 0; // 防止flex子项溢出
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .right-content {
      flex: 1;
      min-width: 280px;
      max-width: 400px;
      display: flex;
      flex-direction: column;
      gap: 24px;
    }
  }

  // 会诊消息区域样式
  .message-section {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;

    // 融合的标题和标签页
    .integrated-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 20px;
      border-bottom: 1px solid #f0f0f0;
      min-height: 48px;

      .header-left {
        display: flex;
        align-items: center;
        gap: 12px;

        .title-icon {
          color: #1890ff;
          font-size: 18px;
          flex-shrink: 0;
        }

        .message-tabs {
          display: flex;
          gap: 0;

          .message-tab {
            padding: 8px 16px;
            cursor: pointer;
            color: #666;
            font-size: 14px;
            font-weight: 500;
            border-bottom: 2px solid transparent;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.3s ease;
            border-radius: 4px 4px 0 0;
            position: relative;

            &.active {
              color: #1890ff;
              border-bottom-color: #1890ff;
              background: #f8f9ff;
            }

            &:hover:not(.active) {
              color: #1890ff;
              background: #f5f5f5;
            }

            .tab-badge {
              .el-badge__content {
                background: #1890ff;
                border: none;
                font-size: 10px;
                height: 16px;
                line-height: 16px;
                padding: 0 4px;
                min-width: 16px;
                transform: scale(0.9);
              }
            }
          }
        }
      }

      .header-right {
        display: flex;
        align-items: center;
        gap: 8px;

        .unread-badge {
          .el-badge__content {
            background: #ff4d4f;
            border: none;
            font-size: 10px;
            height: 16px;
            line-height: 16px;
            padding: 0 4px;
            min-width: 16px;
          }
        }

        .total-count {
          font-size: 12px;
          color: #999;
          margin-right: 8px;
        }

        .el-button {
          padding: 4px 8px;

          .el-icon {
            font-size: 14px;
          }
        }
      }
    }

    .message-list {
      padding: 12px 16px;
      max-height: 400px;
      overflow-y: auto;

      .message-item {
        display: flex;
        align-items: flex-start;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        transition: background-color 0.3s ease;

        &:hover {
          background: #f5f5f5;
        }

        &:last-child {
          border-bottom: none;
        }

        // 紧凑模式样式
        &.compact {
          padding: 6px 0;

          .message-icon {
            margin-right: 8px;
            margin-top: 1px;

            .el-icon {
              font-size: 14px;

              &.message-primary { color: #1890ff; }
              &.message-success { color: #52c41a; }
              &.message-warning { color: #fa8c16; }
              &.message-danger { color: #ff4d4f; }
              &.message-info { color: #722ed1; }
            }
          }

          .message-content {
            flex: 1;

            .message-title {
              font-size: 13px;
              color: #333;
              margin-bottom: 3px;
              font-weight: 500;
              line-height: 1.2;
            }

            .message-meta {
              display: flex;
              align-items: center;
              gap: 8px;

              .message-from {
                font-size: 11px;
                color: #666;
              }

              .message-time {
                font-size: 11px;
                color: #999;
              }

              .status-tag {
                font-size: 10px;
                height: 18px;
                line-height: 16px;
                padding: 0 4px;
              }
            }
          }

          .message-unread {
            margin-left: 6px;
            margin-top: 3px;
          }
        }

        // 标准模式样式（保留原有样式）
        &:not(.compact) {
          .message-icon {
            margin-right: 12px;
            margin-top: 2px;

            .el-icon {
              font-size: 18px;

              &.message-primary { color: #1890ff; }
              &.message-success { color: #52c41a; }
              &.message-warning { color: #fa8c16; }
              &.message-danger { color: #ff4d4f; }
              &.message-info { color: #722ed1; }
            }
          }

          .message-content {
            flex: 1;

            .message-title {
              font-size: 14px;
              color: #333;
              margin-bottom: 4px;
              font-weight: 500;
            }

            .message-description {
              font-size: 12px;
              color: #666;
              margin-bottom: 6px;
              line-height: 1.4;
            }

            .message-meta {
              display: flex;
              justify-content: space-between;
              align-items: center;

              .message-from {
                font-size: 11px;
                color: #999;
              }

              .message-time {
                font-size: 11px;
                color: #999;
              }
            }
          }

          .message-status {
            margin-left: 8px;
            margin-top: 2px;
          }

          .message-unread {
            margin-left: 8px;
            margin-top: 6px;
          }
        }
      }
    }
  }

  // 空状态样式
  .empty-state {
    padding: 20px;
    text-align: center;

    &.compact {
      padding: 16px;

      .el-empty {
        .el-empty__image {
          width: 40px;
          height: 40px;
        }

        .el-empty__description {
          font-size: 12px;
          color: #999;
        }
      }
    }
  }



  // 统一的卡片样式
  .section-card {
    background: #fff;
    border-radius: 8px;
    border: 1px solid #e8e8e8;
    overflow: hidden;

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 20px;
      border-bottom: 1px solid #f0f0f0;
      background: #fafafa;

      .card-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 16px;
        font-weight: 600;
        color: #333;

        .title-icon {
          color: #1890ff;
          font-size: 18px;
        }
      }

      .card-link {
        color: #1890ff;
        font-size: 14px;
        text-decoration: none;
        cursor: pointer;
        transition: color 0.3s ease;

        &:hover {
          color: #40a9ff;
          text-decoration: underline;
        }
      }
    }
  }

  // 会诊统计样式
  .consultation-section {

    .consultation-stats {
      display: flex;
      padding: 20px;

      .consultation-item {
        flex: 1;
        text-align: center;
        cursor: pointer;
        padding: 12px 8px;
        border-radius: 6px;
        transition: background-color 0.3s ease;

        &:hover {
          background: #f5f5f5;
        }

        .consultation-number {
          font-size: 24px;
          font-weight: 600;
          margin-bottom: 8px;

          &.pending { color: #fa8c16; }
          &.accepted { color: #52c41a; }
          &.completed { color: #1890ff; }
          &.total { color: #333; }
        }

        .consultation-label {
          font-size: 13px;
          color: #666;
        }
      }
    }
  }

  // 影像申请统计样式
  .apply-section {

    .apply-stats {
      display: flex;
      padding: 20px;

      .apply-item {
        flex: 1;
        text-align: center;
        cursor: pointer;
        padding: 12px 8px;
        border-radius: 6px;
        transition: background-color 0.3s ease;

        &:hover {
          background: #f5f5f5;
        }

        .apply-number {
          font-size: 24px;
          font-weight: 600;
          margin-bottom: 8px;
          color: #333;

          &.success { color: #52c41a; }
          &.warning { color: #fa8c16; }
          &.info { color: #1890ff; }
        }

        .apply-label {
          font-size: 13px;
          color: #666;
        }
      }
    }
  }

  // 诊断统计样式
  .diagnosis-section {
    .diagnosis-stats {
      display: flex;
      padding: 20px;

      .diagnosis-item {
        flex: 1;
        text-align: center;
        cursor: pointer;
        padding: 12px 8px;
        border-radius: 6px;
        transition: background-color 0.3s ease;

        &:hover {
          background: #f5f5f5;
        }

        .diagnosis-number {
          font-size: 24px;
          font-weight: 600;
          margin-bottom: 8px;

          &.pending { color: #fa8c16; }    // 待诊断 - 橙色
          &.diagnosed { color: #1890ff; }  // 已诊断 - 蓝色
          &.audited { color: #52c41a; }    // 已审核 - 绿色
          &.archived { color: #722ed1; }   // 院内诊断 - 紫色
        }

        .diagnosis-label {
          font-size: 13px;
          color: #666;
        }
      }
    }
  }

  // 右侧功能区域样式
  .quick-actions {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 24px;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 16px;
    }

    .action-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;

      .action-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 16px 8px;
        cursor: pointer;
        border-radius: 8px;
        transition: background-color 0.3s;

        &:hover {
          background: #f5f5f5;
        }

        .action-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 8px;
          color: white;

          &.blue { background: #1890ff; }
          &.purple { background: #722ed1; }
          &.orange { background: #fa8c16; }
          &.green { background: #52c41a; }
          &.gray { background: #8c8c8c; }
        }

        .action-text {
          font-size: 12px;
          color: #666;
          text-align: center;
        }
      }
    }
  }







  // 右侧数据同步概览样式
  .sync-overview-section {
    .sync-overview-stats {
      display: flex;
      padding: 20px 20px 16px 20px;
      gap: 20px;

      .sync-overview-item {
        flex: 1;
        text-align: center;
        cursor: pointer;
        padding: 16px 12px;
        border-radius: 8px;
        border: 1px solid #f0f0f0;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
        }

        .sync-overview-number {
          font-size: 32px;
          font-weight: 700;
          margin-bottom: 8px;

          &.total { color: #333; }
          &.synced { color: #52c41a; }
        }

        .sync-overview-label {
          font-size: 14px;
          color: #666;
          font-weight: 500;
        }
      }
    }

    .sync-progress {
      padding: 0 20px 20px 20px;

      .progress-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .progress-text {
          font-size: 14px;
          color: #666;
          font-weight: 500;
        }

        .progress-percent {
          font-size: 16px;
          color: #52c41a;
          font-weight: 600;
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {

    .main-content {
      flex-direction: column;
      padding: 16px;
      gap: 16px;

      .left-content {
        flex: none;
        min-width: auto;
      }

      .right-content {
        flex: none;
        min-width: auto;
        max-width: none;
      }
    }

    .page-header {
      padding: 12px 16px;

      .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }

      .header-left h1 {
        font-size: 18px;
      }

      .header-right {
        align-self: flex-end;
      }
    }

    .sync-overview-stats {
      flex-direction: column;
      gap: 12px;

      .sync-overview-item {
        .sync-overview-number {
          font-size: 28px;
        }
      }
    }

    .consultation-stats, .apply-stats, .diagnosis-stats {
      flex-wrap: wrap;
      gap: 12px;

      .consultation-item, .apply-item, .diagnosis-item {
        flex: 1 1 45%;
        min-width: 100px;

        .consultation-number, .apply-number, .diagnosis-number {
          font-size: 20px;
        }
      }
    }

    // 诊断统计在移动端显示为2x2网格
    .diagnosis-stats {
      .diagnosis-item {
        flex: 1 1 calc(50% - 6px);
        min-width: calc(50% - 6px);
      }
    }

    .action-grid {
      grid-template-columns: repeat(2, 1fr) !important;
    }
  }
}

</style>
