/**
 * 主样式入口文件 - 精简版
 * 合并和精简了历史修复中的重复文件
 */

// Element Plus 主题定制 - 优先导入 (包含element-ui.scss内容)
@import './element-theme.scss';

// 基础样式
@import './variables.module.scss';
@import './mixin.scss';
@import './transition.scss';
@import './sidebar.scss';

// 组件样式 - 合并后的文件
@import './buttons.scss';    // 合并：btn.scss + button-fix.scss + button-theme.scss + global-button-fix.scss
@import './medical.scss';    // 合并：medical-theme.scss + medical-messages.scss
@import './ruoyi.scss';

/* ==================== 紧急按钮样式修复 ==================== */
/* 修复Element Plus按钮样式问题 */
.el-button--primary {
  color: #ffffff !important;
  background-color: #0066cc !important;
  border-color: #0066cc !important;

  &:hover, &:focus {
    color: #ffffff !important;
    background-color: #4d94ff !important;
    border-color: #4d94ff !important;
  }

  &:active {
    color: #ffffff !important;
    background-color: #004499 !important;
    border-color: #004499 !important;
  }
}

/* 修复多类型按钮的特殊情况 */
.el-button.el-button--warning.el-button--default,
.el-button.el-button--success.el-button--default,
.el-button.el-button--danger.el-button--default,
.el-button.el-button--primary.el-button--default,
.el-button.el-button--info.el-button--default {
  color: #ffffff !important;

  &:hover, &:focus, &:active {
    color: #ffffff !important;
  }
}

/* 特别处理warning类型按钮 */
.el-button--warning {
  color: #ffffff !important;
  background-color: #fa8c16 !important;
  border-color: #fa8c16 !important;

  &:hover, &:focus {
    color: #ffffff !important;
    background-color: #ff9c2e !important;
    border-color: #ff9c2e !important;
  }

  &:active {
    color: #ffffff !important;
    background-color: #d46b08 !important;
    border-color: #d46b08 !important;
  }
}

/* 特别处理success类型按钮 */
.el-button--success {
  color: #ffffff !important;
  background-color: #2e7d32 !important;
  border-color: #2e7d32 !important;

  &:hover, &:focus {
    color: #ffffff !important;
    background-color: #66bb6a !important;
    border-color: #66bb6a !important;
  }

  &:active {
    color: #ffffff !important;
    background-color: #1b5e20 !important;
    border-color: #1b5e20 !important;
  }
}

/* 特别处理danger类型按钮 */
.el-button--danger {
  color: #ffffff !important;
  background-color: #ff4d4f !important;
  border-color: #ff4d4f !important;

  &:hover, &:focus {
    color: #ffffff !important;
    background-color: #ff7875 !important;
    border-color: #ff7875 !important;
  }

  &:active {
    color: #ffffff !important;
    background-color: #d9363e !important;
    border-color: #d9363e !important;
  }
}

/* 特别处理info类型实心按钮 */
.el-button--info:not(.is-plain):not(.is-text):not(.is-link) {
  color: #ffffff !important;
  background-color: #909399 !important;
  border-color: #909399 !important;

  &:hover, &:focus {
    color: #ffffff !important;
    background-color: #a6a9ad !important;
    border-color: #a6a9ad !important;
  }

  &:active {
    color: #ffffff !important;
    background-color: #82848a !important;
    border-color: #82848a !important;
  }
}

.el-button:not(.el-button--primary):not(.el-button--success):not(.el-button--warning):not(.el-button--danger):not(.el-button--info):not(.is-link):not(.is-text):not(.is-plain) {
  color: #606266 !important;
  background-color: #ffffff !important;
  border-color: #dcdfe6 !important;

  &:hover, &:focus {
    color: #0066cc !important;
    background-color: #e6f2ff !important;
    border-color: #b3d1ff !important;
  }

  &:active {
    color: #0066cc !important;
    background-color: #e6f2ff !important;
    border-color: #0066cc !important;
  }
}


body {
  height: 100%;
  margin: 0;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 5px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}
