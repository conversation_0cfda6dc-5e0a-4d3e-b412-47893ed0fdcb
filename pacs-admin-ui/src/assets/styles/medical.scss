/**
 * 医疗主题统一样式
 * 合并：medical-theme.scss + medical-messages.scss
 * 包含：表格、卡片、统计、表单、分页、消息提示等医疗主题样式
 */

@import './variables.module.scss';

/* ==================== 医疗主题表格样式 ==================== */

.medical-table {
  .el-table {
    border: 1px solid var(--table-border);
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 102, 204, 0.08);

    .el-table__header-wrapper {
      .el-table__header {
        th {
          background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%) !important;
          color: var(--table-header-text) !important;
          font-weight: 600 !important;
          font-size: 14px !important;
          height: 48px !important;
          border-bottom: 2px solid var(--medical-primary) !important;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--medical-primary) 0%, var(--medical-primary-light) 100%);
          }

          .cell {
            padding: 0 16px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }

    .el-table__body-wrapper {
      .el-table__body {
        tr {
          transition: all 0.2s ease;

          &:hover {
            background-color: var(--table-hover-bg) !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 102, 204, 0.1);
          }

          td {
            border-bottom: 1px solid #f0f0f0 !important;
            padding: 12px 0 !important;

            .cell {
              padding: 0 16px;
              line-height: 1.5;
              font-size: 13px;
            }
          }

          &:nth-child(even) {
            background-color: #fafcff;
          }
        }
      }
    }
  }
}

/* ==================== 医疗主题状态标签 ==================== */

.medical-status-tag {
  &.pending {
    background: linear-gradient(135deg, #fff7e6 0%, #fff2d9 100%);
    color: #d46b08;
    border: 1px solid #ffd591;
  }

  &.diagnosed {
    background: linear-gradient(135deg, #f0f9f4 0%, #e8f5e8 100%);
    color: #2d7d32;
    border: 1px solid #a5d6a7;
  }

  &.audited {
    background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
    color: #0050b3;
    border: 1px solid #91d5ff;
  }

  &.archived {
    background: linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%);
    color: #531dab;
    border: 1px solid #d3adf7;
  }

  &.synced {
    background: linear-gradient(135deg, #f0f9f4 0%, #e8f5e8 100%);
    color: #2d7d32;
    border: 1px solid #a5d6a7;
  }

  &.sync-pending {
    background: linear-gradient(135deg, #fff7e6 0%, #fff2d9 100%);
    color: #d46b08;
    border: 1px solid #ffd591;
  }
}

/* ==================== 医疗主题卡片样式 ==================== */

.medical-card {
  border: 1px solid #e6f7ff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 102, 204, 0.08);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 102, 204, 0.15);
    transform: translateY(-2px);
  }

  .el-card__header {
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%);
    border-bottom: 2px solid var(--medical-primary);
    padding: 16px 20px;

    .card-header-title {
      color: var(--medical-primary);
      font-weight: 600;
      font-size: 16px;
    }
  }

  .el-card__body {
    padding: 20px;
  }
}

/* ==================== 医疗主题统计卡片 ==================== */

.medical-stats-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fcff 100%);
  border: 1px solid #e6f7ff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 102, 204, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--medical-primary) 0%, var(--medical-primary-light) 100%);
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 102, 204, 0.15);
  }

  .stats-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    margin-bottom: 12px;

    &.pending {
      background: linear-gradient(135deg, #fa8c16 0%, #ffa940 100%);
    }

    &.diagnosed {
      background: linear-gradient(135deg, #2e7d32 0%, #4caf50 100%);
    }

    &.audited {
      background: linear-gradient(135deg, #0066cc 0%, #4d94ff 100%);
    }

    &.archived {
      background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
    }
  }

  .stats-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
  }

  .stats-value {
    font-size: 28px;
    font-weight: 700;
    color: #262626;
    line-height: 1;
  }
}

/* ==================== 医疗主题表单样式 ==================== */

.medical-form {
  .el-form-item__label {
    color: var(--medical-primary) !important;
    font-weight: 600 !important;
    font-size: 14px !important;
  }

  .el-input__wrapper {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--medical-primary-light);
    }

    &.is-focus {
      border-color: var(--medical-primary);
      box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.1);
    }
  }

  .el-textarea__inner {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--medical-primary-light);
    }

    &:focus {
      border-color: var(--medical-primary);
      box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.1);
    }
  }
}

/* ==================== 医疗主题分页样式 ==================== */

.medical-pagination {
  .el-pagination {
    .el-pager li {
      border-radius: 6px;
      margin: 0 2px;
      transition: all 0.3s ease;

      &:hover {
        background-color: var(--table-hover-bg);
        color: var(--medical-primary);
      }

      &.is-active {
        background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-primary-light) 100%);
        color: white;
        box-shadow: 0 2px 8px rgba(0, 102, 204, 0.3);
      }
    }

    .btn-prev,
    .btn-next {
      border-radius: 6px;
      transition: all 0.3s ease;

      &:hover {
        background-color: var(--table-hover-bg);
        color: var(--medical-primary);
      }
    }
  }
}

/* ==================== 医疗主题消息提示样式 ==================== */

/* Element Plus Message 医疗主题覆盖 */
.el-message {
  border-radius: 8px !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important;
  border: none !important;
  padding: 16px 20px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  min-width: 320px !important;

  &.el-message--success {
    background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%) !important;
    color: #389e0d !important;
    border-left: 4px solid var(--medical-success) !important;

    .el-message__icon {
      color: var(--medical-success) !important;
      font-size: 18px !important;
    }
  }

  &.el-message--warning {
    background: linear-gradient(135deg, #fff7e6 0%, #fff2d9 100%) !important;
    color: #d46b08 !important;
    border-left: 4px solid var(--medical-warning) !important;

    .el-message__icon {
      color: var(--medical-warning) !important;
      font-size: 18px !important;
    }
  }

  &.el-message--error {
    background: linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%) !important;
    color: #cf1322 !important;
    border-left: 4px solid var(--medical-danger) !important;

    .el-message__icon {
      color: var(--medical-danger) !important;
      font-size: 18px !important;
    }
  }

  &.el-message--info {
    background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%) !important;
    color: #0050b3 !important;
    border-left: 4px solid var(--medical-primary) !important;

    .el-message__icon {
      color: var(--medical-primary) !important;
      font-size: 18px !important;
    }
  }

  .el-message__content {
    line-height: 1.5 !important;
    margin-left: 12px !important;
  }

  .el-message__closeBtn {
    color: #999 !important;
    font-size: 16px !important;

    &:hover {
      color: #666 !important;
    }
  }
}

/* 医疗场景专用提示样式 */
.medical-alert {
  border-radius: 8px;
  padding: 16px 20px;
  margin: 16px 0;
  border: none;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 4px;
  }

  &.medical-alert--critical {
    background: linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%);
    color: #cf1322;

    &::before {
      background: var(--medical-danger);
    }

    .medical-alert__icon {
      color: var(--medical-danger);
    }
  }

  &.medical-alert--warning {
    background: linear-gradient(135deg, #fff7e6 0%, #fff2d9 100%);
    color: #d46b08;

    &::before {
      background: var(--medical-warning);
    }

    .medical-alert__icon {
      color: var(--medical-warning);
    }
  }

  &.medical-alert--info {
    background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
    color: #0050b3;

    &::before {
      background: var(--medical-primary);
    }

    .medical-alert__icon {
      color: var(--medical-primary);
    }
  }

  &.medical-alert--success {
    background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
    color: #389e0d;

    &::before {
      background: var(--medical-success);
    }

    .medical-alert__icon {
      color: var(--medical-success);
    }
  }

  .medical-alert__content {
    display: flex;
    align-items: flex-start;
    padding-left: 16px;

    .medical-alert__icon {
      font-size: 20px;
      margin-right: 12px;
      margin-top: 2px;
      flex-shrink: 0;
    }

    .medical-alert__text {
      flex: 1;
      line-height: 1.5;
      font-size: 14px;
      font-weight: 500;

      .medical-alert__title {
        font-weight: 600;
        margin-bottom: 4px;
      }

      .medical-alert__description {
        font-weight: 400;
        opacity: 0.9;
      }
    }
  }
}
