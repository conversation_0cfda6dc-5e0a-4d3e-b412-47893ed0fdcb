/**
 * Element Plus 主题定制
 * 基于 Element Plus 官方主题定制规范
 * https://element-plus.org/zh-CN/guide/theming.html
 */

/* ==================== Element Plus CSS 变量覆盖 ==================== */

:root {
  /* 品牌色 - 医疗主题蓝色 */
  --el-color-primary: #0066cc;
  --el-color-primary-light-3: #4d94ff;
  --el-color-primary-light-5: #80b3ff;
  --el-color-primary-light-7: #b3d1ff;
  --el-color-primary-light-8: #ccdfff;
  --el-color-primary-light-9: #e6f2ff;
  --el-color-primary-dark-2: #004499;

  /* 功能色 */
  --el-color-success: #2e7d32;
  --el-color-success-light-3: #66bb6a;
  --el-color-success-light-5: #81c784;
  --el-color-success-light-7: #a5d6a7;
  --el-color-success-light-8: #c8e6c9;
  --el-color-success-light-9: #e8f5e8;
  --el-color-success-dark-2: #1b5e20;

  --el-color-warning: #fa8c16;
  --el-color-warning-light-3: #ffc53d;
  --el-color-warning-light-5: #ffd666;
  --el-color-warning-light-7: #ffe58f;
  --el-color-warning-light-8: #fff1b8;
  --el-color-warning-light-9: #fff7e6;
  --el-color-warning-dark-2: #d46b08;

  --el-color-danger: #ff4d4f;
  --el-color-danger-light-3: #ff7875;
  --el-color-danger-light-5: #ff9c9e;
  --el-color-danger-light-7: #ffbabb;
  --el-color-danger-light-8: #ffd8d8;
  --el-color-danger-light-9: #fff2f0;
  --el-color-danger-dark-2: #cf1322;

  --el-color-info: #909399;
  --el-color-info-light-3: #b1b3b8;
  --el-color-info-light-5: #c8c9cc;
  --el-color-info-light-7: #dedfe0;
  --el-color-info-light-8: #e9e9eb;
  --el-color-info-light-9: #f4f4f5;
  --el-color-info-dark-2: #73767a;

  /* 中性色 */
  --el-color-white: #ffffff;
  --el-color-black: #000000;
  --el-bg-color: #ffffff;
  --el-bg-color-page: #f2f3f5;
  --el-bg-color-overlay: #ffffff;
  --el-text-color-primary: #303133;
  --el-text-color-regular: #606266;
  --el-text-color-secondary: #909399;
  --el-text-color-placeholder: #a8abb2;
  --el-text-color-disabled: #c0c4cc;
  --el-border-color: #dcdfe6;
  --el-border-color-light: #e4e7ed;
  --el-border-color-lighter: #ebeef5;
  --el-border-color-extra-light: #f2f6fc;
  --el-border-color-dark: #d4d7de;
  --el-border-color-darker: #cdd0d6;
  --el-fill-color: #f0f2f5;
  --el-fill-color-light: #f5f7fa;
  --el-fill-color-lighter: #fafafa;
  --el-fill-color-extra-light: #fafcff;
  --el-fill-color-dark: #ebedf0;
  --el-fill-color-darker: #e6e8eb;
  --el-fill-color-blank: #ffffff;

  /* 边框 */
  --el-border-width: 1px;
  --el-border-style: solid;
  --el-border-color-hover: var(--el-color-primary-light-7);
  --el-border: var(--el-border-width) var(--el-border-style) var(--el-border-color);
  --el-border-radius-base: 4px;
  --el-border-radius-small: 2px;
  --el-border-radius-round: 20px;
  --el-border-radius-circle: 100%;

  /* 字体 */
  --el-font-size-extra-large: 20px;
  --el-font-size-large: 18px;
  --el-font-size-medium: 16px;
  --el-font-size-base: 14px;
  --el-font-size-small: 13px;
  --el-font-size-extra-small: 12px;
  --el-font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  --el-font-weight-primary: 500;

  /* 禁用状态 */
  --el-disabled-bg-color: var(--el-fill-color-light);
  --el-disabled-text-color: var(--el-text-color-placeholder);
  --el-disabled-border-color: var(--el-border-color-light);

  /* 动画 */
  --el-transition-duration: 0.3s;
  --el-transition-duration-fast: 0.2s;

  /* 组件尺寸 */
  --el-component-size-large: 40px;
  --el-component-size: 32px;
  --el-component-size-small: 24px;

  /* 阴影 */
  --el-box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.04), 0px 8px 20px rgba(0, 0, 0, 0.08);
  --el-box-shadow-light: 0px 0px 12px rgba(0, 0, 0, 0.12);
  --el-box-shadow-lighter: 0px 0px 6px rgba(0, 0, 0, 0.12);
  --el-box-shadow-dark: 0px 16px 48px 16px rgba(0, 0, 0, 0.08), 0px 12px 32px rgba(0, 0, 0, 0.12), 0px 8px 16px -8px rgba(0, 0, 0, 0.16);

  /* 医疗主题特有变量 */
  --medical-primary: var(--el-color-primary);
  --medical-primary-light: var(--el-color-primary-light-3);
  --medical-primary-dark: var(--el-color-primary-dark-2);
  --medical-success: var(--el-color-success);
  --medical-warning: var(--el-color-warning);
  --medical-danger: var(--el-color-danger);
  --medical-info: var(--el-color-info);

  /* 医疗主题表格变量 */
  --medical-table-header-bg: var(--el-color-primary-light-9);
  --medical-table-header-text: var(--el-color-primary);
  --medical-table-border: var(--el-color-primary-light-8);
  --medical-table-hover-bg: var(--el-color-primary-light-9);

  /* 医疗主题卡片变量 */
  --medical-card-border: var(--el-color-primary-light-8);
  --medical-card-shadow: 0 2px 8px rgba(0, 102, 204, 0.08);
  --medical-card-shadow-hover: 0 4px 16px rgba(0, 102, 204, 0.15);
}

/* 暗黑模式 */
html.dark {
  --el-bg-color: #141414;
  --el-bg-color-page: #0a0a0a;
  --el-bg-color-overlay: #1d1e1f;
  --el-text-color-primary: #e5eaf3;
  --el-text-color-regular: #cfd3dc;
  --el-text-color-secondary: #a3a6ad;
  --el-text-color-placeholder: #8d9095;
  --el-text-color-disabled: #6c6e72;
  --el-border-color: #4c4d4f;
  --el-border-color-light: #414243;
  --el-border-color-lighter: #363637;
  --el-border-color-extra-light: #2b2b2c;
  --el-border-color-dark: #58585b;
  --el-border-color-darker: #636466;
  --el-fill-color: #303133;
  --el-fill-color-light: #262727;
  --el-fill-color-lighter: #1d1d1d;
  --el-fill-color-extra-light: #191919;
  --el-fill-color-dark: #39393a;
  --el-fill-color-darker: #424243;
  --el-fill-color-blank: #141414;

  /* 医疗主题暗黑模式调整 */
  --medical-table-header-bg: var(--el-fill-color-darker);
  --medical-table-header-text: var(--el-color-primary-light-3);
  --medical-table-border: var(--el-border-color);
  --medical-table-hover-bg: var(--el-fill-color-light);
  --medical-card-border: var(--el-border-color);
  --medical-card-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  --medical-card-shadow-hover: 0 4px 16px rgba(0, 0, 0, 0.4);
}

/* ==================== 组件特定样式覆盖 ==================== */

/* 按钮组件 */
.el-button {
  --el-button-font-weight: var(--el-font-weight-primary);
  --el-button-border-color: var(--el-border-color);
  --el-button-bg-color: var(--el-fill-color-blank);
  --el-button-text-color: var(--el-text-color-regular);
  --el-button-disabled-text-color: var(--el-disabled-text-color);
  --el-button-disabled-bg-color: var(--el-fill-color-blank);
  --el-button-disabled-border-color: var(--el-border-color-light);
  --el-button-divide-border-color: rgba(255, 255, 255, 0.5);
  --el-button-hover-text-color: var(--el-color-primary);
  --el-button-hover-bg-color: var(--el-color-primary-light-9);
  --el-button-hover-border-color: var(--el-color-primary-light-7);
  --el-button-active-text-color: var(--el-button-hover-text-color);
  --el-button-active-border-color: var(--el-color-primary);
  --el-button-active-bg-color: var(--el-button-hover-bg-color);
  --el-button-outline-color: var(--el-color-primary-light-5);
  --el-button-active-color: var(--el-text-color-primary);
}

/* 表格组件 */
.el-table {
  --el-table-border-color: var(--medical-table-border);
  --el-table-border: 1px solid var(--el-table-border-color);
  --el-table-text-color: var(--el-text-color-regular);
  --el-table-header-text-color: var(--medical-table-header-text);
  --el-table-header-bg-color: var(--medical-table-header-bg);
  --el-table-bg-color: var(--el-fill-color-blank);
  --el-table-tr-bg-color: var(--el-fill-color-blank);
  --el-table-expanded-cell-bg-color: var(--el-fill-color-lighter);
  --el-table-fixed-box-shadow: var(--el-box-shadow-light);
  --el-table-row-hover-bg-color: var(--medical-table-hover-bg);
  --el-table-current-row-bg-color: var(--el-color-primary-light-9);
  --el-table-header-border-bottom-color: var(--el-border-color-lighter);
  --el-table-border-radius: 0;
}

/* 卡片组件 */
.el-card {
  --el-card-border-color: var(--medical-card-border);
  --el-card-border-radius: 8px;
  --el-card-padding: 20px;
  --el-card-bg-color: var(--el-fill-color-blank);
  box-shadow: var(--medical-card-shadow);
  transition: box-shadow var(--el-transition-duration);

  &:hover {
    box-shadow: var(--medical-card-shadow-hover);
  }
}

/* Element UI 兼容性样式 - 合并 element-ui.scss */
.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
  color: var(--medical-primary, #0066cc) !important;

  &:hover {
    color: var(--medical-primary-light, #4d94ff) !important;
  }
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

/* 医疗主题标签样式 */
.cell {
  .el-tag {
    margin-right: 0px;
    border-radius: 6px;
    font-weight: 500;
    padding: 4px 12px;
    border: 1px solid transparent;

    &.el-tag--success {
      background: linear-gradient(135deg, #f0f9f4 0%, #e8f5e8 100%);
      color: #2d7d32;
      border-color: #a5d6a7;
    }

    &.el-tag--warning {
      background: linear-gradient(135deg, #fff7e6 0%, #fff2d9 100%);
      color: #d46b08;
      border-color: #ffd591;
    }

    &.el-tag--danger {
      background: linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%);
      color: #cf1322;
      border-color: #ffadd2;
    }

    &.el-tag--info {
      background: linear-gradient(135deg, #f4f4f5 0%, #e9e9eb 100%);
      color: #73767a;
      border-color: #c8c9cc;
    }
  }
}

/* 输入框组件 */
.el-input {
  --el-input-border-color: var(--el-border-color);
  --el-input-hover-border-color: var(--el-border-color-hover);
  --el-input-focus-border-color: var(--el-color-primary);
  --el-input-transparent-border-color: transparent;
  --el-input-border-radius: var(--el-border-radius-base);
  --el-input-bg-color: var(--el-fill-color-blank);
  --el-input-icon-color: var(--el-text-color-placeholder);
  --el-input-placeholder-color: var(--el-text-color-placeholder);
  --el-input-hover-border: var(--el-border-color-hover);
  --el-input-clear-hover-color: var(--el-text-color-secondary);
  --el-input-focus-border: var(--el-color-primary);
}

/* 分页组件 */
.el-pagination {
  --el-pagination-font-size: var(--el-font-size-base);
  --el-pagination-bg-color: var(--el-fill-color-blank);
  --el-pagination-text-color: var(--el-text-color-primary);
  --el-pagination-border-radius: var(--el-border-radius-base);
  --el-pagination-button-color: var(--el-text-color-primary);
  --el-pagination-button-width: 32px;
  --el-pagination-button-height: 32px;
  --el-pagination-button-disabled-color: var(--el-text-color-placeholder);
  --el-pagination-button-disabled-bg-color: var(--el-fill-color-blank);
  --el-pagination-hover-color: var(--el-color-primary);
  --el-pagination-hover-bg-color: var(--el-color-primary-light-9);
  --el-pagination-active-color: var(--el-color-primary);
  --el-pagination-active-bg-color: var(--el-color-primary-light-9);
}
