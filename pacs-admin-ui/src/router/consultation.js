import Layout from '@/layout'

// 会诊管理路由
export default [
  {
    path: '/consultation',
    component: Layout,
    alwaysShow: true,
    name: 'Consultation',
    meta: { 
      title: '会诊管理', 
      icon: 'star',
      permissions: ['consultation:request:list', 'consultation:consultant:list', 'consultation:management:list']
    },
    children: [
      {
        path: 'my-request',
        component: () => import('@/views/consultation/request/index'),
        name: 'MyConsultationRequest',
        meta: { 
          title: '我的申请', 
          icon: 'edit-pen',
          permissions: ['consultation:request:list']
        }
      },
      {
        path: 'my-consultation',
        component: () => import('@/views/consultation/consultant/index'),
        name: 'MyConsultation',
        meta: { 
          title: '我的会诊', 
          icon: 'user',
          permissions: ['consultation:consultant:list']
        }
      },
      {
        path: 'management',
        component: () => import('@/views/consultation/management/index'),
        name: 'ConsultationManagement',
        meta: {
          title: '会诊管理',
          icon: 'monitor',
          permissions: ['consultation:management:list']
        }
      }
    ]
  }
]
