import { ElMessage, ElMessageBox, ElNotification, ElLoading } from 'element-plus'
import { getMedicalMessageStyle, getMedicalNotificationStyle } from '@/utils/medical-theme'

let loadingInstance;

export default {
  // 消息提示 - 医疗主题
  msg(content) {
    const style = getMedicalMessageStyle('info')
    ElMessage({
      message: content,
      type: 'info',
      customClass: style.customClass,
      duration: style.duration,
      showClose: true
    })
  },
  // 错误消息 - 医疗主题
  msgError(content) {
    const style = getMedicalMessageStyle('error')
    ElMessage({
      message: content,
      type: 'error',
      customClass: style.customClass,
      duration: style.duration,
      showClose: true
    })
  },
  // 成功消息 - 医疗主题
  msgSuccess(content) {
    const style = getMedicalMessageStyle('success')
    ElMessage({
      message: content,
      type: 'success',
      customClass: style.customClass,
      duration: style.duration,
      showClose: true
    })
  },
  // 警告消息 - 医疗主题
  msgWarning(content) {
    const style = getMedicalMessageStyle('warning')
    ElMessage({
      message: content,
      type: 'warning',
      customClass: style.customClass,
      duration: style.duration,
      showClose: true
    })
  },
  // 弹出提示
  alert(content) {
    ElMessageBox.alert(content, "系统提示")
  },
  // 错误提示
  alertError(content) {
    ElMessageBox.alert(content, "系统提示", { type: 'error' })
  },
  // 成功提示
  alertSuccess(content) {
    ElMessageBox.alert(content, "系统提示", { type: 'success' })
  },
  // 警告提示
  alertWarning(content) {
    ElMessageBox.alert(content, "系统提示", { type: 'warning' })
  },
  // 通知提示 - 医疗主题
  notify(content) {
    const style = getMedicalNotificationStyle('info', '系统提示')
    ElNotification({
      title: style.title,
      message: content,
      type: 'info',
      customClass: style.customClass,
      duration: style.duration,
      position: style.position
    })
  },
  // 错误通知 - 医疗主题
  notifyError(content) {
    const style = getMedicalNotificationStyle('error', '错误提示')
    ElNotification({
      title: style.title,
      message: content,
      type: 'error',
      customClass: style.customClass,
      duration: style.duration,
      position: style.position
    })
  },
  // 成功通知 - 医疗主题
  notifySuccess(content) {
    const style = getMedicalNotificationStyle('success', '操作成功')
    ElNotification({
      title: style.title,
      message: content,
      type: 'success',
      customClass: style.customClass,
      duration: style.duration,
      position: style.position
    })
  },
  // 警告通知 - 医疗主题
  notifyWarning(content) {
    const style = getMedicalNotificationStyle('warning', '警告提示')
    ElNotification({
      title: style.title,
      message: content,
      type: 'warning',
      customClass: style.customClass,
      duration: style.duration,
      position: style.position
    })
  },
  // 确认窗体
  confirm(content) {
    return ElMessageBox.confirm(content, "系统提示", {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: "warning",
    })
  },
  // 提交内容
  prompt(content) {
    return ElMessageBox.prompt(content, "系统提示", {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: "warning",
    })
  },
  // 打开遮罩层
  loading(content) {
    loadingInstance = ElLoading.service({
      lock: true,
      text: content,
      background: "rgba(0, 0, 0, 0.7)",
    })
  },
  // 关闭遮罩层
  closeLoading() {
    loadingInstance.close();
  }
}
