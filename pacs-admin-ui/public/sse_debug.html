<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSE连接调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.danger {
            background-color: #dc3545;
        }
        button.danger:hover {
            background-color: #c82333;
        }
        button.success {
            background-color: #28a745;
        }
        button.success:hover {
            background-color: #218838;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log-container {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-entry.info {
            color: #0c5460;
        }
        .log-entry.success {
            color: #155724;
        }
        .log-entry.error {
            color: #721c24;
        }
        .log-entry.warning {
            color: #856404;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .stats-table {
            width: 100%;
            border-collapse: collapse;
        }
        .stats-table th, .stats-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .stats-table th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>SSE连接调试工具</h1>
        
        <!-- 用户信息和连接状态 -->
        <div class="section">
            <h3>用户信息和连接状态</h3>
            <div class="grid">
                <div>
                    <div class="form-group">
                        <label>Token:</label>
                        <input type="text" id="token" placeholder="请输入Bearer Token">
                        <button onclick="getUserInfo()">获取用户信息</button>
                    </div>
                    <div id="userInfoStatus" class="status info" style="display: none;"></div>
                </div>
                <div>
                    <table class="stats-table">
                        <tr><th>用户ID</th><td id="userId">-</td></tr>
                        <tr><th>用户名</th><td id="username">-</td></tr>
                        <tr><th>是否连接</th><td id="isConnected">-</td></tr>
                        <tr><th>总连接数</th><td id="totalConnections">-</td></tr>
                        <tr><th>在线用户</th><td id="onlineUsers">-</td></tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- SSE连接管理 -->
        <div class="section">
            <h3>SSE连接管理</h3>
            <button onclick="connectSSE()" class="success">建立SSE连接</button>
            <button onclick="disconnectSSE()" class="danger">断开SSE连接</button>
            <button onclick="getConnectionStats()">获取连接统计</button>
            <button onclick="simulateUserOnline()">模拟当前用户上线</button>
            <div id="sseStatus" class="status info" style="display: none;"></div>
        </div>

        <!-- 通知测试 -->
        <div class="section">
            <h3>通知测试</h3>
            <div class="grid">
                <div>
                    <div class="form-group">
                        <label>通知标题:</label>
                        <input type="text" id="notificationTitle" value="测试通知">
                    </div>
                    <div class="form-group">
                        <label>通知内容:</label>
                        <textarea id="notificationContent" rows="3">这是一条测试通知消息</textarea>
                    </div>
                    <div class="form-group">
                        <label>目标用户ID (留空发送给自己):</label>
                        <input type="number" id="targetUserId" placeholder="留空发送给自己">
                    </div>
                    <button onclick="sendTestNotification()">发送测试通知</button>
                    <button onclick="broadcastNotification()" class="success">广播通知</button>
                </div>
                <div>
                    <div id="notificationStatus" class="status info" style="display: none;"></div>
                </div>
            </div>
        </div>

        <!-- 连接调试 -->
        <div class="section">
            <h3>连接调试</h3>
            <div class="form-group">
                <label>要清理连接的用户ID:</label>
                <input type="number" id="removeUserId" placeholder="输入用户ID">
                <button onclick="removeUserConnection()" class="danger">清理用户连接</button>
            </div>
            <div class="form-group">
                <label>要模拟上线的用户ID:</label>
                <input type="number" id="simulateUserId" placeholder="输入用户ID">
                <button onclick="simulateSpecificUserOnline()" class="success">模拟指定用户上线</button>
            </div>
        </div>

        <!-- 日志显示 -->
        <div class="section">
            <h3>调试日志</h3>
            <div class="log-container" id="logContainer"></div>
            <button onclick="clearLogs()" class="danger">清空日志</button>
        </div>
    </div>

    <script>
        let eventSource = null;
        let currentUserId = null;

        // 日志记录
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // 清空日志
        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
        }

        // 更新状态显示
        function updateStatus(elementId, isSuccess, message) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `status ${isSuccess ? 'success' : 'error'}`;
            element.textContent = message;
        }

        // 发起API请求
        async function makeRequest(method, url, data = null) {
            const token = document.getElementById('token').value;
            if (!token) {
                throw new Error('请先输入Token');
            }

            const options = {
                method: method,
                headers: {
                    'Authorization': 'Bearer ' + token,
                    'Content-Type': 'application/json'
                }
            };

            if (data) {
                options.body = JSON.stringify(data);
            }

            const response = await fetch(`/dev-api${url}`, options);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        }

        // 获取用户信息
        async function getUserInfo() {
            try {
                log('获取用户信息...');
                const response = await makeRequest('GET', '/test/sse/user-info');
                
                if (response.code === 200) {
                    const data = response.data;
                    currentUserId = data.userId;
                    
                    document.getElementById('userId').textContent = data.userId || '-';
                    document.getElementById('username').textContent = data.username || '-';
                    document.getElementById('isConnected').textContent = data.isConnected ? '是' : '否';
                    document.getElementById('totalConnections').textContent = data.totalConnections || 0;
                    document.getElementById('onlineUsers').textContent = data.onlineUsers ? data.onlineUsers.join(', ') : '-';
                    
                    updateStatus('userInfoStatus', true, '用户信息获取成功');
                    log(`用户信息获取成功: ID=${data.userId}, 用户名=${data.username}, 连接状态=${data.isConnected}`, 'success');
                } else {
                    throw new Error(response.msg || '获取用户信息失败');
                }
            } catch (error) {
                updateStatus('userInfoStatus', false, '获取用户信息失败: ' + error.message);
                log('获取用户信息失败: ' + error.message, 'error');
            }
        }

        // 建立SSE连接
        function connectSSE() {
            const token = document.getElementById('token').value;
            if (!token) {
                updateStatus('sseStatus', false, '请先输入Token');
                return;
            }

            if (eventSource) {
                eventSource.close();
            }

            const url = `/dev-api/consultation/reliable-notification/connect?token=${encodeURIComponent(token)}`;
            log(`建立SSE连接: ${url}`);

            eventSource = new EventSource(url, {
                withCredentials: true
            });

            eventSource.onopen = function(event) {
                updateStatus('sseStatus', true, 'SSE连接已建立');
                log('SSE连接已打开', 'success');
            };

            eventSource.addEventListener('connected', function(event) {
                log(`连接确认: ${event.data}`, 'success');
            });

            eventSource.addEventListener('consultation-notification', function(event) {
                log(`收到会诊通知: ${event.data}`, 'info');
            });

            eventSource.addEventListener('heartbeat', function(event) {
                log(`收到心跳: ${event.data}`, 'info');
            });

            eventSource.onerror = function(event) {
                updateStatus('sseStatus', false, 'SSE连接错误');
                log('SSE连接发生错误', 'error');
            };

            eventSource.onmessage = function(event) {
                log(`收到消息: ${event.data}`, 'info');
            };
        }

        // 断开SSE连接
        function disconnectSSE() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
                updateStatus('sseStatus', true, 'SSE连接已断开');
                log('SSE连接已断开', 'warning');
            } else {
                updateStatus('sseStatus', false, '没有活跃的SSE连接');
            }
        }

        // 获取连接统计
        async function getConnectionStats() {
            try {
                log('获取连接统计信息...');
                const response = await makeRequest('GET', '/test/sse/connection-stats');
                
                if (response.code === 200) {
                    log(`连接统计: ${JSON.stringify(response.data)}`, 'success');
                    updateStatus('sseStatus', true, '连接统计获取成功');
                } else {
                    throw new Error(response.msg || '获取连接统计失败');
                }
            } catch (error) {
                updateStatus('sseStatus', false, '获取连接统计失败: ' + error.message);
                log('获取连接统计失败: ' + error.message, 'error');
            }
        }

        // 发送测试通知
        async function sendTestNotification() {
            try {
                const title = document.getElementById('notificationTitle').value;
                const content = document.getElementById('notificationContent').value;
                const targetUserId = document.getElementById('targetUserId').value;

                log('发送测试通知...');

                let response;
                if (targetUserId) {
                    response = await makeRequest('POST', `/test/sse/send-to-user/${targetUserId}`, {
                        title: title,
                        content: content
                    });
                } else {
                    response = await makeRequest('POST', '/test/sse/send-to-self', {
                        title: title,
                        content: content
                    });
                }

                if (response.code === 200) {
                    updateStatus('notificationStatus', true, '测试通知发送成功');
                    log(`测试通知发送成功: ${JSON.stringify(response.data)}`, 'success');
                } else {
                    throw new Error(response.msg || '发送测试通知失败');
                }
            } catch (error) {
                updateStatus('notificationStatus', false, '发送测试通知失败: ' + error.message);
                log('发送测试通知失败: ' + error.message, 'error');
            }
        }

        // 广播通知
        async function broadcastNotification() {
            try {
                const title = document.getElementById('notificationTitle').value;
                const content = document.getElementById('notificationContent').value;

                log('广播通知...');
                const response = await makeRequest('POST', '/test/sse/broadcast', {
                    title: title,
                    content: content
                });

                if (response.code === 200) {
                    updateStatus('notificationStatus', true, '广播通知发送成功');
                    log(`广播通知发送成功: ${JSON.stringify(response.data)}`, 'success');
                } else {
                    throw new Error(response.msg || '广播通知失败');
                }
            } catch (error) {
                updateStatus('notificationStatus', false, '广播通知失败: ' + error.message);
                log('广播通知失败: ' + error.message, 'error');
            }
        }

        // 清理用户连接
        async function removeUserConnection() {
            try {
                const userId = document.getElementById('removeUserId').value;
                if (!userId) {
                    throw new Error('请输入要清理连接的用户ID');
                }

                log(`清理用户 ${userId} 的连接...`);
                const response = await makeRequest('POST', `/test/sse/remove-connection/${userId}`);

                if (response.code === 200) {
                    log(`用户连接清理成功: ${JSON.stringify(response.data)}`, 'success');
                } else {
                    throw new Error(response.msg || '清理用户连接失败');
                }
            } catch (error) {
                log('清理用户连接失败: ' + error.message, 'error');
            }
        }

        // 模拟当前用户上线
        async function simulateUserOnline() {
            try {
                if (!currentUserId) {
                    throw new Error('请先获取用户信息');
                }

                log(`模拟当前用户 ${currentUserId} 上线...`);
                const response = await makeRequest('POST', `/test/sse/simulate-online/${currentUserId}`);

                if (response.code === 200) {
                    updateStatus('sseStatus', true, '模拟用户上线成功');
                    log(`模拟用户上线成功: ${JSON.stringify(response.data)}`, 'success');
                } else {
                    throw new Error(response.msg || '模拟用户上线失败');
                }
            } catch (error) {
                updateStatus('sseStatus', false, '模拟用户上线失败: ' + error.message);
                log('模拟用户上线失败: ' + error.message, 'error');
            }
        }

        // 模拟指定用户上线
        async function simulateSpecificUserOnline() {
            try {
                const userId = document.getElementById('simulateUserId').value;
                if (!userId) {
                    throw new Error('请输入要模拟上线的用户ID');
                }

                log(`模拟用户 ${userId} 上线...`);
                const response = await makeRequest('POST', `/test/sse/simulate-online/${userId}`);

                if (response.code === 200) {
                    log(`模拟用户上线成功: ${JSON.stringify(response.data)}`, 'success');
                } else {
                    throw new Error(response.msg || '模拟用户上线失败');
                }
            } catch (error) {
                log('模拟用户上线失败: ' + error.message, 'error');
            }
        }

        // 页面加载完成后的初始化
        window.onload = function() {
            log('SSE连接调试工具已加载', 'info');
            log('请先输入Token并获取用户信息', 'info');
        };

        // 页面关闭时清理连接
        window.onbeforeunload = function() {
            if (eventSource) {
                eventSource.close();
            }
        };
    </script>
</body>
</html>
