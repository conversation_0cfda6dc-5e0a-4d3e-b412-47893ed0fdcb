<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云影像浏览</title>
</head>
<body>
    <script>
    // 获取当前页面URL参数
    function getCurrentParams() {
        const params = new URLSearchParams(window.location.search);
        const paramsObj = {};
        
        for (const [key, value] of params) {
            paramsObj[key] = value;
        }
        return paramsObj;
    }

    // 安全构建带参数的URL（支持相对路径和http/https）
    function buildUrlWithParams(baseUrl, params) {
        try {
            // 处理协议头（同时支持http和https）
            let absoluteUrl;
            if (/^https?:\/\//i.test(baseUrl)) {
                absoluteUrl = baseUrl;
            } else {
                // 转换为绝对路径（自动继承当前页面的协议）
                absoluteUrl = new URL(baseUrl, window.location.href).href;
            }

            const url = new URL(absoluteUrl);
            Object.entries(params).forEach(([key, value]) => {
                url.searchParams.append(key, value);
            });
            return url.toString();
        } catch (error) {
            console.error('URL构建失败:', error.message);
            // 回退方案：直接拼接参数
            return baseUrl + (baseUrl.includes('?') ? '&' : '?') + new URLSearchParams(params).toString();
        }
    }

    // 设备判断（保持原有逻辑）
    function detectDevice() {
        const userAgent = navigator.userAgent.toLowerCase();
        const screenWidth = window.screen.width;
        const mobileKeywords = ['mobile', 'android', 'iphone', 'ipod', 'windows phone'];
        const isTablet = /ipad|android|tablet/i.test(userAgent) && screenWidth >= 768;

        if (isTablet) return 'tablet';
        if (mobileKeywords.some(k => userAgent.includes(k)) || screenWidth < 768) return 'mobile';
        return 'desktop';
    }

    // 执行跳转
    const deviceType = detectDevice();
    const baseUrl = {
        mobile: './mobile/dicomViewer.html',  // 相对路径
        desktop: './desktop/index.html', // 相对路径
        tablet: './mobile/dicomViewer.html'  // 平板使用移动版
    };

    const currentParams = getCurrentParams();
    const targetUrl = buildUrlWithParams(baseUrl[deviceType], currentParams);
    
    // 显示调试信息
    document.write(`
        <div style="padding: 20px; font-family: Arial, sans-serif;">
            <h2>云影像浏览器 - 设备检测</h2>
            <p><strong>检测到的设备类型:</strong> ${deviceType}</p>
            <p><strong>原始参数:</strong> ${JSON.stringify(currentParams)}</p>
            <p><strong>目标URL:</strong> ${targetUrl}</p>
            <p>3秒后将自动跳转...</p>
            <button onclick="window.location.href='${targetUrl}'">立即跳转</button>
        </div>
    `);
    
    // 延迟3秒后跳转
    setTimeout(() => {
        window.location.href = targetUrl;
    }, 3000);
    </script>
</body>
</html>
