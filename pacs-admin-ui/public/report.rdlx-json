{"Name": "multisectionreport1111", "Width": "0in", "Layers": [{"Name": "default"}], "CustomProperties": [{"Name": "DisplayType", "Value": "Page"}, {"Name": "SizeType", "Value": "<PERSON><PERSON><PERSON>"}, {"Name": "CollapseWhiteSpace", "Value": "True"}], "Version": "7.6.0", "Page": {"PageWidth": "8.5in", "PageHeight": "11in", "RightMargin": "0in", "LeftMargin": "0in", "TopMargin": "0in", "BottomMargin": "0in", "Columns": 1, "ColumnSpacing": "0.5in", "PaperOrientation": "Portrait"}, "DataSources": [{"Name": "DataSource", "ConnectionProperties": {"DataProvider": "JSONEMBED", "ConnectString": "jsondata={\\n    \"hospitalName\": \"鄂托克旗人民医院\",\\n    \"reportTitle\": \"医学影像诊断报告\",\\n    \"qrCode\":\"https://\",\\n    \"patientName\": \"测试患者\",\\n    \"patientSex\": \"男\",\\n    \"diagnosis\": {\\n        \"id\": 1,\\n        \"checkId\": null,\\n        \"diagnose\": \"双肺纹理清晰，未见明显异常密度影。心影大小形态正常。纵隔居中，气管通畅。胸膜光滑，未见胸腔积液征象。\",\\n        \"doctor\": \"张医生\",\\n        \"doctorAvatar\": \"https://\",\\n        \"studyId\": \"TEST001\",\\n        \"patientId\": \"P001\",\\n        \"recommendation\": \"建议定期复查。\",\\n        \"status\": \"2\",\\n        \"diagnosisType\": \"影像诊断\",\\n        \"auditBy\": \"李主任\",\\n        \"auditAvatar\": \"https://\",\\n        \"auditTime\": \"2025-06-17 11:00:00\",\\n        \"auditComment\": null,\\n        \"positiveNegative\": \"negative\",\\n        \"pdfReportPath\": null,\\n        \"pdfStatus\": \"pending\",\\n        \"pdfGenerateTime\": null,\\n        \"createTime\": \"2025-06-17 10:30:00\",\\n        \"updateTime\": \"2025-06-17 11:00:00\"\\n    },\\n    \"study\": {\\n        \"id\": 1,\\n        \"hospitalId\": \"H001\",\\n        \"hospitalName\": \"鄂托克旗人民医院\",\\n        \"originalPatientId\": \"P001\",\\n        \"patientName\": \"测试患者\",\\n        \"originalExamCode\": \"E001\",\\n        \"examCode\": \"TEST001\",\\n        \"modality\": \"CT\",\\n        \"mobile\": \"13800138000\",\\n        \"patientSex\": \"男\",\\n        \"hisPatientId\": \"HIS001\",\\n        \"outPatientId\": \"OUT001\",\\n        \"inPatientId\": null,\\n        \"examPatientId\": \"EXAM001\",\\n        \"patientFrom\": \"门诊\",\\n        \"bedNo\": null,\\n        \"patientBirthday\": \"1978-06-17\",\\n        \"organ\": \"胸部\",\\n        \"examItem\": \"胸部CT平扫\",\\n        \"examDepartment\": \"放射科\",\\n        \"examDoctorName\": \"王医生\",\\n        \"registerTime\": \"2025-06-17 09:00:00\",\\n        \"reserveTime\": \"2025-06-17 10:00:00\",\\n        \"reserveArrivalTime\": \"2025-06-17 09:55:00\",\\n        \"checkFinishTime\": \"2025-06-17 10:30:00\",\\n        \"idNo\": \"150000197806170001\",\\n        \"socialSecurityCardNo\": \"**********\",\\n        \"medicalCardNo\": \"MC001\",\\n        \"medicalHistory\": \"无特殊病史\",\\n        \"clinicalSymptom\": \"胸闷气短\",\\n        \"clinicalDiagnosis\": \"胸部不适\",\\n        \"device\": \"CT设备001\",\\n        \"syncTime\": \"2025-06-17 10:35:00\",\\n        \"isDeleted\": 0,\\n        \"dicomSyncFlag\": \"1\",\\n        \"reportDiagnose\": \"建议定期复查。\",\\n        \"see\": \"双肺纹理清晰，未见明显异常密度影。心影大小形态正常。纵隔居中，气管通畅。胸膜光滑，未见胸腔积液征象。\",\\n        \"diagnosisStatus\": \"2\",\\n        \"age\": 45\\n    },\\n    \"generateTime\": \"2025-06-17\"\\n}\\n"}}], "ReportSections": [{"Type": "Continuous", "Name": "报表区域1", "Page": {"PageWidth": "8.27in", "PageHeight": "11.69in", "RightMargin": "0.5in", "LeftMargin": "0.5in", "TopMargin": "0.5in", "BottomMargin": "0.5in", "Columns": 1, "ColumnSpacing": "0in", "PaperOrientation": "Portrait"}, "Width": "7.25in", "Body": {"Height": "10.4791in", "ReportItems": [{"Type": "rectangle", "Name": "头部", "ReportItems": [{"Type": "textbox", "Name": "医院名称", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!hospitalName.Value", "Style": {"FontFamily": "宋体", "FontSize": "20pt", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Center"}, "Width": "7.25in", "Height": "0.5104in"}, {"Type": "textbox", "Name": "报告单类型", "ZIndex": 2, "CanGrow": true, "KeepTogether": true, "Value": "=study.modality & \"检查报告单\"", "Style": {"FontFamily": "宋体", "FontSize": "24pt", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Center"}, "Left": "0.0209in", "Top": "0.4583in", "Width": "7.2291in", "Height": "0.4271in"}, {"Type": "textbox", "Name": "检查号标题", "ZIndex": 3, "CanGrow": true, "KeepTogether": true, "Value": "检查号：", "Style": {"FontFamily": "宋体", "FontSize": "12pt", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "VerticalAlign": "Bottom"}, "Left": "0.2814in", "Top": "0.8958in", "Width": "0.8021in", "Height": "0.25in"}, {"Type": "textbox", "Name": "检查号值", "ZIndex": 4, "CanGrow": true, "KeepTogether": true, "Value": "=study.originalPatientId", "Style": {"FontFamily": "宋体", "FontSize": "12pt", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "VerticalAlign": "Bottom"}, "Left": "1.1042in", "Top": "0.8958in", "Width": "1.9479in", "Height": "0.25in"}, {"Type": "rectangle", "Name": "基本信息", "ZIndex": 5, "ReportItems": [{"Type": "line", "Name": "基本信息上", "StartPoint": {"X": "0.1042in", "Y": "0.0207in"}, "EndPoint": {"X": "7.1667in", "Y": "0.0207in"}}, {"Type": "line", "Name": "基本信息下", "ZIndex": 1, "StartPoint": {"X": "0.1042in", "Y": "1in"}, "EndPoint": {"X": "7.1667in", "Y": "1in"}}, {"Type": "textbox", "Name": "姓名", "ZIndex": 2, "CanGrow": true, "KeepTogether": true, "Value": "=\"姓名:\" & Fields.Item(\"study.patientName\").Value", "Style": {"FontFamily": "宋体", "FontSize": "11pt", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0.1181in", "Top": "0.0659in", "Width": "1.1563in", "Height": "0.25in"}, {"Type": "textbox", "Name": "性别", "ZIndex": 3, "CanGrow": true, "KeepTogether": true, "Value": "=\"性别:\" & Fields.Item(\"study.patientSex\").Value", "Style": {"FontFamily": "宋体", "FontSize": "11pt", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "1.5in", "Top": "0.0659in", "Width": "1.4479in", "Height": "0.25in"}, {"Type": "textbox", "Name": "年龄", "ZIndex": 4, "CanGrow": true, "KeepTogether": true, "Value": "=\"年龄: \" & Fields.Item(\"study.age\").Value", "Style": {"FontFamily": "宋体", "FontSize": "11pt", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "3in", "Top": "0.0659in", "Width": "1.4583in", "Height": "0.25in"}, {"Type": "textbox", "Name": "检查时间", "ZIndex": 5, "CanGrow": true, "KeepTogether": true, "Value": "=\"检查时间: \" & Fields.Item(\"study.checkFinishTime\").Value", "Style": {"FontFamily": "宋体", "FontSize": "11pt", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "4.5104in", "Top": "0.0659in", "Width": "2.5105in", "Height": "0.25in"}, {"Type": "textbox", "Name": "床号", "ZIndex": 6, "CanGrow": true, "KeepTogether": true, "Value": "=\"床号: \" & Fields.Item(\"study.bedNo\").Value", "Style": {"FontFamily": "宋体", "FontSize": "11pt", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0.1181in", "Top": "0.375in", "Width": "1.1458in", "Height": "0.25in"}, {"Type": "textbox", "Name": "门诊号", "ZIndex": 7, "CanGrow": true, "KeepTogether": true, "Value": "=\"门诊号: \" & Fields.Item(\"study.outPatientId\").Value", "Style": {"FontFamily": "宋体", "FontSize": "11pt", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "1.5in", "Top": "0.375in", "Width": "1.4376in", "Height": "0.25in"}, {"Type": "textbox", "Name": "科别", "ZIndex": 8, "CanGrow": true, "KeepTogether": true, "Value": "=\"科别: \" & Fields.Item(\"study.examDepartment\").Value", "Style": {"FontFamily": "宋体", "FontSize": "11pt", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "3in", "Top": "0.375in", "Width": "1.4584in", "Height": "0.25in"}, {"Type": "textbox", "Name": "送检医生", "ZIndex": 9, "CanGrow": true, "KeepTogether": true, "Value": "=\"送检医生:\" & Fields.Item(\"study.examDoctorName\").Value", "Style": {"FontFamily": "宋体", "FontSize": "11pt", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "4.5104in", "Top": "0.375in", "Width": "1.9584in", "Height": "0.25in"}, {"Type": "textbox", "Name": "检查部位", "ZIndex": 10, "CanGrow": true, "KeepTogether": true, "Value": "=\"检查部位: \" & Fields.Item(\"study.examItem\").Value", "Style": {"FontFamily": "宋体", "FontSize": "11pt", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0.1181in", "Top": "0.7083in", "Width": "3.948in", "Height": "0.25in"}, {"Type": "textbox", "Name": "影像号", "ZIndex": 11, "CanGrow": true, "KeepTogether": true, "Value": "=\"影像号: \" & Fields.Item(\"study.examCode\").Value", "Style": {"FontFamily": "宋体", "FontSize": "11pt", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "4.5104in", "Top": "0.7083in", "Width": "2.5001in", "Height": "0.25in"}], "Top": "1.1667in", "Width": "7.1667in", "Height": "1.052in"}, {"Type": "barcode", "Name": "云影像链接", "ZIndex": 6, "Value": "=Fields!qrCode.Value", "Symbology": "QRCode", "Style": {"FontSize": "8pt"}, "Left": "6.2917in", "Top": "0.0415in", "Width": "0.8541in", "Height": "0.8125in"}], "Width": "7.25in", "Height": "2.2187in"}, {"Type": "rectangle", "Name": "诊断", "ZIndex": 1, "ReportItems": [{"Type": "textbox", "Name": "影像所见标题", "CanGrow": true, "KeepTogether": true, "Value": "影像所见：", "Style": {"FontFamily": "宋体", "FontSize": "12pt", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0.1181in", "Width": "1in", "Height": "0.25in"}, {"Type": "textbox", "Name": "影像所见", "ZIndex": 1, "CanGrow": true, "KeepTogether": true, "Value": "=Fields.Item(\"diagnosis.diagnose\").Value", "Style": {"PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0.1181in", "Top": "0.2744in", "Width": "7.0833in", "Height": "3.198in"}, {"Type": "textbox", "Name": "影像意见标题", "ZIndex": 2, "CanGrow": true, "KeepTogether": true, "Value": "影像意见：", "Style": {"FontFamily": "宋体", "FontSize": "12pt", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0.1146in", "Top": "3.5001in", "Width": "1in", "Height": "0.25in"}, {"Type": "textbox", "Name": "影响意见内容", "ZIndex": 3, "CanGrow": true, "KeepTogether": true, "Value": "=Fields.Item(\"diagnosis.recommendation\").Value", "Style": {"PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0.1145in", "Top": "3.7708in", "Width": "7.0938in", "Height": "3.9479in"}], "Top": "2.2708in", "Width": "7.2083in", "Height": "7.7187in"}, {"Type": "rectangle", "Name": "底部", "ZIndex": 2, "ReportItems": [{"Type": "textbox", "Name": "报告医生", "CanGrow": true, "KeepTogether": true, "Value": "=\"报告医生: \" & Fields.Item(\"diagnosis.doctor\").Value", "Style": {"PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0.1041in", "Top": "0.5in", "Width": "1.8854in", "Height": "0.25in"}, {"Type": "textbox", "Name": "审核医生", "ZIndex": 1, "CanGrow": true, "KeepTogether": true, "Value": "=\"审核医生: \" & Fields.Item(\"diagnosis.auditBy\").Value", "Style": {"PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "2.4636in", "Top": "0.4792in", "Width": "2.0104in", "Height": "0.25in"}, {"Type": "textbox", "Name": "报告时间", "ZIndex": 2, "CanGrow": true, "KeepTogether": true, "Value": "=\"报告时间: \" & Fields.Item(\"diagnosis.auditTime\").Value", "Style": {"PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "4.948in", "Top": "0.4896in", "Width": "2.2291in", "Height": "0.25in"}], "Top": "9.7291in", "Width": "7.2396in", "Height": "0.75in"}]}}], "DataSets": [{"Name": "DataSet", "Fields": [{"Name": "hospitalName", "DataField": "hospitalName"}, {"Name": "reportTitle", "DataField": "reportTitle"}, {"Name": "qrCode", "DataField": "qrCode"}, {"Name": "patientName", "DataField": "patientName"}, {"Name": "patientSex", "DataField": "patientSex"}, {"Name": "diagnosis.id", "DataField": "diagnosis.id"}, {"Name": "diagnosis.checkId", "DataField": "diagnosis.checkId"}, {"Name": "diagnosis.diagnose", "DataField": "diagnosis.diagnose"}, {"Name": "diagnosis.doctor", "DataField": "diagnosis.doctor"}, {"Name": "diagnosis.doctor<PERSON><PERSON><PERSON>", "DataField": "diagnosis.doctor<PERSON><PERSON><PERSON>"}, {"Name": "diagnosis.studyId", "DataField": "diagnosis.studyId"}, {"Name": "diagnosis.patientId", "DataField": "diagnosis.patientId"}, {"Name": "diagnosis.recommendation", "DataField": "diagnosis.recommendation"}, {"Name": "diagnosis.status", "DataField": "diagnosis.status"}, {"Name": "diagnosis.diagnosisType", "DataField": "diagnosis.diagnosisType"}, {"Name": "diagnosis.auditBy", "DataField": "diagnosis.auditBy"}, {"Name": "diagnosis.auditAvatar", "DataField": "diagnosis.auditAvatar"}, {"Name": "diagnosis.auditTime", "DataField": "diagnosis.auditTime"}, {"Name": "diagnosis.auditComment", "DataField": "diagnosis.auditComment"}, {"Name": "diagnosis.positiveNegative", "DataField": "diagnosis.positiveNegative"}, {"Name": "diagnosis.pdfReportPath", "DataField": "diagnosis.pdfReportPath"}, {"Name": "diagnosis.pdfStatus", "DataField": "diagnosis.pdfStatus"}, {"Name": "diagnosis.pdfGenerateTime", "DataField": "diagnosis.pdfGenerateTime"}, {"Name": "diagnosis.createTime", "DataField": "diagnosis.createTime"}, {"Name": "diagnosis.updateTime", "DataField": "diagnosis.updateTime"}, {"Name": "study.id", "DataField": "study.id"}, {"Name": "study.hospitalId", "DataField": "study.hospitalId"}, {"Name": "study.hospitalName", "DataField": "study.hospitalName"}, {"Name": "study.originalPatientId", "DataField": "study.originalPatientId"}, {"Name": "study.patientName", "DataField": "study.patientName"}, {"Name": "study.originalExamCode", "DataField": "study.originalExamCode"}, {"Name": "study.examCode", "DataField": "study.examCode"}, {"Name": "study.modality", "DataField": "study.modality"}, {"Name": "study.mobile", "DataField": "study.mobile"}, {"Name": "study.patientSex", "DataField": "study.patientSex"}, {"Name": "study.hisPatientId", "DataField": "study.hisPatientId"}, {"Name": "study.outPatientId", "DataField": "study.outPatientId"}, {"Name": "study.inPatientId", "DataField": "study.inPatientId"}, {"Name": "study.examPatientId", "DataField": "study.examPatientId"}, {"Name": "study.patientFrom", "DataField": "study.patientFrom"}, {"Name": "study.bedNo", "DataField": "study.bedNo"}, {"Name": "study.patientBirthday", "DataField": "study.patientBirthday"}, {"Name": "study.organ", "DataField": "study.organ"}, {"Name": "study.examItem", "DataField": "study.examItem"}, {"Name": "study.examDepartment", "DataField": "study.examDepartment"}, {"Name": "study.examDoctorName", "DataField": "study.examDoctorName"}, {"Name": "study.registerTime", "DataField": "study.registerTime"}, {"Name": "study.reserveTime", "DataField": "study.reserveTime"}, {"Name": "study.reserveArrivalTime", "DataField": "study.reserveArrivalTime"}, {"Name": "study.checkFinishTime", "DataField": "study.checkFinishTime"}, {"Name": "study.idNo", "DataField": "study.idNo"}, {"Name": "study.socialSecurityCardNo", "DataField": "study.socialSecurityCardNo"}, {"Name": "study.medicalCardNo", "DataField": "study.medicalCardNo"}, {"Name": "study.medicalHistory", "DataField": "study.medicalHistory"}, {"Name": "study.clinicalSymptom", "DataField": "study.clinicalSymptom"}, {"Name": "study.clinicalDiagnosis", "DataField": "study.clinicalDiagnosis"}, {"Name": "study.device", "DataField": "study.device"}, {"Name": "study.syncTime", "DataField": "study.syncTime"}, {"Name": "study.isDeleted", "DataField": "study.isDeleted"}, {"Name": "study.dicomSyncFlag", "DataField": "study.dicomSyncFlag"}, {"Name": "study.reportDiagnose", "DataField": "study.reportDiagnose"}, {"Name": "study.see", "DataField": "study.see"}, {"Name": "study.diagnosisStatus", "DataField": "study.diagnosisStatus"}, {"Name": "study.age", "DataField": "study.age"}, {"Name": "generateTime", "DataField": "generateTime"}], "Query": {"DataSourceName": "DataSource", "CommandText": "jpath=$"}, "CaseSensitivity": "Auto", "KanatypeSensitivity": "Auto", "AccentSensitivity": "Auto", "WidthSensitivity": "Auto"}]}