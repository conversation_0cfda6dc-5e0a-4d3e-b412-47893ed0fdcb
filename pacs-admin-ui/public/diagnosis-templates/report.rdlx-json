{"Name": "DiagnosisReport", "DataSources": [{"Name": "JsonDataSource", "ConnectionProperties": {"DataProvider": "JSON", "ConnectString": "jsondata="}}], "Body": {"ReportItems": [{"Type": "textbox", "Name": "HospitalName", "Value": "=Fields!hospitalName.Value", "Style": {"FontSize": "18pt", "FontWeight": "Bold", "TextAlign": "Center", "FontFamily": "Sim<PERSON>un"}, "Top": "0.2in", "Left": "0.5in", "Width": "7in", "Height": "0.4in"}, {"Type": "textbox", "Name": "ReportTitle", "Value": "=Fields!reportTitle.Value", "Style": {"FontSize": "16pt", "FontWeight": "Bold", "TextAlign": "Center", "FontFamily": "Sim<PERSON>un"}, "Top": "0.7in", "Left": "0.5in", "Width": "7in", "Height": "0.3in"}, {"Type": "textbox", "Name": "ExamNumber", "Value": "=\"检查号：\" + Fields!study.examCode.Value", "Style": {"FontSize": "12pt", "FontFamily": "Sim<PERSON>un", "TextAlign": "Center"}, "Top": "1.1in", "Left": "0.5in", "Width": "7in", "Height": "0.25in"}, {"Type": "line", "Name": "Header<PERSON><PERSON>", "Top": "1.4in", "Left": "0.5in", "Width": "7in", "Height": "0in", "Style": {"BorderStyle": "Solid", "BorderWidth": "1pt"}}, {"Type": "textbox", "Name": "PatientInfo", "Value": "=\"患者姓名：\" + Fields!patientName.Value + \"  性别：\" + Fields!patientSex.Value + \"  年龄：\" + Fields!study.age.Value", "Style": {"FontSize": "12pt", "FontFamily": "Sim<PERSON>un"}, "Top": "1.6in", "Left": "0.5in", "Width": "7in", "Height": "0.25in"}, {"Type": "textbox", "Name": "ExamInfo", "Value": "=\"检查日期：\" + Fields!study.registerTime.Value + \"  检查部位：\" + Fields!study.organ.Value", "Style": {"FontSize": "12pt", "FontFamily": "Sim<PERSON>un"}, "Top": "1.9in", "Left": "0.5in", "Width": "7in", "Height": "0.25in"}, {"Type": "textbox", "Name": "ExamDetails", "Value": "=\"检查项目：\" + Fields!study.examItem.Value + \"  检查方式：\" + Fields!study.modality.Value", "Style": {"FontSize": "12pt", "FontFamily": "Sim<PERSON>un"}, "Top": "2.15in", "Left": "0.5in", "Width": "7in", "Height": "0.25in"}, {"Type": "line", "Name": "PatientInfoLine", "Top": "2.45in", "Left": "0.5in", "Width": "7in", "Height": "0in", "Style": {"BorderStyle": "Solid", "BorderWidth": "1pt"}}, {"Type": "textbox", "Name": "FindingsTitle", "Value": "影像所见：", "Style": {"FontSize": "14pt", "FontWeight": "Bold", "FontFamily": "Sim<PERSON>un"}, "Top": "2.65in", "Left": "0.5in", "Width": "7in", "Height": "0.3in"}, {"Type": "textbox", "Name": "FindingsContent", "Value": "=Fields!study.see.Value", "Style": {"FontSize": "12pt", "FontFamily": "Sim<PERSON>un", "VerticalAlign": "Top"}, "Top": "3.05in", "Left": "0.5in", "Width": "7in", "Height": "1.5in", "CanGrow": true}, {"Type": "textbox", "Name": "RecommendationTitle", "Value": "建议：", "Style": {"FontSize": "14pt", "FontWeight": "Bold", "FontFamily": "Sim<PERSON>un"}, "Top": "4.5in", "Left": "0.5in", "Width": "7in", "Height": "0.3in"}, {"Type": "textbox", "Name": "RecommendationContent", "Value": "=Fields!study.reportDiagnose.Value", "Style": {"FontSize": "12pt", "FontFamily": "Sim<PERSON>un", "VerticalAlign": "Top"}, "Top": "4.9in", "Left": "0.5in", "Width": "7in", "Height": "1in", "CanGrow": true}, {"Type": "line", "Name": "SignatureLine", "Top": "6.2in", "Left": "0.5in", "Width": "7in", "Height": "0in", "Style": {"BorderStyle": "Solid", "BorderWidth": "1pt"}}, {"Type": "textbox", "Name": "ReportDoctor", "Value": "=\"报告医生：\" + Fields!diagnosis.doctor.Value", "Style": {"FontSize": "12pt", "FontFamily": "Sim<PERSON>un"}, "Top": "6.5in", "Left": "0.5in", "Width": "2.5in", "Height": "0.25in"}, {"Type": "textbox", "Name": "AuditDoctor", "Value": "=\"审核医生：\" + Fields!diagnosis.auditBy.Value", "Style": {"FontSize": "12pt", "FontFamily": "Sim<PERSON>un"}, "Top": "6.5in", "Left": "3in", "Width": "2.5in", "Height": "0.25in"}, {"Type": "textbox", "Name": "ReportTime", "Value": "=\"报告时间：\" + Fields!diagnosis.createTime.Value", "Style": {"FontSize": "12pt", "FontFamily": "Sim<PERSON>un"}, "Top": "6.8in", "Left": "0.5in", "Width": "7in", "Height": "0.25in"}]}, "PageHeader": {"Height": "0in"}, "PageFooter": {"Height": "0.5in", "ReportItems": [{"Type": "textbox", "Name": "GenerateTime", "Value": "=\"生成时间：\" + Fields!generateTime.Value", "Style": {"FontSize": "10pt", "FontFamily": "Sim<PERSON>un", "TextAlign": "Center"}, "Top": "0.1in", "Left": "0.5in", "Width": "7in", "Height": "0.2in"}]}}