-- 诊断模板表
CREATE TABLE diagnosis_template (
  id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  name varchar(100) NOT NULL COMMENT '模板名称',
  title varchar(200) DEFAULT NULL COMMENT '诊断标题',
  content text DEFAULT NULL COMMENT '诊断内容',
  conclusion text DEFAULT NULL COMMENT '诊断结论',
  remark varchar(500) DEFAULT NULL COMMENT '备注信息',
  modality_type varchar(50) DEFAULT NULL COMMENT '适用检查类型(CT/MRI等)',
  body_part varchar(100) DEFAULT NULL COMMENT '适用身体部位',
  is_public char(1) DEFAULT '0' COMMENT '是否公开(0私有 1公开)',
  create_by varchar(64) DEFAULT '' COMMENT '创建者',
  create_time datetime DEFAULT NULL COMMENT '创建时间',
  update_by varchar(64) DEFAULT '' COMMENT '更新者',
  update_time datetime DEFAULT NULL COMMENT '更新时间',
  del_flag char(1) DEFAULT '0' COMMENT '删除标志(0存在 1删除)',
  PRIMARY KEY (id)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='诊断模板表'; 