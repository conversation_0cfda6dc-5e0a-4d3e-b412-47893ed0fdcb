<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="renderer" content="webkit">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
  <link rel="icon" href="/favicon.ico">
  <title>云影像系统</title>
  <!--[if lt IE 11]><script>window.location.href='/html/ie.html';</script><![endif]-->
  <style>
    html,
    body,
    #app {
      height: 100%;
      margin: 0px;
      padding: 0px;
      overflow: hidden;
    }

    .chromeframe {
      margin: 0.2em 0;
      background: #ccc;
      color: #000;
      padding: 0.2em 0;
    }

    /* 主加载容器 */
    #loader-wrapper {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999999;
      background: linear-gradient(135deg, #f8fafb 0%, #ebf2ff 50%, #f0f7ff 100%);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    /* PACS系统Logo */
    .pacs-logo {
      position: absolute;
      top: 30%;
      transform: translateY(-50%);
      display: flex;
      flex-direction: column;
      align-items: center;
      z-index: 1003;
    }

    .logo-icon {
      width: 80px;
      height: 80px;
      background: linear-gradient(135deg, #2563eb 0%, #3b82f6 50%, #60a5fa 100%);
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 24px;
      box-shadow: 
        0 20px 40px rgba(37, 99, 235, 0.15),
        0 8px 16px rgba(37, 99, 235, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
      animation: logoFloat 3s ease-in-out infinite;
      position: relative;
    }

    .logo-icon::before {
      content: '';
      position: absolute;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
      border-radius: 20px;
      animation: logoShine 4s ease-in-out infinite;
    }

    .logo-icon svg {
      width: 48px;
      height: 48px;
      fill: white;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    }

    .system-title {
      font-family: 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', sans-serif;
      font-size: 28px;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 8px;
      letter-spacing: 0.5px;
      text-align: center;
    }

    .system-subtitle {
      font-family: 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', sans-serif;
      font-size: 14px;
      color: #64748b;
      font-weight: 400;
      letter-spacing: 1px;
      text-transform: uppercase;
    }

    /* 优雅的加载动画 */
    .loader-container {
      position: absolute;
      top: 60%;
      transform: translateY(-50%);
      display: flex;
      flex-direction: column;
      align-items: center;
      z-index: 1002;
    }

    .elegant-loader {
      width: 60px;
      height: 60px;
      position: relative;
      margin-bottom: 32px;
    }

    .elegant-loader::before {
      content: '';
      position: absolute;
      width: 100%;
      height: 100%;
      border: 3px solid #e2e8f0;
      border-radius: 50%;
    }

    .elegant-loader::after {
      content: '';
      position: absolute;
      width: 100%;
      height: 100%;
      border: 3px solid transparent;
      border-top-color: #2563eb;
      border-right-color: #3b82f6;
      border-radius: 50%;
      animation: elegantSpin 1.2s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
    }

    .loading-dots {
      display: flex;
      gap: 8px;
      margin-bottom: 16px;
    }

    .dot {
      width: 8px;
      height: 8px;
      background: #2563eb;
      border-radius: 50%;
      animation: dotPulse 1.4s ease-in-out infinite;
    }

    .dot:nth-child(2) {
      animation-delay: 0.2s;
      background: #3b82f6;
    }

    .dot:nth-child(3) {
      animation-delay: 0.4s;
      background: #60a5fa;
    }

    .loading-text {
      font-family: 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', sans-serif;
      font-size: 16px;
      color: #475569;
      font-weight: 500;
      opacity: 0.8;
      animation: textFade 2s ease-in-out infinite;
    }

    /* 背景装饰元素 */
    .background-decoration {
      position: absolute;
      width: 100%;
      height: 100%;
      overflow: hidden;
      z-index: 1000;
    }

    .decoration-circle {
      position: absolute;
      border-radius: 50%;
      background: linear-gradient(135deg, rgba(37, 99, 235, 0.05) 0%, rgba(59, 130, 246, 0.03) 100%);
      animation: floatUp 8s ease-in-out infinite;
    }

    .decoration-circle:nth-child(1) {
      width: 120px;
      height: 120px;
      top: 10%;
      left: 15%;
      animation-delay: 0s;
    }

    .decoration-circle:nth-child(2) {
      width: 80px;
      height: 80px;
      top: 20%;
      right: 20%;
      animation-delay: 1s;
    }

    .decoration-circle:nth-child(3) {
      width: 100px;
      height: 100px;
      bottom: 15%;
      left: 10%;
      animation-delay: 2s;
    }

    .decoration-circle:nth-child(4) {
      width: 60px;
      height: 60px;
      bottom: 25%;
      right: 15%;
      animation-delay: 3s;
    }

    /* 动画定义 */
    @keyframes logoFloat {
      0%, 100% {
        transform: translateY(0px);
      }
      50% {
        transform: translateY(-8px);
      }
    }

    @keyframes logoShine {
      0%, 100% {
        opacity: 0;
        transform: translateX(-100%);
      }
      50% {
        opacity: 1;
        transform: translateX(100%);
      }
    }

    @keyframes elegantSpin {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }

    @keyframes dotPulse {
      0%, 80%, 100% {
        transform: scale(1);
        opacity: 0.5;
      }
      40% {
        transform: scale(1.2);
        opacity: 1;
      }
    }

    @keyframes textFade {
      0%, 100% {
        opacity: 0.6;
      }
      50% {
        opacity: 1;
      }
    }

    @keyframes floatUp {
      0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.3;
      }
      50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 0.6;
      }
    }

    /* 加载完成动画 */
    .loaded #loader-wrapper {
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.5s ease-out, visibility 0.5s ease-out;
    }

    .loaded .pacs-logo {
      transform: translateY(-100px);
      opacity: 0;
      transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .loaded .loader-container {
      transform: translateY(100px);
      opacity: 0;
      transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.1s;
    }

    .no-js #loader-wrapper {
      display: none;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .system-title {
        font-size: 24px;
      }
      
      .logo-icon {
        width: 70px;
        height: 70px;
        margin-bottom: 20px;
      }
      
      .logo-icon svg {
        width: 40px;
        height: 40px;
      }
      
      .elegant-loader {
        width: 50px;
        height: 50px;
      }
    }
  </style>
</head>

<body>
  <div id="app">
    <div id="loader-wrapper">
      <!-- 背景装饰元素 -->
      <div class="background-decoration">
        <div class="decoration-circle"></div>
        <div class="decoration-circle"></div>
        <div class="decoration-circle"></div>
        <div class="decoration-circle"></div>
      </div>
      
      <!-- PACS系统Logo -->
      <div class="pacs-logo">
        <div class="logo-icon">
          <svg viewBox="0 0 24 24">
            <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
          </svg>
        </div>
        <div class="system-title">PACS医疗影像系统</div>
        <div class="system-subtitle">Picture Archiving Communication System</div>
      </div>
      
      <!-- 加载动画容器 -->
      <div class="loader-container">
        <div class="elegant-loader"></div>
        <div class="loading-dots">
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
        </div>
        <div class="loading-text">系统初始化中...</div>
      </div>
    </div>
  </div>
  <script type="module" src="/src/main.js"></script>
</body>

</html>