# 系统登录优化配置方案

## 🎯 优化目标
解决生产环境偶发登录报错问题，提升系统稳定性和性能。

## 📋 配置优化清单

### 1. Redis连接池优化

#### 当前配置问题分析
```yaml
# 当前配置 - 可能不足以应对高并发
redis:
  lettuce:
    pool:
      min-idle: 0      # 太小，可能导致连接创建延迟
      max-idle: 8      # 偏小
      max-active: 8    # 偏小，高并发时可能不够
      max-wait: -1ms   # 无限等待可能导致请求堆积
```

#### 优化后配置
```yaml
spring:
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
      password:
      timeout: 30s                    # 增加超时时间
      connect-timeout: 10s            # 连接超时
      lettuce:
        pool:
          min-idle: 5                 # 保持最小连接数
          max-idle: 20                # 增加最大空闲连接
          max-active: 50              # 增加最大活跃连接
          max-wait: 5s                # 设置合理等待时间
        shutdown-timeout: 100ms
      # 添加集群配置（如果使用Redis集群）
      cluster:
        max-redirects: 3
```

### 2. 数据库连接池优化

#### Druid连接池优化
```yaml
spring:
  datasource:
    druid:
      master:
        # 基础连接配置
        initialSize: 10               # 增加初始连接数
        minIdle: 15                   # 增加最小空闲连接
        maxActive: 100                # 增加最大连接数
        maxWait: 60000               # 最大等待时间60秒
        
        # 连接有效性检测
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        validationQuery: SELECT 1
        validationQueryTimeout: 30
        
        # 连接回收配置
        timeBetweenEvictionRunsMillis: 60000    # 60秒检测一次
        minEvictableIdleTimeMillis: 300000      # 5分钟空闲后回收
        maxEvictableIdleTimeMillis: 900000      # 15分钟强制回收
        
        # 连接泄漏检测
        removeAbandoned: true
        removeAbandonedTimeout: 1800            # 30分钟
        logAbandoned: true
        
        # 监控配置
        filters: stat,wall,log4j2
        connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=2000
        
        # 防火墙配置
        wall:
          config:
            multi-statement-allow: true
            none-base-statement-allow: true
```

### 3. JVM参数优化

#### 推荐JVM启动参数
```bash
# 内存配置
-Xms2g -Xmx4g
-XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m
-XX:NewRatio=2

# GC配置
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:G1HeapRegionSize=16m
-XX:+G1UseAdaptiveIHOP
-XX:G1MixedGCCountTarget=8

# GC日志
-XX:+PrintGC
-XX:+PrintGCDetails
-XX:+PrintGCTimeStamps
-XX:+PrintGCApplicationStoppedTime
-Xloggc:/path/to/gc.log
-XX:+UseGCLogFileRotation
-XX:NumberOfGCLogFiles=5
-XX:GCLogFileSize=100M

# 其他优化
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=/path/to/heapdump/
-XX:+DisableExplicitGC
-Djava.awt.headless=true
-Dfile.encoding=UTF-8
-Duser.timezone=GMT+08
```

### 4. Tomcat线程池优化

#### application.yml配置
```yaml
server:
  port: 8080
  tomcat:
    uri-encoding: UTF-8
    accept-count: 2000              # 增加排队数
    max-connections: 10000          # 增加最大连接数
    threads:
      max: 1000                     # 增加最大线程数
      min-spare: 200                # 增加最小空闲线程数
    connection-timeout: 30000       # 连接超时30秒
    keep-alive-timeout: 60000       # Keep-Alive超时60秒
    max-keep-alive-requests: 1000   # 最大Keep-Alive请求数
```

### 5. Token服务优化

#### 增强Token服务异常处理
```java
@Component
public class EnhancedTokenService extends TokenService {
    
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long RETRY_DELAY_MS = 100;
    
    @Override
    public LoginUser getLoginUser(HttpServletRequest request) {
        String token = getToken(request);
        if (StringUtils.isEmpty(token)) {
            return null;
        }
        
        // 添加重试机制
        for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {
            try {
                Claims claims = parseToken(token);
                String uuid = (String) claims.get(Constants.LOGIN_USER_KEY);
                String userKey = getTokenKey(uuid);
                
                LoginUser user = redisCache.getCacheObject(userKey);
                if (user != null) {
                    return user;
                }
                
                log.warn("Token对应的用户信息不存在，尝试次数: {}", attempt);
                
            } catch (ExpiredJwtException e) {
                log.warn("Token已过期: {}", e.getMessage());
                return null;
            } catch (JwtException e) {
                log.error("Token解析失败，尝试次数: {}, 错误: {}", attempt, e.getMessage());
                return null;
            } catch (Exception e) {
                log.error("获取用户信息异常，尝试次数: {}, 错误: {}", attempt, e.getMessage(), e);
                
                if (attempt < MAX_RETRY_ATTEMPTS) {
                    try {
                        Thread.sleep(RETRY_DELAY_MS * attempt);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        return null;
                    }
                } else {
                    // 最后一次尝试失败，记录详细错误信息
                    log.error("获取用户信息最终失败，Token: {}", token.substring(0, Math.min(token.length(), 20)) + "...");
                }
            }
        }
        
        return null;
    }
}
```

### 6. 登录服务优化

#### 增强登录异常处理
```java
@Component
public class EnhancedSysLoginService extends SysLoginService {
    
    @Override
    public String login(String username, String password, String code, String uuid) {
        try {
            // 添加登录前的系统状态检查
            checkSystemHealth();
            
            // 验证码校验
            validateCaptcha(username, code, uuid);
            
            // 登录前置校验
            loginPreCheck(username, password);
            
            // 用户验证
            Authentication authentication = authenticateUser(username, password);
            
            // 记录成功日志
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, 
                Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
            
            LoginUser loginUser = (LoginUser) authentication.getPrincipal();
            recordLoginInfo(loginUser.getUserId());
            
            // 生成token
            return tokenService.createToken(loginUser);
            
        } catch (Exception e) {
            // 详细的异常处理和日志记录
            handleLoginException(username, e);
            throw e;
        }
    }
    
    private void checkSystemHealth() {
        // 检查Redis连接
        try {
            redisCache.hasKey("health_check");
        } catch (Exception e) {
            log.error("Redis连接检查失败", e);
            throw new ServiceException("系统暂时不可用，请稍后重试");
        }
        
        // 检查数据库连接
        try {
            userService.checkDatabaseConnection();
        } catch (Exception e) {
            log.error("数据库连接检查失败", e);
            throw new ServiceException("系统暂时不可用，请稍后重试");
        }
    }
    
    private void handleLoginException(String username, Exception e) {
        String errorMsg = "登录异常";
        
        if (e instanceof BadCredentialsException) {
            errorMsg = "用户名或密码错误";
        } else if (e instanceof ServiceException) {
            errorMsg = e.getMessage();
        } else if (e instanceof DataAccessException) {
            errorMsg = "数据库访问异常";
            log.error("数据库访问异常，用户: {}", username, e);
        } else if (e.getCause() instanceof ConnectException) {
            errorMsg = "网络连接异常";
            log.error("网络连接异常，用户: {}", username, e);
        } else {
            log.error("未知登录异常，用户: {}", username, e);
        }
        
        // 记录失败日志
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, 
            Constants.LOGIN_FAIL, errorMsg));
    }
}
```

### 7. 监控和告警配置

#### 健康检查端点
```java
@RestController
@RequestMapping("/actuator/health")
public class HealthCheckController {
    
    @Autowired
    private RedisCache redisCache;
    
    @Autowired
    private DataSource dataSource;
    
    @GetMapping("/custom")
    public Map<String, Object> customHealthCheck() {
        Map<String, Object> health = new HashMap<>();
        
        // Redis健康检查
        health.put("redis", checkRedisHealth());
        
        // 数据库健康检查
        health.put("database", checkDatabaseHealth());
        
        // JVM健康检查
        health.put("jvm", checkJvmHealth());
        
        return health;
    }
    
    private Map<String, Object> checkRedisHealth() {
        Map<String, Object> redis = new HashMap<>();
        try {
            redisCache.hasKey("health_check");
            redis.put("status", "UP");
            redis.put("details", "Redis连接正常");
        } catch (Exception e) {
            redis.put("status", "DOWN");
            redis.put("details", "Redis连接异常: " + e.getMessage());
        }
        return redis;
    }
    
    private Map<String, Object> checkDatabaseHealth() {
        Map<String, Object> db = new HashMap<>();
        try (Connection conn = dataSource.getConnection()) {
            db.put("status", "UP");
            db.put("details", "数据库连接正常");
        } catch (Exception e) {
            db.put("status", "DOWN");
            db.put("details", "数据库连接异常: " + e.getMessage());
        }
        return db;
    }
    
    private Map<String, Object> checkJvmHealth() {
        Map<String, Object> jvm = new HashMap<>();
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        double memoryUsage = (double) usedMemory / maxMemory * 100;
        
        jvm.put("memoryUsage", String.format("%.2f%%", memoryUsage));
        jvm.put("status", memoryUsage < 80 ? "UP" : "WARNING");
        
        return jvm;
    }
}
```

## 🚀 部署建议

### 1. 分阶段部署
1. **第一阶段**: 优化配置参数（Redis、数据库连接池）
2. **第二阶段**: 升级JVM参数和Tomcat配置
3. **第三阶段**: 部署增强的异常处理代码
4. **第四阶段**: 添加监控和告警

### 2. 回滚计划
- 保留原始配置文件备份
- 准备快速回滚脚本
- 监控关键指标变化

### 3. 验证方案
- 压力测试验证
- 监控指标对比
- 用户反馈收集

这些优化配置应该能够显著改善系统的稳定性和性能，减少偶发的登录报错问题。建议按照分阶段的方式逐步实施，并密切监控系统表现。
