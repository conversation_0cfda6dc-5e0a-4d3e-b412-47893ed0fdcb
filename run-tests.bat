@echo off
echo ========================================
echo 运行Spring Boot测试
echo ========================================

cd ruoyi-admin

echo 清理之前的构建...
call mvn clean

echo 运行所有测试...
call mvn test

echo 运行特定的AI测试...
call mvn test -Dtest=com.ruoyi.ai.**.*Test

echo 生成测试报告...
call mvn surefire-report:report

echo ========================================
echo 测试完成！
echo 测试报告位置: ruoyi-admin/target/site/surefire-report.html
echo ========================================

pause
