# 修复后的 nginx 配置文件
# 主要修复 8888 端口的 /admin 路径配置问题

#user  nobody;
worker_processes  1;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    keepalive_timeout  65;

    # 90 端口配置保持不变
    server {
        listen       90;
        server_name  localhost;

        location /admin {
            root   admin;
            index  index.html index.htm;
        }
		
        location /report/ {
            root           report;
            add_header 'Access-Control-Allow-Origin' '*';
        }

        location /api/ {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://localhost:8080/api/;
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
	
    # 8888 端口配置 - 修复版本
    server {
        listen       8888;
        server_name  eqq.natapp4.cc;

        # 修复：管理后台配置
        # 方案1：使用 alias（推荐）
        location /admin/ {
            alias E:/dcm/nginx-1.21.6/admin/;  # 修复：使用完整路径和 alias
            index index.html index.htm;
            try_files $uri $uri/ /admin/index.html;
        }
        
        # 精确匹配 /admin，重定向到 /admin/
        location = /admin {
            return 301 /admin/;
        }
        
        # 根路径处理
        location / {
            root   html;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
        }
        
        # API 代理
        location /api/ {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            # 确保不强制 HTTPS
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_pass http://localhost:8080/api/;
        }
        
        # DCM4CHEE 代理
        location /dcm4chee-arc/ {
            proxy_pass http://localhost:8088/dcm4chee-arc/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # CORS 支持
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
        }
    }
}

# 备选方案：如果不知道确切的文件路径，使用 root 方式
# 将上面的 location /admin/ 替换为：
#
# location /admin/ {
#     root E:/dcm/nginx-1.21.6/;  # 注意：这里是父目录
#     index index.html index.htm;
#     try_files $uri $uri/ /admin/index.html;
# }
