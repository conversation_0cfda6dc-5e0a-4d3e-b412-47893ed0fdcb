package com.ruoyi.framework.config;

import com.ruoyi.common.core.redis.RedisCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.OperatingSystemMXBean;
import java.lang.management.ThreadMXBean;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 系统监控配置
 */
@Configuration
@EnableScheduling
@Slf4j
public class SystemMonitoringConfig {

    /**
     * 自定义健康检查指示器
     */
    @Component
    public static class CustomHealthIndicator implements HealthIndicator {

        @Autowired
        private RedisCache redisCache;

        @Autowired
        private DataSource dataSource;

        @Override
        public Health health() {
            Map<String, Object> details = new HashMap<>();
            boolean isHealthy = true;

            // Redis健康检查
            try {
                redisCache.hasKey("health_check");
                details.put("redis", "UP");
            } catch (Exception e) {
                details.put("redis", "DOWN - " + e.getMessage());
                isHealthy = false;
            }

            // 数据库健康检查
            try (Connection conn = dataSource.getConnection()) {
                details.put("database", "UP");
            } catch (SQLException e) {
                details.put("database", "DOWN - " + e.getMessage());
                isHealthy = false;
            }

            // JVM内存检查
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            long usedMemory = memoryBean.getHeapMemoryUsage().getUsed();
            long maxMemory = memoryBean.getHeapMemoryUsage().getMax();
            double memoryUsage = (double) usedMemory / maxMemory * 100;
            
            details.put("memory_usage", String.format("%.2f%%", memoryUsage));
            if (memoryUsage > 90) {
                details.put("memory_status", "CRITICAL");
                isHealthy = false;
            } else if (memoryUsage > 80) {
                details.put("memory_status", "WARNING");
            } else {
                details.put("memory_status", "OK");
            }

            return isHealthy ? Health.up().withDetails(details).build() 
                            : Health.down().withDetails(details).build();
        }
    }

    /**
     * 系统监控服务
     */
    @Component
    public static class SystemMonitorService {

        @Autowired
        private RedisCache redisCache;

        @Autowired
        private DataSource dataSource;

        private final OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
        private final MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        private final ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();

        private static final String MONITOR_KEY_PREFIX = "system_monitor:";
        private static final String ALERT_COOLDOWN_KEY = "alert_cooldown:";

        /**
         * 每分钟执行系统监控
         */
        @Scheduled(fixedRate = 60000) // 每分钟执行一次
        public void monitorSystem() {
            try {
                Map<String, Object> metrics = collectSystemMetrics();
                
                // 存储监控数据
                String metricsKey = MONITOR_KEY_PREFIX + System.currentTimeMillis();
                redisCache.setCacheObject(metricsKey, metrics, 24, TimeUnit.HOURS);
                
                // 检查告警条件
                checkAlertConditions(metrics);
                
                // 清理过期的监控数据
                cleanupOldMetrics();
                
            } catch (Exception e) {
                log.error("系统监控执行失败", e);
            }
        }

        /**
         * 收集系统指标
         */
        private Map<String, Object> collectSystemMetrics() {
            Map<String, Object> metrics = new HashMap<>();
            
            // CPU指标
            double systemLoad = osBean.getSystemLoadAverage();
            int availableProcessors = osBean.getAvailableProcessors();
            metrics.put("cpu_load", systemLoad);
            metrics.put("cpu_cores", availableProcessors);
            metrics.put("cpu_usage_percent", systemLoad / availableProcessors * 100);
            
            // 内存指标
            long usedMemory = memoryBean.getHeapMemoryUsage().getUsed();
            long maxMemory = memoryBean.getHeapMemoryUsage().getMax();
            long freeMemory = maxMemory - usedMemory;
            metrics.put("memory_used", usedMemory);
            metrics.put("memory_max", maxMemory);
            metrics.put("memory_free", freeMemory);
            metrics.put("memory_usage_percent", (double) usedMemory / maxMemory * 100);
            
            // 线程指标
            int threadCount = threadBean.getThreadCount();
            int peakThreadCount = threadBean.getPeakThreadCount();
            metrics.put("thread_count", threadCount);
            metrics.put("thread_peak", peakThreadCount);
            
            // Redis指标
            try {
                // 这里可以添加Redis特定的监控指标
                redisCache.hasKey("monitor_test");
                metrics.put("redis_status", "UP");
            } catch (Exception e) {
                metrics.put("redis_status", "DOWN");
                metrics.put("redis_error", e.getMessage());
            }
            
            // 数据库指标
            try (Connection conn = dataSource.getConnection()) {
                metrics.put("database_status", "UP");
                // 可以添加更多数据库相关指标
            } catch (SQLException e) {
                metrics.put("database_status", "DOWN");
                metrics.put("database_error", e.getMessage());
            }
            
            metrics.put("timestamp", System.currentTimeMillis());
            return metrics;
        }

        /**
         * 检查告警条件
         */
        private void checkAlertConditions(Map<String, Object> metrics) {
            // CPU使用率告警
            Double cpuUsage = (Double) metrics.get("cpu_usage_percent");
            if (cpuUsage != null && cpuUsage > 80) {
                sendAlert("CPU_HIGH", String.format("CPU使用率过高: %.2f%%", cpuUsage), "HIGH");
            }
            
            // 内存使用率告警
            Double memoryUsage = (Double) metrics.get("memory_usage_percent");
            if (memoryUsage != null && memoryUsage > 85) {
                String severity = memoryUsage > 95 ? "CRITICAL" : "HIGH";
                sendAlert("MEMORY_HIGH", String.format("内存使用率过高: %.2f%%", memoryUsage), severity);
            }
            
            // 线程数告警
            Integer threadCount = (Integer) metrics.get("thread_count");
            if (threadCount != null && threadCount > 500) {
                sendAlert("THREAD_HIGH", String.format("线程数过多: %d", threadCount), "MEDIUM");
            }
            
            // Redis状态告警
            String redisStatus = (String) metrics.get("redis_status");
            if ("DOWN".equals(redisStatus)) {
                String error = (String) metrics.get("redis_error");
                sendAlert("REDIS_DOWN", "Redis连接失败: " + error, "CRITICAL");
            }
            
            // 数据库状态告警
            String dbStatus = (String) metrics.get("database_status");
            if ("DOWN".equals(dbStatus)) {
                String error = (String) metrics.get("database_error");
                sendAlert("DATABASE_DOWN", "数据库连接失败: " + error, "CRITICAL");
            }
        }

        /**
         * 发送告警
         */
        private void sendAlert(String alertType, String message, String severity) {
            try {
                // 检查告警冷却时间
                String cooldownKey = ALERT_COOLDOWN_KEY + alertType;
                if (redisCache.hasKey(cooldownKey)) {
                    log.debug("告警 {} 在冷却期内，跳过发送", alertType);
                    return;
                }
                
                // 设置冷却时间（根据严重程度设置不同的冷却时间）
                int cooldownMinutes = getCooldownMinutes(severity);
                redisCache.setCacheObject(cooldownKey, System.currentTimeMillis(), 
                                        cooldownMinutes, TimeUnit.MINUTES);
                
                // 记录告警日志
                log.error("系统告警 - 类型: {}, 严重程度: {}, 消息: {}", alertType, severity, message);
                
                // 这里可以集成实际的告警系统
                // 例如：短信、邮件、钉钉、企业微信等
                sendToAlertSystem(alertType, message, severity);
                
            } catch (Exception e) {
                log.error("发送告警失败", e);
            }
        }

        /**
         * 根据严重程度获取冷却时间
         */
        private int getCooldownMinutes(String severity) {
            switch (severity) {
                case "CRITICAL":
                    return 5;  // 严重告警5分钟冷却
                case "HIGH":
                    return 15; // 高级告警15分钟冷却
                case "MEDIUM":
                    return 30; // 中级告警30分钟冷却
                default:
                    return 60; // 默认60分钟冷却
            }
        }

        /**
         * 发送到实际的告警系统
         */
        private void sendToAlertSystem(String alertType, String message, String severity) {
            // 这里可以实现具体的告警发送逻辑
            // 例如调用短信API、邮件API等
            
            // 示例：记录到特定的告警日志文件
            log.error("ALERT|{}|{}|{}", severity, alertType, message);
            
            // 示例：如果是CRITICAL级别，可以发送短信
            if ("CRITICAL".equals(severity)) {
                // smsService.sendAlert(message);
            }
        }

        /**
         * 清理过期的监控数据
         */
        private void cleanupOldMetrics() {
            try {
                // 清理24小时前的监控数据
                long cutoffTime = System.currentTimeMillis() - 24 * 60 * 60 * 1000;
                // 这里可以实现具体的清理逻辑
                log.debug("清理监控数据，截止时间: {}", cutoffTime);
            } catch (Exception e) {
                log.error("清理监控数据失败", e);
            }
        }

        /**
         * 获取系统状态摘要
         */
        public Map<String, Object> getSystemSummary() {
            try {
                return collectSystemMetrics();
            } catch (Exception e) {
                log.error("获取系统状态摘要失败", e);
                Map<String, Object> errorSummary = new HashMap<>();
                errorSummary.put("status", "ERROR");
                errorSummary.put("error", e.getMessage());
                return errorSummary;
            }
        }
    }
}
