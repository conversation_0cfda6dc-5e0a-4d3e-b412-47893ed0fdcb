-- 检查通知功能状态的SQL脚本

-- 1. 检查persistent_notification表是否存在
SHOW TABLES LIKE '%notification%';

-- 2. 检查表结构
DESCRIBE persistent_notification;

-- 3. 检查是否有通知记录
SELECT COUNT(*) as total_notifications FROM persistent_notification;

-- 4. 检查最近的通知记录
SELECT 
    id, user_id, notification_type, title, content, status, 
    create_time, send_time, delivery_time, error_message
FROM persistent_notification 
ORDER BY create_time DESC 
LIMIT 10;

-- 5. 检查待发送的通知
SELECT 
    id, user_id, title, status, retry_count, error_message, create_time
FROM persistent_notification 
WHERE status = 'PENDING'
ORDER BY create_time DESC;

-- 6. 检查失败的通知
SELECT 
    id, user_id, title, status, retry_count, error_message, create_time
FROM persistent_notification 
WHERE status = 'FAILED'
ORDER BY create_time DESC;

-- 7. 检查用户表中的用户信息
SELECT user_id, user_name, nick_name, phonenumber, status 
FROM sys_user 
WHERE status = '0' 
ORDER BY user_id 
LIMIT 5;

-- 8. 检查会诊申请表
SELECT 
    id, request_no, requester_id, consultant_id, 
    requester_name, consultant_name, patient_name, 
    status, create_time
FROM consultation_request 
ORDER BY create_time DESC 
LIMIT 5;

-- 9. 检查系统配置
SELECT config_key, config_value 
FROM sys_config 
WHERE config_key LIKE '%notification%';
