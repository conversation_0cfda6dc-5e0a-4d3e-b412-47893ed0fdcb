# AI报告解析功能部署指南

## 概述

本功能集成了阿里云百炼智能体API，为移动端用户提供医学影像报告的智能解读服务。该功能基于 `pacs_patient_study` 表中的 `see`（影像所见）和 `report_diagnose`（影像意见）字段进行AI分析。

## 部署步骤

### 1. 数据库初始化

执行以下SQL脚本创建AI报告解析表：

```bash
mysql -u your_username -p your_database < sql/ai_report_analysis.sql
```

### 2. 配置百炼API

#### 2.1 获取API密钥和创建智能体应用
1. 访问 [阿里云百炼控制台](https://dashscope.console.aliyun.com/)
2. 登录并获取API密钥
3. 创建智能体应用，获取应用ID

#### 2.2 修改配置文件
在 `ruoyi-admin/src/main/resources/application.yml` 中配置：

```yaml
# 百炼AI配置
bailian:
  api:
    key: sk-your-actual-api-key-here  # 替换为您的实际API密钥
  app-id: YOUR_ACTUAL_APP_ID          # 替换为您的实际应用ID
  model: qwen-plus                    # 可选：qwen-plus, qwen-max, qwen-turbo（保留兼容性）
  max-tokens: 2000                    # 最大token数（保留兼容性）
  temperature: 0.7                    # 温度参数 (0.0-2.0)（保留兼容性）
```

### 3. 依赖检查

确认 `ruoyi-admin/pom.xml` 中已包含百炼SDK依赖：

```xml
<dependency>
    <groupId>com.alibaba</groupId>
    <artifactId>dashscope-sdk-java</artifactId>
    <version>2.18.3</version>
    <exclusions>
        <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-simple</artifactId>
        </exclusion>
    </exclusions>
</dependency>
```

### 4. 前端部署

前端代码已自动集成，无需额外配置。

### 5. 重启服务

```bash
# 后端
cd ruoyi-admin
mvn clean package
java -jar target/ruoyi-admin.jar

# 前端
cd pacs-mobile
npm install
npm run build
```

## 功能验证

### 1. 后端API测试

```bash
# 获取AI解析结果（studyId为检查记录ID）
curl -X GET "http://localhost:8080/mobile/ai/report/analysis/1" \
  -H "Authorization: Bearer your-token"

# 检查解析状态
curl -X GET "http://localhost:8080/mobile/ai/report/analysis/status/1" \
  -H "Authorization: Bearer your-token"
```

### 2. 前端功能测试

1. 登录移动端应用
2. 进入任意报告详情页
3. 点击"AI报告解析"按钮
4. 验证页面跳转和功能正常

## 故障排除

### 1. API密钥或应用ID错误
**症状**: 提示"调用百炼API失败"
**解决**: 检查配置文件中的API密钥和应用ID是否正确

### 2. 网络连接问题
**症状**: 连接超时或网络错误
**解决**: 检查服务器网络连接，确保能访问阿里云服务

### 3. 数据库连接问题
**症状**: 无法保存解析结果
**解决**: 检查数据库连接和表结构是否正确

### 4. 前端路由问题
**症状**: 点击按钮无响应或页面404
**解决**: 检查路由配置和组件导入

## 监控和维护

### 1. 日志监控
查看应用日志中的AI分析相关信息：
```bash
tail -f logs/ruoyi-admin.log | grep "AI报告解析"
```

### 2. 数据库监控
定期检查AI解析表的数据量和性能：
```sql
-- 查看解析统计
SELECT 
    analysis_status,
    COUNT(*) as count,
    AVG(TIMESTAMPDIFF(SECOND, create_time, analysis_time)) as avg_duration
FROM ai_report_analysis 
GROUP BY analysis_status;

-- 清理过期数据
DELETE FROM ai_report_analysis 
WHERE expire_time < NOW() AND analysis_status = 'completed';
```

### 3. API使用量监控
在阿里云控制台监控API调用量和费用。

## 安全注意事项

1. **API密钥安全**: 不要将API密钥提交到版本控制系统
2. **访问控制**: 确保只有授权用户能访问AI分析功能
3. **数据隐私**: 确保患者数据的隐私和安全
4. **免责声明**: 确保用户了解AI分析的局限性

## 性能优化建议

1. **缓存策略**: 合理设置解析结果的过期时间
2. **并发控制**: 限制同时进行的AI分析任务数量
3. **资源监控**: 监控服务器资源使用情况
4. **成本控制**: 监控API调用费用，设置预算告警

## 技术支持

如遇到问题，请：
1. 查看详细的错误日志
2. 检查配置是否正确
3. 验证网络连接
4. 联系技术支持团队

## 更新日志

- **v1.0.0** (2025-06-15): 初始版本发布
  - 集成百炼API
  - 实现流式响应
  - 添加缓存机制
  - 完善错误处理
