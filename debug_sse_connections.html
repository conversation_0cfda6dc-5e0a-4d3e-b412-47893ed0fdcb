<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSE连接调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            border: 1px solid #ddd;
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px 0;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>SSE连接调试工具</h1>
    
    <div class="section">
        <h3>1. 获取Token和用户信息</h3>
        <button onclick="getTokenAndUserInfo()">自动获取Token和用户信息</button>
        <div>
            <label>Token:</label>
            <input type="text" id="token" readonly>
        </div>
        <div>
            <label>当前用户ID:</label>
            <input type="text" id="currentUserId" readonly>
        </div>
        <div>
            <label>当前用户名:</label>
            <input type="text" id="currentUserName" readonly>
        </div>
    </div>

    <div class="section">
        <h3>2. 检查在线用户</h3>
        <button onclick="checkOnlineUsers()">检查在线用户</button>
        <div id="onlineUsersStatus" class="status">等待检查...</div>
        <div id="onlineUsersList"></div>
    </div>

    <div class="section">
        <h3>3. 建立SSE连接</h3>
        <button onclick="connectSSE()">建立连接</button>
        <button onclick="disconnectSSE()">断开连接</button>
        <div id="sseStatus" class="status">未连接</div>
        <div class="log" id="sseLog"></div>
    </div>

    <div class="section">
        <h3>4. 手动发送通知测试</h3>
        <div>
            <label>目标用户ID:</label>
            <input type="number" id="targetUserId" placeholder="输入要发送通知的用户ID">
        </div>
        <button onclick="sendTestNotification()">发送测试通知</button>
        <div id="testStatus" class="status">等待发送...</div>
    </div>

    <div class="section">
        <h3>5. 检查会诊申请</h3>
        <button onclick="checkConsultationRequests()">检查最近的会诊申请</button>
        <div id="consultationList"></div>
    </div>

    <script>
        let eventSource = null;
        let currentUserId = null;

        function log(message) {
            const logDiv = document.getElementById('sseLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(elementId, success, message) {
            const element = document.getElementById(elementId);
            element.className = `status ${success ? 'success' : 'error'}`;
            element.textContent = message;
        }

        async function getTokenAndUserInfo() {
            // 从Cookie获取Token
            const cookies = document.cookie.split(';');
            let token = null;
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'Admin-Token') {
                    token = value;
                    break;
                }
            }

            if (!token) {
                alert('未找到Token，请先登录系统');
                return;
            }

            document.getElementById('token').value = token;

            // 获取用户信息
            try {
                const response = await fetch('/dev-api/system/user/profile', {
                    headers: {
                        'Authorization': 'Bearer ' + token
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.data && data.data.user) {
                        currentUserId = data.data.user.userId;
                        document.getElementById('currentUserId').value = currentUserId;
                        document.getElementById('currentUserName').value = data.data.user.userName;
                        document.getElementById('targetUserId').value = currentUserId; // 默认发送给自己
                        console.log('用户信息获取成功:', data.data.user);
                    }
                } else {
                    alert('获取用户信息失败');
                }
            } catch (error) {
                alert('获取用户信息异常: ' + error.message);
            }
        }

        async function checkOnlineUsers() {
            const token = document.getElementById('token').value;
            if (!token) {
                alert('请先获取Token');
                return;
            }

            try {
                const response = await fetch('/dev-api/consultation/reliable-notification/online-users', {
                    headers: {
                        'Authorization': 'Bearer ' + token
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    const onlineUsers = data.data.onlineUsers || [];
                    
                    updateStatus('onlineUsersStatus', true, `当前在线用户数: ${onlineUsers.length}`);
                    
                    if (onlineUsers.length > 0) {
                        const userList = onlineUsers.map(userId => `用户ID: ${userId}`).join('<br>');
                        document.getElementById('onlineUsersList').innerHTML = `<strong>在线用户:</strong><br>${userList}`;
                    } else {
                        document.getElementById('onlineUsersList').innerHTML = '<strong>当前没有在线用户</strong>';
                    }
                } else {
                    updateStatus('onlineUsersStatus', false, '检查失败');
                }
            } catch (error) {
                updateStatus('onlineUsersStatus', false, '检查异常: ' + error.message);
            }
        }

        function connectSSE() {
            const token = document.getElementById('token').value;
            if (!token) {
                alert('请先获取Token');
                return;
            }

            if (eventSource) {
                eventSource.close();
            }

            const url = `/dev-api/consultation/reliable-notification/connect?token=${encodeURIComponent(token)}`;
            log(`建立SSE连接: ${url}`);

            eventSource = new EventSource(url, {
                withCredentials: true
            });

            eventSource.onopen = function(event) {
                updateStatus('sseStatus', true, '连接已建立');
                log('SSE连接已打开');
            };

            eventSource.addEventListener('connected', function(event) {
                log(`连接确认: ${event.data}`);
            });

            eventSource.addEventListener('consultation-notification', function(event) {
                log(`收到会诊通知: ${event.data}`);
                
                // 显示弹窗
                const notification = JSON.parse(event.data);
                alert(`收到通知!\n标题: ${notification.title}\n内容: ${notification.content}`);
            });

            eventSource.addEventListener('heartbeat', function(event) {
                log(`心跳: ${event.data}`);
            });

            eventSource.onerror = function(event) {
                updateStatus('sseStatus', false, '连接错误');
                log(`连接错误: ${JSON.stringify(event)}`);
            };
        }

        function disconnectSSE() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
                updateStatus('sseStatus', false, '连接已断开');
                log('连接已断开');
            }
        }

        async function sendTestNotification() {
            const token = document.getElementById('token').value;
            const targetUserId = document.getElementById('targetUserId').value;

            if (!token || !targetUserId) {
                alert('请先获取Token并输入目标用户ID');
                return;
            }

            try {
                const response = await fetch('/dev-api/consultation/reliable-notification/send-custom', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + token
                    },
                    body: JSON.stringify({
                        userId: parseInt(targetUserId),
                        title: '测试通知',
                        content: `这是发送给用户${targetUserId}的测试通知`,
                        type: 'TEST'
                    })
                });

                if (response.ok) {
                    updateStatus('testStatus', true, '测试通知发送成功');
                    log(`测试通知已发送给用户${targetUserId}`);
                } else {
                    const errorText = await response.text();
                    updateStatus('testStatus', false, '发送失败: ' + errorText);
                }
            } catch (error) {
                updateStatus('testStatus', false, '发送异常: ' + error.message);
            }
        }

        async function checkConsultationRequests() {
            const token = document.getElementById('token').value;
            if (!token) {
                alert('请先获取Token');
                return;
            }

            try {
                const response = await fetch('/dev-api/consultation/request/list?pageNum=1&pageSize=5', {
                    headers: {
                        'Authorization': 'Bearer ' + token
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    const requests = data.rows || [];
                    
                    if (requests.length > 0) {
                        let html = '<table><tr><th>申请编号</th><th>申请人</th><th>受邀者</th><th>受邀者ID</th><th>状态</th><th>创建时间</th></tr>';
                        requests.forEach(req => {
                            html += `<tr>
                                <td>${req.requestNo}</td>
                                <td>${req.requesterName}</td>
                                <td>${req.consultantName}</td>
                                <td>${req.consultantId}</td>
                                <td>${req.status}</td>
                                <td>${req.createTime}</td>
                            </tr>`;
                        });
                        html += '</table>';
                        document.getElementById('consultationList').innerHTML = html;
                    } else {
                        document.getElementById('consultationList').innerHTML = '没有找到会诊申请';
                    }
                } else {
                    document.getElementById('consultationList').innerHTML = '查询失败';
                }
            } catch (error) {
                document.getElementById('consultationList').innerHTML = '查询异常: ' + error.message;
            }
        }

        // 页面加载时自动获取用户信息
        window.addEventListener('load', function() {
            getTokenAndUserInfo();
        });
    </script>
</body>
</html>
