package com.ruoyi.framework.web.service;

import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.StringUtils;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.JwtException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.RedisConnectionFailureException;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.net.ConnectException;
import java.sql.SQLException;
import java.util.concurrent.TimeUnit;

/**
 * 增强的Token服务 - 添加详细的异常处理和监控
 */
@Component
@Slf4j
public class EnhancedTokenService extends TokenService {

    @Autowired
    private RedisCache redisCache;

    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long RETRY_DELAY_MS = 100;
    private static final String LOGIN_ERROR_COUNT_KEY = "login_error_count:";
    private static final String SYSTEM_HEALTH_KEY = "system_health_check";

    /**
     * 增强的获取用户身份信息方法
     * 添加重试机制和详细的异常处理
     */
    @Override
    public LoginUser getLoginUser(HttpServletRequest request) {
        String token = getToken(request);
        if (StringUtils.isEmpty(token)) {
            log.debug("请求中未找到Token");
            return null;
        }

        String clientIp = getClientIP(request);
        String userAgent = request.getHeader("User-Agent");
        
        // 记录请求信息用于问题排查
        log.debug("获取用户信息 - IP: {}, UserAgent: {}, Token前缀: {}", 
                 clientIp, userAgent, token.substring(0, Math.min(token.length(), 10)) + "...");

        // 添加重试机制处理临时性异常
        for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {
            try {
                return attemptGetLoginUser(token, attempt, clientIp);
            } catch (ExpiredJwtException e) {
                log.warn("Token已过期 - IP: {}, 错误: {}", clientIp, e.getMessage());
                recordLoginError(clientIp, "TOKEN_EXPIRED", e.getMessage());
                return null;
            } catch (JwtException e) {
                log.error("Token解析失败 - IP: {}, 尝试次数: {}, 错误: {}", clientIp, attempt, e.getMessage());
                recordLoginError(clientIp, "TOKEN_PARSE_ERROR", e.getMessage());
                return null;
            } catch (RedisConnectionFailureException e) {
                log.error("Redis连接失败 - IP: {}, 尝试次数: {}, 错误: {}", clientIp, attempt, e.getMessage());
                recordLoginError(clientIp, "REDIS_CONNECTION_ERROR", e.getMessage());
                
                if (attempt < MAX_RETRY_ATTEMPTS) {
                    waitBeforeRetry(attempt);
                } else {
                    // 触发系统告警
                    triggerSystemAlert("Redis连接持续失败", e.getMessage());
                    throw new ServiceException("系统暂时不可用，请稍后重试");
                }
            } catch (Exception e) {
                log.error("获取用户信息异常 - IP: {}, 尝试次数: {}, 错误: {}", clientIp, attempt, e.getMessage(), e);
                recordLoginError(clientIp, "UNKNOWN_ERROR", e.getMessage());
                
                if (attempt < MAX_RETRY_ATTEMPTS) {
                    waitBeforeRetry(attempt);
                } else {
                    log.error("获取用户信息最终失败 - IP: {}, Token: {}", clientIp, 
                             token.substring(0, Math.min(token.length(), 20)) + "...");
                    throw new ServiceException("登录验证失败，请重新登录");
                }
            }
        }

        return null;
    }

    /**
     * 尝试获取登录用户信息
     */
    private LoginUser attemptGetLoginUser(String token, int attempt, String clientIp) {
        Claims claims = parseToken(token);
        String uuid = (String) claims.get(Constants.LOGIN_USER_KEY);
        String userKey = getTokenKey(uuid);

        // 检查Redis连接状态
        if (attempt == 1) {
            checkRedisHealth();
        }

        LoginUser user = redisCache.getCacheObject(userKey);
        if (user == null) {
            log.warn("Token对应的用户信息不存在 - IP: {}, UUID: {}, 尝试次数: {}", clientIp, uuid, attempt);
            
            // 检查是否是Token过期导致的
            if (!redisCache.hasKey(userKey)) {
                throw new ExpiredJwtException(null, null, "用户会话已过期");
            }
        } else {
            // 记录成功获取用户信息
            log.debug("成功获取用户信息 - 用户: {}, IP: {}", user.getUsername(), clientIp);
        }

        return user;
    }

    /**
     * 检查Redis健康状态
     */
    private void checkRedisHealth() {
        try {
            redisCache.setCacheObject(SYSTEM_HEALTH_KEY, System.currentTimeMillis(), 60, TimeUnit.SECONDS);
            Long healthCheck = redisCache.getCacheObject(SYSTEM_HEALTH_KEY);
            if (healthCheck == null) {
                throw new ServiceException("Redis健康检查失败");
            }
        } catch (Exception e) {
            log.error("Redis健康检查失败", e);
            throw new RedisConnectionFailureException("Redis连接异常", e);
        }
    }

    /**
     * 记录登录错误信息
     */
    private void recordLoginError(String clientIp, String errorType, String errorMessage) {
        try {
            String errorKey = LOGIN_ERROR_COUNT_KEY + clientIp + ":" + errorType;
            Long errorCount = redisCache.getCacheObject(errorKey);
            if (errorCount == null) {
                errorCount = 0L;
            }
            errorCount++;
            
            // 错误计数，1小时过期
            redisCache.setCacheObject(errorKey, errorCount, 3600, TimeUnit.SECONDS);
            
            // 如果错误次数过多，触发告警
            if (errorCount >= 10) {
                triggerSystemAlert("登录错误频繁", 
                    String.format("IP: %s, 错误类型: %s, 错误次数: %d, 最新错误: %s", 
                                 clientIp, errorType, errorCount, errorMessage));
            }
            
        } catch (Exception e) {
            log.error("记录登录错误失败", e);
        }
    }

    /**
     * 触发系统告警
     */
    private void triggerSystemAlert(String alertType, String alertMessage) {
        try {
            // 这里可以集成短信、邮件或其他告警系统
            log.error("系统告警 - 类型: {}, 消息: {}", alertType, alertMessage);
            
            // 可以发送到监控系统或告警平台
            // alertService.sendAlert(alertType, alertMessage);
            
        } catch (Exception e) {
            log.error("发送系统告警失败", e);
        }
    }

    /**
     * 重试前等待
     */
    private void waitBeforeRetry(int attempt) {
        try {
            long delay = RETRY_DELAY_MS * attempt;
            Thread.sleep(delay);
        } catch (InterruptedException ie) {
            Thread.currentThread().interrupt();
            throw new ServiceException("操作被中断");
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIP(HttpServletRequest request) {
        String xip = request.getHeader("X-Real-IP");
        String xfor = request.getHeader("X-Forwarded-For");
        if (StringUtils.isNotEmpty(xfor) && !"unKnown".equalsIgnoreCase(xfor)) {
            int index = xfor.indexOf(",");
            if (index != -1) {
                return xfor.substring(0, index);
            } else {
                return xfor;
            }
        }
        if (StringUtils.isNotEmpty(xip) && !"unKnown".equalsIgnoreCase(xip)) {
            return xip;
        }
        return request.getRemoteAddr();
    }

    /**
     * 增强的Token创建方法
     */
    @Override
    public String createToken(LoginUser loginUser) {
        try {
            String token = super.createToken(loginUser);
            
            // 记录Token创建成功
            log.info("Token创建成功 - 用户: {}, UUID: {}", 
                    loginUser.getUsername(), loginUser.getToken());
            
            return token;
            
        } catch (Exception e) {
            log.error("Token创建失败 - 用户: {}, 错误: {}", loginUser.getUsername(), e.getMessage(), e);
            throw new ServiceException("登录失败，请重试");
        }
    }

    /**
     * 增强的Token删除方法
     */
    @Override
    public void delLoginUser(String token) {
        try {
            super.delLoginUser(token);
            log.info("Token删除成功 - Token: {}", token.substring(0, Math.min(token.length(), 10)) + "...");
        } catch (Exception e) {
            log.error("Token删除失败 - Token: {}, 错误: {}", 
                     token.substring(0, Math.min(token.length(), 10)) + "...", e.getMessage(), e);
        }
    }

    /**
     * 获取系统健康状态
     */
    public boolean isSystemHealthy() {
        try {
            // 检查Redis
            checkRedisHealth();
            
            // 检查Token服务基本功能
            String testKey = "health_check_" + System.currentTimeMillis();
            redisCache.setCacheObject(testKey, "test", 10, TimeUnit.SECONDS);
            String testValue = redisCache.getCacheObject(testKey);
            redisCache.deleteObject(testKey);
            
            return "test".equals(testValue);
            
        } catch (Exception e) {
            log.error("系统健康检查失败", e);
            return false;
        }
    }
}
