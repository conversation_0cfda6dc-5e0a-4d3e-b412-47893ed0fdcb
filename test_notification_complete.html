<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完整通知功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px 0;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .test-results {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
    </style>
</head>
<body>
    <h1>完整通知功能测试工具</h1>
    
    <div class="test-section">
        <h3>1. 基础配置</h3>
        <div class="form-group">
            <label for="token">认证Token:</label>
            <input type="text" id="token" placeholder="从浏览器Cookie中获取Admin-Token">
        </div>
        <div class="form-group">
            <label for="userId">用户ID:</label>
            <input type="number" id="userId" placeholder="当前登录用户的ID">
        </div>
        <button onclick="getTokenFromCookie()">自动获取Token</button>
    </div>

    <div class="test-section">
        <h3>2. API连通性测试</h3>
        <button onclick="testApiConnectivity()">测试API连通性</button>
        <div id="apiStatus" class="status">等待测试...</div>
    </div>

    <div class="test-section">
        <h3>3. SSE连接测试</h3>
        <button onclick="testSSEConnection()">建立SSE连接</button>
        <button onclick="disconnectSSE()">断开连接</button>
        <div id="sseStatus" class="status">未连接</div>
        <div class="log" id="sseLog"></div>
    </div>

    <div class="test-section">
        <h3>4. 数据库测试</h3>
        <button onclick="testDatabaseTables()">检查数据库表</button>
        <button onclick="testNotificationRecords()">检查通知记录</button>
        <div id="dbStatus" class="status">等待测试...</div>
    </div>

    <div class="test-section">
        <h3>5. 手动发送测试通知</h3>
        <div class="form-group">
            <label for="testTitle">通知标题:</label>
            <input type="text" id="testTitle" value="测试通知">
        </div>
        <div class="form-group">
            <label for="testContent">通知内容:</label>
            <textarea id="testContent" rows="3">这是一个测试通知，用于验证通知功能是否正常工作。</textarea>
        </div>
        <button onclick="sendTestNotification()">发送测试通知</button>
        <div id="testStatus" class="status">等待发送...</div>
    </div>

    <div class="test-results">
        <div class="test-section">
            <h3>6. 实时日志</h3>
            <div class="log" id="mainLog"></div>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div class="test-section">
            <h3>7. 测试结果</h3>
            <div id="testResults">
                <p>请依次执行上述测试步骤...</p>
            </div>
        </div>
    </div>

    <script>
        let eventSource = null;
        let testResults = {
            api: false,
            sse: false,
            database: false,
            notification: false
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('mainLog');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            logDiv.innerHTML += `[${timestamp}] ${prefix} ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function sseLog(message) {
            const logDiv = document.getElementById('sseLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(elementId, success, message) {
            const element = document.getElementById(elementId);
            element.className = `status ${success ? 'success' : 'error'}`;
            element.textContent = message;
        }

        function getTokenFromCookie() {
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'Admin-Token') {
                    document.getElementById('token').value = value;
                    log('从Cookie中获取到Token', 'success');
                    return;
                }
            }
            log('未在Cookie中找到Admin-Token', 'error');
        }

        async function testApiConnectivity() {
            log('开始测试API连通性...');
            
            try {
                // 测试基础API
                const response = await fetch('/dev-api/system/user/profile', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + document.getElementById('token').value
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    testResults.api = true;
                    updateStatus('apiStatus', true, 'API连接正常');
                    log('API连接测试成功', 'success');
                    
                    // 自动填充用户ID
                    if (data.data && data.data.user && data.data.user.userId) {
                        document.getElementById('userId').value = data.data.user.userId;
                        log(`自动获取用户ID: ${data.data.user.userId}`, 'success');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                testResults.api = false;
                updateStatus('apiStatus', false, `API连接失败: ${error.message}`);
                log(`API连接测试失败: ${error.message}`, 'error');
            }
            
            updateTestResults();
        }

        function testSSEConnection() {
            const token = document.getElementById('token').value;
            if (!token) {
                log('请先输入Token', 'error');
                return;
            }

            if (eventSource) {
                eventSource.close();
            }

            const url = `/dev-api/consultation/reliable-notification/connect?token=${encodeURIComponent(token)}`;
            log(`尝试建立SSE连接: ${url}`);
            sseLog(`连接URL: ${url}`);

            try {
                eventSource = new EventSource(url, {
                    withCredentials: true
                });

                eventSource.onopen = function(event) {
                    testResults.sse = true;
                    updateStatus('sseStatus', true, 'SSE连接已建立');
                    log('SSE连接建立成功', 'success');
                    sseLog('连接已打开');
                    updateTestResults();
                };

                eventSource.addEventListener('connected', function(event) {
                    sseLog(`收到连接确认: ${event.data}`);
                });

                eventSource.addEventListener('consultation-notification', function(event) {
                    sseLog(`收到会诊通知: ${event.data}`);
                    log('收到会诊通知！', 'success');
                    
                    // 显示通知弹窗
                    try {
                        const notification = JSON.parse(event.data);
                        showNotificationPopup(notification);
                    } catch (e) {
                        log(`解析通知数据失败: ${e.message}`, 'error');
                    }
                });

                eventSource.addEventListener('heartbeat', function(event) {
                    sseLog(`心跳: ${event.data}`);
                });

                eventSource.onerror = function(event) {
                    testResults.sse = false;
                    updateStatus('sseStatus', false, 'SSE连接错误');
                    log('SSE连接发生错误', 'error');
                    sseLog(`连接错误: ${JSON.stringify(event)}`);
                    updateTestResults();
                };

            } catch (error) {
                testResults.sse = false;
                updateStatus('sseStatus', false, `创建SSE连接失败: ${error.message}`);
                log(`创建SSE连接失败: ${error.message}`, 'error');
                updateTestResults();
            }
        }

        function disconnectSSE() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
                updateStatus('sseStatus', false, '连接已断开');
                log('SSE连接已断开');
                sseLog('连接已断开');
            }
        }

        async function testDatabaseTables() {
            log('开始检查数据库表...');
            
            try {
                const response = await fetch('/dev-api/consultation/reliable-notification/statistics', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + document.getElementById('token').value
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    testResults.database = true;
                    updateStatus('dbStatus', true, '数据库表检查正常');
                    log('数据库表检查成功', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                testResults.database = false;
                updateStatus('dbStatus', false, `数据库检查失败: ${error.message}`);
                log(`数据库检查失败: ${error.message}`, 'error');
            }
            
            updateTestResults();
        }

        async function testNotificationRecords() {
            log('开始检查通知记录...');
            
            try {
                const response = await fetch('/dev-api/consultation/reliable-notification/list', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + document.getElementById('token').value
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    log(`查询到 ${data.total || 0} 条通知记录`, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                log(`查询通知记录失败: ${error.message}`, 'error');
            }
        }

        async function sendTestNotification() {
            const title = document.getElementById('testTitle').value;
            const content = document.getElementById('testContent').value;
            const userId = document.getElementById('userId').value;

            if (!title || !content || !userId) {
                log('请填写完整的测试信息', 'error');
                return;
            }

            log('开始发送测试通知...');

            try {
                const response = await fetch('/dev-api/consultation/reliable-notification/send-custom', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + document.getElementById('token').value
                    },
                    body: JSON.stringify({
                        userId: parseInt(userId),
                        title: title,
                        content: content,
                        type: 'TEST'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    testResults.notification = true;
                    updateStatus('testStatus', true, '测试通知发送成功');
                    log('测试通知发送成功', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                testResults.notification = false;
                updateStatus('testStatus', false, `发送失败: ${error.message}`);
                log(`发送测试通知失败: ${error.message}`, 'error');
            }

            updateTestResults();
        }

        function showNotificationPopup(notification) {
            // 创建通知弹窗
            const popup = document.createElement('div');
            popup.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: white;
                border: 2px solid #007bff;
                border-radius: 8px;
                padding: 15px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 10000;
                max-width: 300px;
                animation: slideIn 0.3s ease-out;
            `;
            
            popup.innerHTML = `
                <div style="font-weight: bold; margin-bottom: 8px;">${notification.title}</div>
                <div style="margin-bottom: 10px;">${notification.content}</div>
                <button onclick="this.parentElement.remove()" style="float: right;">关闭</button>
            `;
            
            document.body.appendChild(popup);
            
            // 5秒后自动关闭
            setTimeout(() => {
                if (popup.parentElement) {
                    popup.remove();
                }
            }, 5000);
        }

        function updateTestResults() {
            const resultsDiv = document.getElementById('testResults');
            const total = Object.keys(testResults).length;
            const passed = Object.values(testResults).filter(r => r).length;
            
            resultsDiv.innerHTML = `
                <h4>测试进度: ${passed}/${total}</h4>
                <ul>
                    <li>API连通性: ${testResults.api ? '✅ 通过' : '❌ 失败'}</li>
                    <li>SSE连接: ${testResults.sse ? '✅ 通过' : '❌ 失败'}</li>
                    <li>数据库检查: ${testResults.database ? '✅ 通过' : '❌ 失败'}</li>
                    <li>通知发送: ${testResults.notification ? '✅ 通过' : '❌ 失败'}</li>
                </ul>
                ${passed === total ? '<p style="color: green; font-weight: bold;">🎉 所有测试通过！</p>' : 
                  '<p style="color: orange;">⚠️ 部分测试失败，请检查上述步骤</p>'}
            `;
        }

        function clearLog() {
            document.getElementById('mainLog').innerHTML = '';
            document.getElementById('sseLog').innerHTML = '';
        }

        // 页面加载时的初始化
        window.addEventListener('load', function() {
            log('测试工具已加载，请按顺序执行测试步骤');
            getTokenFromCookie();
        });

        // 页面卸载时关闭连接
        window.addEventListener('beforeunload', function() {
            if (eventSource) {
                eventSource.close();
            }
        });
    </script>
</body>
</html>
