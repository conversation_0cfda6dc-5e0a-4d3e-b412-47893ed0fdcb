# 可靠通知组件改进说明

## 概述

本次对可靠通知组件（ReliableNotification）进行了四个主要功能的改进，并将通知显示方式从ElNotification替换为ConsultationNotificationCard，提升了系统的可靠性、用户体验和消息处理能力。

## 重要更新

### 通知显示组件替换
- **原来**: 使用Element Plus的ElNotification组件显示通知
- **现在**: 使用自定义的ConsultationNotificationCard组件显示通知
- **组件位置**: `@/components/ReliableNotification/ConsultationNotificationCard.vue`
- **优势**: 更丰富的交互功能、更好的视觉效果、支持快速操作按钮

## 改进功能

### 1. 消息去重机制

#### 功能描述
- 添加消息ID去重逻辑，防止重复消息显示
- 使用Set数据结构存储已处理的消息ID
- 自动限制Set大小，避免内存泄漏

#### 实现细节
```javascript
// 消息去重检查
const isDuplicateMessage = (notification) => {
  const messageId = notification.notificationId || notification.id
  if (!messageId) return false
  return processedMessageIds.value.has(messageId.toString())
}

// 标记消息为已处理
const markMessageAsProcessed = (notification) => {
  const messageId = notification.notificationId || notification.id
  if (messageId) {
    processedMessageIds.value.add(messageId.toString())
    
    // 限制Set大小，避免内存泄漏
    if (processedMessageIds.value.size > 1000) {
      const firstItem = processedMessageIds.value.values().next().value
      processedMessageIds.value.delete(firstItem)
    }
  }
}
```

#### 使用场景
- 网络不稳定时可能收到重复消息
- 服务端重发机制导致的消息重复
- 客户端重连后的消息重复接收

### 2. 本地存储功能

#### 功能描述
- 将未确认消息存储到localStorage
- 页面刷新后自动恢复未确认消息
- 定期清理过期的本地存储数据

#### 实现细节
```javascript
// 保存待处理通知到本地存储
const savePendingNotificationsToStorage = () => {
  try {
    const data = {
      notifications: pendingNotifications.value,
      timestamp: Date.now()
    }
    localStorage.setItem(config.localStorageKey, JSON.stringify(data))
  } catch (error) {
    console.warn('保存通知到本地存储失败:', error)
  }
}

// 从本地存储加载待处理通知
const loadPendingNotificationsFromStorage = () => {
  try {
    const stored = localStorage.getItem(config.localStorageKey)
    if (!stored) return
    
    const data = JSON.parse(stored)
    const now = Date.now()
    
    // 检查数据是否过期
    if (now - data.timestamp > config.localStorageExpiry) {
      localStorage.removeItem(config.localStorageKey)
      return
    }
    
    // 过滤掉过期的通知
    const validNotifications = data.notifications.filter(notification => {
      const notificationAge = now - (notification.timestamp || 0)
      return notificationAge < config.localStorageExpiry
    })
    
    pendingNotifications.value = validNotifications
    
    // 重新显示未确认的通知
    validNotifications.forEach(notification => {
      if (!isDuplicateMessage(notification)) {
        showNotification(notification)
        markMessageAsProcessed(notification)
      }
    })
    
    console.log('从本地存储恢复通知:', validNotifications.length, '条')
  } catch (error) {
    console.warn('从本地存储加载通知失败:', error)
    localStorage.removeItem(config.localStorageKey)
  }
}
```

#### 配置参数
- `localStorageKey`: 本地存储键名，默认为 'reliable_notifications_pending'
- `localStorageExpiry`: 本地存储过期时间，默认为24小时

### 3. 网络状态检测

#### 功能描述
- 监听网络状态变化事件
- 根据网络状态智能调整重连策略
- 网络恢复时立即尝试重连

#### 实现细节
```javascript
// 网络状态监听
const setupNetworkStatusMonitoring = () => {
  // 监听网络状态变化
  window.addEventListener('online', handleNetworkOnline)
  window.addEventListener('offline', handleNetworkOffline)
  
  // 定期检查网络状态
  networkStatusInterval.value = setInterval(() => {
    const currentOnlineStatus = navigator.onLine
    if (currentOnlineStatus !== isOnline.value) {
      isOnline.value = currentOnlineStatus
      console.log('网络状态变化:', currentOnlineStatus ? '在线' : '离线')
    }
  }, config.networkCheckInterval)
}

// 网络恢复处理
const handleNetworkOnline = () => {
  console.log('网络已恢复')
  isOnline.value = true
  
  // 如果连接断开，立即尝试重连
  if (!isConnected.value) {
    console.log('网络恢复，立即尝试重连')
    reconnectAttempts.value = 0 // 重置重连次数
    connectToNotificationService()
  }
}

// 网络断开处理
const handleNetworkOffline = () => {
  console.log('网络已断开')
  isOnline.value = false
  showConnectionStatus.value = true
}
```

#### 智能重连策略
- 网络离线时延长重连间隔（至少10秒）
- 网络恢复时立即重连并重置重连次数
- 重连前检查网络状态

### 4. 消息优先级支持

#### 功能描述
- 支持紧急消息优先处理和显示
- 不同优先级消息使用不同的显示样式和声音
- 紧急消息不自动关闭，需要用户手动确认

#### 优先级定义
```javascript
priorityLevels: {
  LOW: 0,      // 低优先级
  NORMAL: 1,   // 普通优先级
  HIGH: 2,     // 高优先级
  URGENT: 3    // 紧急优先级
}
```

#### 实现细节
```javascript
// 显示通知（支持优先级）
const showNotification = (notification) => {
  const priority = notification.priority || config.priorityLevels.NORMAL
  const isUrgent = priority >= config.priorityLevels.URGENT
  
  const notificationConfig = {
    id: notification.notificationId || Date.now(),
    title: notification.title,
    message: notification.content,
    type: getNotificationType(notification.type, priority),
    duration: isUrgent ? 0 : config.notificationTimeout, // 紧急消息不自动关闭
    onClick: () => handleNotificationClick(notification),
    onClose: () => handleNotificationClose(notification),
    customClass: getPriorityClass(priority)
  }
  
  // 使用Element Plus的通知组件
  ElNotification(notificationConfig)
  
  // 添加到活动通知列表
  activeNotifications.value.push(notificationConfig)
}
```

#### 优先级特性
- **紧急消息**: 红色边框，闪烁效果，不自动关闭，最大音量
- **高优先级**: 橙色边框，较大音量
- **普通优先级**: 蓝色边框，默认音量
- **低优先级**: 灰色边框，较小音量，半透明

#### 声音和震动
- 不同优先级使用不同的声音文件
- 不同优先级使用不同的震动模式
- 支持声音文件回退机制

## 配置参数

### 新增配置项
```javascript
// 本地存储相关
localStorageKey: 'reliable_notifications_pending',
localStorageExpiry: 24 * 60 * 60 * 1000, // 24小时

// 网络检测相关
networkCheckInterval: 5000, // 5秒

// 优先级定义
priorityLevels: {
  LOW: 0,
  NORMAL: 1,
  HIGH: 2,
  URGENT: 3
}
```

## 样式改进

### 优先级样式
- 添加了不同优先级的边框颜色和背景色
- 紧急消息添加了闪烁动画效果
- 使用深度选择器确保样式生效

### CSS类名
- `notification-urgent`: 紧急消息样式
- `notification-high`: 高优先级消息样式
- `notification-normal`: 普通消息样式
- `notification-low`: 低优先级消息样式

## 使用方法

### 基本使用
组件会自动处理所有改进功能，无需额外配置。

### 发送优先级消息
后端发送消息时需要包含priority字段：
```json
{
  "notificationId": "12345",
  "title": "紧急通知",
  "content": "有新的紧急会诊申请",
  "type": "URGENT",
  "priority": 3,
  "url": "/consultation/detail/123"
}
```

## 兼容性

- 保持与现有API的完全兼容
- 新功能为增量改进，不影响现有功能
- 支持渐进式增强，即使某些功能不可用也不影响基本功能

## 注意事项

1. **本地存储限制**: 浏览器本地存储有大小限制，建议定期清理
2. **网络检测精度**: navigator.onLine可能不够精确，建议结合实际连接测试
3. **声音文件**: 需要确保声音文件存在，否则会回退到默认声音
4. **优先级处理**: 建议后端统一优先级标准，避免混乱

## 测试建议

1. **消息去重测试**: 模拟重复消息发送
2. **本地存储测试**: 刷新页面验证消息恢复
3. **网络状态测试**: 断网重连测试
4. **优先级测试**: 发送不同优先级消息验证显示效果

## ConsultationNotificationCard集成

### 组件特性
- **丰富的视觉效果**: 支持卡片样式、阴影效果、图标显示
- **交互功能**: 支持查看详情、快速接受、关闭等操作
- **优先级显示**: 根据紧急程度显示不同的视觉样式和标签
- **自动关闭**: 在ReliableNotification中实现，根据优先级设置不同的自动关闭时间
- **会诊信息展示**: 显示申请编号、申请医生、患者姓名等详细信息

### 数据格式适配
```javascript
// 转换通知数据格式以适配ConsultationNotificationCard
const notificationConfig = {
  id: notification.notificationId || Date.now(),
  notificationId: notification.notificationId,
  title: notification.title,
  content: notification.content,
  type: notification.type,
  priority: priority,
  urgencyLevel: getUrgencyLevel(priority), // 转换为URGENT/HIGH/NORMAL/LOW
  createTime: notification.createTime || Date.now(),
  patientName: notification.patientName,
  studyDescription: notification.studyDescription,
  requestingDoctor: notification.requestingDoctor,
  url: notification.url
}
```

### 事件处理
- `@close`: 处理通知关闭事件
- `@action`: 处理快速操作（接受/拒绝等）

### 自动关闭实现
由于ConsultationNotificationCard组件本身不支持自动关闭，在ReliableNotification中实现：
```javascript
// 设置自动关闭（如果不是紧急通知）
if (!isUrgentNotification(notificationConfig)) {
  const delay = getAutoCloseDelay(notificationConfig)
  if (delay > 0) {
    setTimeout(() => {
      handleNotificationClose(notificationConfig)
    }, delay)
  }
}
```

### 样式定位
- 通知卡片固定在页面右下角
- 支持垂直滚动显示多个通知
- 响应式设计，适配不同屏幕尺寸

## 真实业务逻辑实现

### handleNotificationAction 实现
已实现完整的会诊业务逻辑处理：

#### 支持的操作类型
- `accept`: 接受会诊申请
- `reject`: 拒绝会诊申请
- `view`: 查看会诊详情

#### 接受会诊流程
```javascript
const handleAcceptConsultation = async (notification) => {
  // 1. 提取会诊申请ID
  const consultationId = extractConsultationId(notification)

  // 2. 显示确认对话框
  const confirmed = await showConfirmDialog(
    '确认接受会诊',
    `确定要接受来自 ${notification.requesterName} 的会诊申请吗？`,
    'success'
  )

  // 3. 调用后端API
  if (confirmed) {
    await acceptConsultationRequest(consultationId, {
      acceptTime: new Date().toISOString(),
      acceptRemark: '通过通知快速接受'
    })
    ElMessage.success('会诊申请已接受')
  }
}
```

#### 拒绝会诊流程
```javascript
const handleRejectConsultation = async (notification) => {
  // 1. 提取会诊申请ID
  const consultationId = extractConsultationId(notification)

  // 2. 显示拒绝原因输入对话框
  const result = await showRejectDialog(
    '拒绝会诊申请',
    `确定要拒绝来自 ${notification.requesterName} 的会诊申请吗？`
  )

  // 3. 调用后端API
  if (result.confirmed) {
    await rejectConsultationRequest(consultationId, {
      rejectTime: new Date().toISOString(),
      rejectReason: result.reason || '通过通知快速拒绝'
    })
    ElMessage.warning('会诊申请已拒绝')
  }
}
```

#### 查看详情流程
```javascript
const handleViewConsultation = async (notification) => {
  // 1. 提取会诊申请ID
  const consultationId = extractConsultationId(notification)

  // 2. 获取会诊详情并跳转
  if (consultationId) {
    const response = await getConsultationRequest(consultationId)
    window.open(`/consultation/detail/${consultationId}`, '_blank')
  } else {
    // 如果没有具体ID，跳转到会诊列表
    window.open('/consultation/my-consultation', '_blank')
  }
}
```

### ID提取策略
支持多种ID提取方式：
- `notification.consultationId`
- `notification.requestId`
- `notification.businessId`
- `notification.relatedId`
- 从URL中提取（如 `/consultation/detail/123`）

### 用户交互增强
- **确认对话框**: 使用ElMessageBox.confirm进行操作确认
- **拒绝原因输入**: 使用ElMessageBox.prompt收集拒绝原因
- **错误处理**: 完整的try-catch错误处理和用户提示
- **自动确认**: 操作完成后自动确认通知

### API集成
- 导入会诊相关API: `acceptConsultationRequest`, `rejectConsultationRequest`, `getConsultationRequest`
- 使用标准的RESTful API调用
- 支持异步操作和错误处理

## 后续优化建议

1. 添加消息统计和分析功能
2. 支持消息分组和批量操作
3. 添加消息搜索和过滤功能
4. 支持自定义通知模板
5. 增强ConsultationNotificationCard的自定义配置选项
6. 添加通知卡片的拖拽排序功能
7. 添加操作日志记录功能
8. 支持批量操作（批量接受/拒绝）
9. 添加操作撤销功能
10. 支持自定义快捷操作按钮
