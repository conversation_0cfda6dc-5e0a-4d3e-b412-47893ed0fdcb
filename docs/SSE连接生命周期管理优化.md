# SSE连接生命周期管理优化

## 问题背景

原始设计中，在`onCompletion`事件中立即移除连接可能导致以下问题：

1. **用户体验问题** - 页面刷新时连接被立即清理，用户重连前可能错过通知
2. **竞争条件** - 重连和清理操作可能产生竞争条件
3. **过度敏感** - 临时网络问题也会导致连接被永久移除

## 优化方案

### 1. 分层的连接清理策略

```java
// 连接事件处理策略
onCompletion() -> 标记为待清理（给予宽限期）
onTimeout()    -> 立即清理（真正的超时）
onError()      -> 立即清理（连接错误）
```

### 2. 连接状态管理

引入连接清理标记机制：

```java
// 连接清理标记 - 记录标记为待清理的连接及其原因
private static final Map<Long, String> connectionCleanupMarks = new ConcurrentHashMap<>();

// 标记连接为待清理状态
private void markConnectionForCleanup(Long userId, String reason) {
    connectionCleanupMarks.put(userId, reason);
    log.info("标记用户 {} 的连接为待清理状态，原因: {}", userId, reason);
}
```

### 3. 智能清理逻辑

```java
// 清理决策逻辑
if (connectionCleanupMarks.containsKey(userId)) {
    // 给被标记的连接30秒宽限期
    if ((currentTime - lastActive) > 30000) {
        shouldCleanup = true;
        cleanupReason = "标记清理: " + markReason;
    }
} else if (heartbeatTimeout) {
    // 心跳超时立即清理
    shouldCleanup = true;
    cleanupReason = "心跳超时";
}
```

### 4. 连接恢复机制

```java
// 在心跳响应中检查连接恢复
public void handleHeartbeatResponse(Long userId) {
    // 如果连接被标记为待清理，现在收到心跳响应，说明连接恢复正常
    if (connectionCleanupMarks.containsKey(userId)) {
        log.info("用户 {} 连接恢复正常，移除清理标记", userId);
        connectionCleanupMarks.remove(userId);
    }
}
```

## 连接生命周期

### 1. 连接建立阶段

```
用户请求连接 -> 创建SseEmitter -> 注册事件处理器 -> 发送连接确认
```

### 2. 连接活跃阶段

```
定期发送心跳 -> 用户响应心跳 -> 更新活跃时间 -> 发送业务通知
```

### 3. 连接异常阶段

```
检测到异常 -> 标记为待清理 -> 给予宽限期 -> 检查是否恢复 -> 决定是否清理
```

### 4. 连接清理阶段

```
满足清理条件 -> 发送清理通知 -> 完成连接 -> 清理相关状态
```

## 事件处理策略对比

### 原始策略（问题较多）

| 事件 | 处理方式 | 问题 |
|------|----------|------|
| onCompletion | 立即移除连接 | 页面刷新时过度敏感 |
| onTimeout | 立即移除连接 | 合理 |
| onError | 立即移除连接 | 合理 |

### 优化策略（更加智能）

| 事件 | 处理方式 | 优势 |
|------|----------|------|
| onCompletion | 标记待清理，给予宽限期 | 允许快速重连 |
| onTimeout | 立即移除连接 | 处理真正的超时 |
| onError | 立即移除连接 | 处理连接错误 |

## 配置参数

```java
// 连接超时时间（30分钟）
private static final long SSE_TIMEOUT = 30 * 60 * 1000L;

// 心跳间隔（30秒）
private static final long HEARTBEAT_INTERVAL = 30 * 1000L;

// 心跳超时时间（90秒，允许3次心跳失败）
private static final long HEARTBEAT_TIMEOUT = 90 * 1000L;

// 连接清理间隔（60秒）
private static final long CONNECTION_CLEANUP_INTERVAL = 60 * 1000L;

// 待清理连接宽限期（30秒）
private static final long CLEANUP_GRACE_PERIOD = 30 * 1000L;
```

## 监控和调试

### 1. 连接状态监控

```java
public Map<String, Object> getConnectionStatistics() {
    Map<String, Object> stats = new HashMap<>();
    stats.put("totalConnections", sseConnections.size());
    stats.put("markedForCleanup", connectionCleanupMarks.size());
    stats.put("activeConnections", getActiveConnectionCount());
    stats.put("timeoutConnections", getTimeoutConnectionCount());
    return stats;
}
```

### 2. 日志记录

```java
// 连接建立
log.info("用户 {} 的SSE连接创建成功", userId);

// 连接标记
log.info("标记用户 {} 的连接为待清理状态，原因: {}", userId, reason);

// 连接恢复
log.info("用户 {} 连接恢复正常，移除清理标记", userId);

// 连接清理
log.warn("清理用户 {} 的连接，原因: {}", userId, cleanupReason);
```

## 最佳实践

### 1. 连接管理

- **避免过度清理** - 不要在每个事件中都立即清理连接
- **给予宽限期** - 允许用户在短时间内重新连接
- **智能恢复** - 检测连接恢复并移除清理标记

### 2. 错误处理

- **区分错误类型** - 不同类型的错误采用不同的处理策略
- **记录详细日志** - 便于问题排查和性能优化
- **优雅降级** - 连接失败时提供备用通知方式

### 3. 性能优化

- **批量操作** - 在清理任务中批量处理多个连接
- **异步处理** - 避免阻塞主线程
- **资源回收** - 及时清理不再使用的资源

## 测试验证

### 1. 正常场景测试

```bash
# 建立连接
curl -H "Authorization: Bearer TOKEN" /dev-api/test/sse/user-info

# 发送通知
curl -X POST -H "Authorization: Bearer TOKEN" \
     -d '{"title":"测试","content":"消息"}' \
     /dev-api/test/sse/send-to-self
```

### 2. 异常场景测试

```bash
# 模拟页面刷新（连接完成）
# 观察是否给予宽限期而不是立即清理

# 模拟网络中断
# 观察心跳超时后的清理行为

# 模拟快速重连
# 观察清理标记是否被正确移除
```

### 3. 压力测试

```bash
# 大量并发连接
# 频繁连接断开重连
# 长时间运行稳定性测试
```

## 总结

通过这次优化，我们实现了：

1. **更智能的连接管理** - 区分不同类型的连接事件
2. **更好的用户体验** - 减少因页面刷新导致的通知丢失
3. **更强的容错能力** - 处理临时网络问题和快速重连
4. **更完善的监控** - 提供详细的连接状态信息

这种设计既保证了系统的稳定性，又提供了更好的用户体验。
