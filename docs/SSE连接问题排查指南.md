# SSE连接问题排查指南

## 问题描述

在测试过程中发现，调用`SseConnectionService.sendNotificationToUser`方法时，`emitter`对象为null，导致无法发送通知。

## 问题原因分析

### 1. 可能的原因

1. **用户未建立SSE连接**
   - 用户没有通过`/consultation/reliable-notification/connect`端点建立SSE连接
   - 前端没有正确调用连接API

2. **用户ID不匹配**
   - 传递给`sendNotificationToUser`方法的`userId`与建立连接时的`userId`不一致
   - 用户认证信息有问题

3. **连接已断开**
   - SSE连接因为超时、网络错误等原因被清理
   - 心跳机制失效导致连接被误删

4. **测试环境问题**
   - 在测试环境中没有正确模拟用户认证
   - Spring Security上下文缺失

## 排查步骤

### 步骤1: 检查用户认证状态

```bash
# 1. 检查当前用户是否已登录
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8080/dev-api/test/sse/user-info

# 2. 查看返回的用户信息
{
  "code": 200,
  "data": {
    "userId": 1,
    "username": "admin",
    "isConnected": false,
    "totalConnections": 0,
    "onlineUsers": []
  }
}
```

### 步骤2: 建立SSE连接

```javascript
// 前端建立SSE连接
const token = 'YOUR_TOKEN';
const url = `/dev-api/consultation/reliable-notification/connect?token=${encodeURIComponent('Bearer ' + token)}`;

const eventSource = new EventSource(url, {
    withCredentials: true
});

eventSource.onopen = function(event) {
    console.log('SSE连接已建立');
};

eventSource.addEventListener('connected', function(event) {
    console.log('连接确认:', event.data);
});
```

### 步骤3: 验证连接状态

```bash
# 检查连接统计信息
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8080/dev-api/test/sse/connection-stats

# 预期返回
{
  "code": 200,
  "data": {
    "totalConnections": 1,
    "onlineUsers": [1],
    "activeConnections": 1,
    "timeoutConnections": 0
  }
}
```

### 步骤4: 测试通知发送

```bash
# 发送测试通知给自己
curl -X POST \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"title":"测试通知","content":"这是一条测试消息"}' \
     http://localhost:8080/dev-api/test/sse/send-to-self

# 发送通知给指定用户
curl -X POST \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"title":"测试通知","content":"这是一条测试消息"}' \
     http://localhost:8080/dev-api/test/sse/send-to-user/1
```

## 调试工具使用

### 1. 使用Web调试工具

访问 `http://localhost:8080/sse_debug.html` 使用可视化调试工具：

1. **输入Token** - 从浏览器开发者工具中获取Bearer Token
2. **获取用户信息** - 确认当前登录用户
3. **建立SSE连接** - 测试连接建立过程
4. **发送测试通知** - 验证通知发送功能

### 2. 查看日志输出

在应用日志中查找以下关键信息：

```log
# 连接建立日志
[INFO] 用户 1 请求建立SSE连接
[INFO] 用户 1 的SSE连接创建成功

# 通知发送日志
[INFO] 尝试向用户 1 发送通知，当前连接数: 1
[INFO] 当前在线用户列表: [1]
[INFO] 向用户 1 发送通知成功

# 错误日志
[WARN] 用户 1 未建立SSE连接，无法发送通知。当前在线用户: []
```

## 常见问题解决

### 问题1: Token认证失败

**现象**: 获取用户信息时返回401错误

**解决方案**:
```bash
# 1. 重新登录获取新Token
curl -X POST \
     -H "Content-Type: application/json" \
     -d '{"username":"admin","password":"admin123"}' \
     http://localhost:8080/dev-api/auth/login

# 2. 使用返回的token
```

### 问题2: SSE连接建立失败

**现象**: EventSource连接失败或立即断开

**解决方案**:
1. 检查Token格式是否正确
2. 确认服务器端口和路径
3. 检查CORS配置
4. 查看浏览器控制台错误信息

### 问题3: 用户ID不匹配

**现象**: 连接建立成功但发送通知失败

**解决方案**:
```java
// 在代码中添加调试日志
log.info("建立连接的用户ID: {}", userId);
log.info("发送通知的目标用户ID: {}", targetUserId);
log.info("当前在线用户列表: {}", sseConnections.keySet());
```

### 问题4: 连接被意外清理

**现象**: 连接建立后很快断开

**解决方案**:
1. 检查心跳机制配置
2. 调整超时时间设置
3. 确认网络稳定性

## 测试脚本

### 完整测试流程

```bash
#!/bin/bash

# 1. 登录获取Token
TOKEN=$(curl -s -X POST \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}' \
  http://localhost:8080/dev-api/auth/login | \
  jq -r '.token')

echo "Token: $TOKEN"

# 2. 获取用户信息
curl -H "Authorization: Bearer $TOKEN" \
     http://localhost:8080/dev-api/test/sse/user-info

# 3. 模拟用户上线
curl -X POST \
     -H "Authorization: Bearer $TOKEN" \
     http://localhost:8080/dev-api/test/sse/simulate-online/1

# 4. 发送测试通知
curl -X POST \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"title":"测试通知","content":"这是一条测试消息"}' \
     http://localhost:8080/dev-api/test/sse/send-to-self

# 5. 检查连接统计
curl -H "Authorization: Bearer $TOKEN" \
     http://localhost:8080/dev-api/test/sse/connection-stats
```

## 代码修改建议

### 1. 增强错误处理

```java
public boolean sendNotificationToUser(Long userId, Object message) {
    // 参数验证
    if (userId == null) {
        log.error("用户ID不能为null");
        return false;
    }
    
    if (message == null) {
        log.error("消息内容不能为null");
        return false;
    }
    
    // 详细的调试日志
    log.info("尝试向用户 {} 发送通知，当前连接数: {}", userId, sseConnections.size());
    log.info("当前在线用户列表: {}", sseConnections.keySet());
    
    SseEmitter emitter = sseConnections.get(userId);
    if (emitter == null) {
        log.warn("用户 {} 未建立SSE连接，无法发送通知。当前在线用户: {}", userId, sseConnections.keySet());
        return false;
    }
    
    // 其余代码...
}
```

### 2. 添加连接验证

```java
public boolean isConnectionValid(Long userId) {
    SseEmitter emitter = sseConnections.get(userId);
    if (emitter == null) {
        return false;
    }
    
    try {
        // 发送测试消息验证连接
        emitter.send(SseEmitter.event()
                .name("connection-test")
                .data("test"));
        return true;
    } catch (IOException e) {
        log.warn("连接验证失败，移除用户 {} 的连接", userId);
        removeConnection(userId);
        return false;
    }
}
```

## 总结

通过以上排查步骤和调试工具，可以有效定位和解决SSE连接相关问题。关键是要：

1. **确保用户认证正确** - Token有效且用户已登录
2. **正确建立SSE连接** - 通过正确的端点和参数
3. **验证连接状态** - 确认用户在连接映射中存在
4. **监控日志输出** - 及时发现和处理异常情况
5. **使用调试工具** - 利用提供的调试接口和页面

如果问题仍然存在，请提供详细的日志信息和错误堆栈，以便进一步分析。
