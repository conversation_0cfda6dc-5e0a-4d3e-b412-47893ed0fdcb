# SSE连接管理机制说明

## 概述

本文档说明了SSE连接服务中的简化管理机制，采用永不过期的SseEmitter配合服务端定时清理的策略。

## 设计原理

### 1. 永不过期连接

创建SseEmitter时设置超时时间为0，表示永不过期：

```java
// 创建永不过期的SSE连接
private static final long SSE_TIMEOUT = 0L; // 永不过期
SseEmitter emitter = new SseEmitter(SSE_TIMEOUT);
```

### 2. 服务端主导清理

通过定时任务检查客户端活跃状态，主动清理无效连接：

```java
// 检查心跳响应超时
Long lastResponse = heartbeatLastResponse.get(userId);
if (lastResponse == null || (currentTime - lastResponse) > HEARTBEAT_TIMEOUT) {
    shouldCleanup = true;
    cleanupReason = "心跳超时";
}
```

## 简化的连接管理

### 1. 简化的心跳响应处理

```java
public void handleHeartbeatResponse(Long userId) {
    if (!sseConnections.containsKey(userId)) {
        log.debug("用户 {} 的SSE连接不存在，无法处理心跳响应", userId);
        return;
    }

    long currentTime = System.currentTimeMillis();

    // 更新心跳响应时间和活跃时间
    heartbeatLastResponse.put(userId, currentTime);
    connectionLastActive.put(userId, currentTime);

    // 如果连接被标记为待清理，现在收到心跳响应，说明连接恢复正常，移除清理标记
    if (connectionCleanupMarks.containsKey(userId)) {
        log.info("用户 {} 连接恢复正常，移除清理标记", userId);
        connectionCleanupMarks.remove(userId);
    }

    log.debug("收到用户 {} 的心跳响应，连接保持活跃", userId);
}
```

### 2. 永不过期的连接创建

```java
public SseEmitter createConnection(Long userId) {
    log.info("为用户 {} 创建SSE连接（永不过期）", userId);

    // 创建永不过期的SSE连接
    SseEmitter emitter = new SseEmitter(SSE_TIMEOUT); // SSE_TIMEOUT = 0L

    // 只处理必要的事件
    emitter.onCompletion(() -> {
        log.info("用户 {} 的SSE连接已完成", userId);
        markConnectionForCleanup(userId, "COMPLETED");
    });

    emitter.onError((throwable) -> {
        log.error("用户 {} 的SSE连接发生错误", userId, throwable);
        removeConnection(userId);
    });

    // ... 其他初始化逻辑
}
```

## 简化设计的优势

### 1. 设计简单

- **无需续期**: SseEmitter永不过期，避免复杂的续期逻辑
- **服务端主导**: 完全由服务端定时任务控制连接生命周期
- **逻辑清晰**: 心跳响应只需更新时间戳，逻辑简单明了

### 2. 性能优化

- **减少开销**: 不需要发送心跳确认消息，减少网络开销
- **简化处理**: 心跳响应处理逻辑简单，性能更好
- **内存友好**: 减少不必要的数据结构和处理逻辑

### 3. 可靠性提升

- **避免超时**: 永不过期避免了SseEmitter自身的超时问题
- **统一管理**: 所有连接清理都由统一的定时任务处理
- **状态一致**: 连接状态完全由服务端控制，避免客户端和服务端状态不一致

## 配置参数

```java
// SSE连接超时（永不过期）
private static final long SSE_TIMEOUT = 0L;

// 心跳间隔（30秒）
private static final long HEARTBEAT_INTERVAL = 30 * 1000L;

// 心跳超时时间（90秒，允许3次心跳失败）
private static final long HEARTBEAT_TIMEOUT = 90 * 1000L;

// 连接清理间隔（60秒）
private static final long CONNECTION_CLEANUP_INTERVAL = 60 * 1000L;
```

## 连接管理流程图

```
客户端心跳响应
       ↓
检查连接是否存在
       ↓
更新心跳响应时间戳
       ↓
更新连接活跃时间戳
       ↓
移除清理标记 (如果存在)
       ↓
处理完成

服务端定时清理任务
       ↓
检查所有连接的心跳超时
       ↓
清理超时的连接
       ↓
清理完成
```

## 使用示例

### 客户端心跳响应

```javascript
// 客户端收到心跳后响应（简化版）
eventSource.addEventListener('heartbeat', function(event) {
    const heartbeatData = JSON.parse(event.data);

    // 发送心跳响应
    fetch('/dev-api/consultation/sse/heartbeat', {
        method: 'POST',
        headers: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            timestamp: heartbeatData.timestamp,
            clientTime: Date.now()
        })
    }).catch(error => {
        console.warn('心跳响应发送失败:', error);
    });
});
```

### 服务端调用

```java
// 处理客户端心跳响应（简化版）
@PostMapping("/heartbeat")
public AjaxResult handleHeartbeat() {
    Long userId = SecurityUtils.getUserId();
    sseConnectionService.handleHeartbeatResponse(userId);
    return AjaxResult.success("心跳响应已记录");
}
```

## 监控和调试

### 1. 连接状态查询

```java
// 获取连接健康状态
Map<String, Object> health = sseConnectionService.getConnectionHealth();
```

### 2. 连接状态验证

```bash
# 查看心跳响应日志
grep "收到用户.*的心跳响应" logs/ruoyi-admin.log

# 查看连接清理日志
grep "清理用户.*的连接" logs/ruoyi-admin.log

# 查看连接恢复日志
grep "连接恢复正常" logs/ruoyi-admin.log
```

## 总结

通过简化的连接管理机制，我们实现了：

1. **简单可靠的设计** - 永不过期的SseEmitter配合服务端定时清理
2. **高效的性能表现** - 减少不必要的网络通信和复杂逻辑
3. **统一的状态管理** - 完全由服务端控制连接生命周期
4. **良好的可维护性** - 逻辑简单清晰，易于理解和维护

这种设计更加简洁高效，避免了复杂的续期逻辑，同时保证了连接的稳定性和系统的可靠性。
