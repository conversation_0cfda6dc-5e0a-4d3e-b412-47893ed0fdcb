# SSE连接续期机制说明

## 概述

本文档说明了SSE连接服务中的续期机制，特别是`handleHeartbeatResponse`方法如何为关联的SSE对象续期。

## 续期机制原理

### 1. 时间戳续期

由于Spring的`SseEmitter`一旦创建后超时时间无法动态修改，我们通过维护时间戳来实现逻辑上的续期：

```java
// 更新心跳响应时间 - 这是续期的核心机制
Long previousResponse = heartbeatLastResponse.put(userId, currentTime);
connectionLastActive.put(userId, currentTime);
```

### 2. 续期判断逻辑

清理任务会检查这些时间戳来决定是否清理连接：

```java
// 检查心跳响应超时
Long lastResponse = heartbeatLastResponse.get(userId);
if (lastResponse == null || (currentTime - lastResponse) > HEARTBEAT_TIMEOUT) {
    shouldCleanup = true;
    cleanupReason = "心跳超时";
}
```

## 改进的续期功能

### 1. 增强的心跳响应处理

```java
public void handleHeartbeatResponse(Long userId) {
    SseEmitter emitter = sseConnections.get(userId);
    if (emitter == null) {
        log.debug("用户 {} 的SSE连接不存在，无法处理心跳响应", userId);
        return;
    }

    long currentTime = System.currentTimeMillis();
    
    // 更新心跳响应时间 - 续期核心
    Long previousResponse = heartbeatLastResponse.put(userId, currentTime);
    connectionLastActive.put(userId, currentTime);
    
    // 连接恢复处理
    if (connectionCleanupMarks.containsKey(userId)) {
        log.info("用户 {} 连接恢复正常，移除清理标记", userId);
        connectionCleanupMarks.remove(userId);
    }
    
    // 续期效果监控
    if (previousResponse != null) {
        long timeSinceLastResponse = currentTime - previousResponse;
        log.debug("用户 {} 心跳响应续期成功，距离上次响应: {}ms", userId, timeSinceLastResponse);
        
        // 网络问题预警
        if (timeSinceLastResponse > HEARTBEAT_TIMEOUT / 2) {
            log.warn("用户 {} 心跳响应间隔较长: {}ms，可能存在网络问题", userId, timeSinceLastResponse);
        }
    }
    
    // 发送续期确认
    try {
        emitter.send(SseEmitter.event()
                .name("heartbeat-ack")
                .data(createHeartbeatAckData(currentTime)));
    } catch (IOException e) {
        log.warn("向用户 {} 发送心跳确认失败，连接可能已断开", userId);
        markConnectionForCleanup(userId, "HEARTBEAT_ACK_FAILED");
    }
}
```

### 2. 心跳确认数据

```java
private Map<String, Object> createHeartbeatAckData(long timestamp) {
    Map<String, Object> ackData = new HashMap<>();
    ackData.put("timestamp", timestamp);
    ackData.put("type", "heartbeat-ack");
    ackData.put("message", "心跳响应已收到，连接已续期");
    ackData.put("nextHeartbeatExpected", timestamp + HEARTBEAT_INTERVAL);
    return ackData;
}
```

## 续期机制的优势

### 1. 智能续期

- **自动续期**: 每次收到心跳响应都会自动续期
- **状态恢复**: 被标记为待清理的连接可以通过心跳响应恢复
- **网络监控**: 监控心跳间隔，及时发现网络问题

### 2. 容错处理

- **连接验证**: 通过发送心跳确认验证连接有效性
- **异常处理**: 发送确认失败时标记为待清理而不是立即断开
- **宽限期机制**: 给予连接恢复的时间

### 3. 监控和调试

- **详细日志**: 记录续期过程和异常情况
- **性能监控**: 跟踪心跳响应间隔
- **状态透明**: 提供连接健康状态查询

## 配置参数

```java
// 心跳间隔（30秒）
private static final long HEARTBEAT_INTERVAL = 30 * 1000L;

// 心跳超时时间（90秒，允许3次心跳失败）
private static final long HEARTBEAT_TIMEOUT = 90 * 1000L;

// SSE连接超时（30分钟）
private static final long SSE_TIMEOUT = 30 * 60 * 1000L;
```

## 续期流程图

```
客户端心跳响应
       ↓
检查连接是否存在
       ↓
更新心跳响应时间戳 (续期核心)
       ↓
更新连接活跃时间戳
       ↓
移除清理标记 (如果存在)
       ↓
计算响应间隔并监控
       ↓
发送心跳确认
       ↓
续期完成
```

## 使用示例

### 客户端心跳响应

```javascript
// 客户端收到心跳后响应
eventSource.addEventListener('heartbeat', function(event) {
    const heartbeatData = JSON.parse(event.data);
    
    // 发送心跳响应
    fetch('/dev-api/consultation/sse/heartbeat', {
        method: 'POST',
        headers: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            timestamp: heartbeatData.timestamp,
            clientTime: Date.now()
        })
    });
});

// 监听心跳确认
eventSource.addEventListener('heartbeat-ack', function(event) {
    const ackData = JSON.parse(event.data);
    console.log('连接已续期:', ackData.message);
    console.log('下次心跳预期时间:', new Date(ackData.nextHeartbeatExpected));
});
```

### 服务端调用

```java
// 处理客户端心跳响应
@PostMapping("/heartbeat")
public AjaxResult handleHeartbeat() {
    Long userId = SecurityUtils.getUserId();
    sseConnectionService.handleHeartbeatResponse(userId);
    return AjaxResult.success("心跳响应处理成功");
}
```

## 监控和调试

### 1. 连接状态查询

```java
// 获取连接健康状态
Map<String, Object> health = sseConnectionService.getConnectionHealth();
```

### 2. 续期效果验证

```bash
# 查看续期日志
grep "心跳响应续期成功" logs/ruoyi-admin.log

# 查看网络问题预警
grep "心跳响应间隔较长" logs/ruoyi-admin.log
```

## 总结

通过改进的续期机制，我们实现了：

1. **有效的连接续期** - 通过时间戳更新实现逻辑续期
2. **智能的状态管理** - 自动恢复被标记的连接
3. **完善的监控机制** - 实时跟踪续期效果和网络状态
4. **良好的容错能力** - 处理各种异常情况

这种设计既保证了连接的稳定性，又提供了良好的用户体验和系统可维护性。
