#!/bin/bash

# 查找项目中的Java可执行文件
JAVA_HOME=$(find /Library/Java/JavaVirtualMachines -name "java" -type f | head -1 | xargs dirname | xargs dirname)

if [ -z "$JAVA_HOME" ]; then
  echo "未找到Java环境，请确保已安装JDK"
  exit 1
fi

echo "使用Java环境: $JAVA_HOME"

# 设置Java环境变量
export JAVA_HOME
export PATH=$JAVA_HOME/bin:$PATH

# 编译测试类
echo "编译测试类..."
javac -cp ruoyi-common/src/main/java ruoyi-common/src/main/java/com/ruoyi/common/utils/sms/VerifyCodeTest.java

# 运行测试类
echo "运行测试..."
java -cp ruoyi-common/src/main/java com.ruoyi.common.utils.sms.VerifyCodeTest

echo "测试完成"
