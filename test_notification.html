<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>会诊通知功能测试</h1>
    
    <div class="form-group">
        <label for="token">认证Token:</label>
        <input type="text" id="token" placeholder="请输入JWT Token">
        <small>从浏览器开发者工具的Application -> Cookies中获取Admin-Token</small>
    </div>
    
    <div class="form-group">
        <button onclick="connectSSE()">建立SSE连接</button>
        <button onclick="disconnectSSE()">断开连接</button>
        <button onclick="clearLog()">清空日志</button>
    </div>
    
    <div id="status" class="status disconnected">
        状态: 未连接
    </div>
    
    <h3>连接日志:</h3>
    <div id="log" class="log"></div>
    
    <h3>测试说明:</h3>
    <ol>
        <li>首先在浏览器中登录PACS系统</li>
        <li>打开开发者工具，在Application -> Cookies中找到Admin-Token的值</li>
        <li>将Token值粘贴到上面的输入框中</li>
        <li>点击"建立SSE连接"按钮</li>
        <li>在另一个浏览器标签页中创建会诊申请</li>
        <li>观察此页面是否收到通知</li>
    </ol>

    <script>
        let eventSource = null;
        let isConnected = false;

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(connected, message) {
            const statusDiv = document.getElementById('status');
            isConnected = connected;
            
            if (connected) {
                statusDiv.className = 'status connected';
                statusDiv.textContent = `状态: 已连接 - ${message}`;
            } else {
                statusDiv.className = 'status disconnected';
                statusDiv.textContent = `状态: 未连接 - ${message}`;
            }
        }

        function connectSSE() {
            const token = document.getElementById('token').value.trim();
            
            if (!token) {
                alert('请先输入Token');
                return;
            }

            if (eventSource) {
                eventSource.close();
            }

            const url = `/dev-api/consultation/reliable-notification/connect?token=${encodeURIComponent(token)}`;
            log(`尝试连接SSE: ${url}`);

            try {
                eventSource = new EventSource(url, {
                    withCredentials: true
                });

                eventSource.onopen = function(event) {
                    log('SSE连接已打开');
                    updateStatus(true, '连接成功');
                };

                eventSource.addEventListener('connected', function(event) {
                    log(`收到连接确认: ${event.data}`);
                });

                eventSource.addEventListener('consultation-notification', function(event) {
                    log(`收到会诊通知: ${event.data}`);
                    try {
                        const notification = JSON.parse(event.data);
                        alert(`收到通知: ${notification.title}\n${notification.content}`);
                    } catch (e) {
                        log(`解析通知数据失败: ${e.message}`);
                    }
                });

                eventSource.addEventListener('consultation-broadcast', function(event) {
                    log(`收到广播通知: ${event.data}`);
                });

                eventSource.addEventListener('heartbeat', function(event) {
                    log(`收到心跳: ${event.data}`);
                });

                eventSource.addEventListener('offline-notifications', function(event) {
                    log(`收到离线通知: ${event.data}`);
                });

                eventSource.onerror = function(event) {
                    log(`SSE连接错误: ${JSON.stringify(event)}`);
                    updateStatus(false, '连接错误');
                    
                    if (eventSource.readyState === EventSource.CLOSED) {
                        log('连接已关闭');
                    }
                };

            } catch (error) {
                log(`创建SSE连接失败: ${error.message}`);
                updateStatus(false, '创建连接失败');
            }
        }

        function disconnectSSE() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
                log('手动断开SSE连接');
                updateStatus(false, '手动断开');
            }
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 页面卸载时关闭连接
        window.addEventListener('beforeunload', function() {
            if (eventSource) {
                eventSource.close();
            }
        });
    </script>
</body>
</html>
